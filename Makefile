.PHONY: build run test infra

# Include the .env file
include .env
export

ENV = development
MIGRATION_DIR=./migration
MIGRATION_DB_URL=postgresql://postgres:password@localhost:5432/price_list?sslmode=disable

default: build

dependencies:
	@go install github.com/air-verse/air@latest
	@go install go.uber.org/mock/mockgen@latest
	@go get -v -d ./...

test:
	@go clean -testcache
	@go test -v -cover -tags 'integration' ./... # -tags '!integration'

serve:
	@air 	

generate-code:
	# find . -name "mock_*" -type f -exec rm -rf {} +
	@go generate ./...

get-linter:
	command -v golangci-lint || curl -sfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b ${GOPATH}/bin

lint:
	@golangci-lint --timeout 5m --verbose --config .golangci.yaml run --exclude-use-default=false

build:
	@go build -o bin/server ./cmd/api

run: build
	@./bin/server

create-migration:
	@if [ -z "$(NAME)" ]; then \
		echo "Please provide the NAME parameter. e.g make create-migration NAME=create_user_table"; \
	else \
		echo "Creating migration files"; \
		touch ./migration/`date -u +"%Y%m%d%H%M%S"`_$(NAME).up.sql; \
		touch ./migration/`date -u +"%Y%m%d%H%M%S"`_$(NAME).down.sql; \
	fi

get-migrate:
	@command -v migrate >/dev/null 2>&1 || go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest

migrate-up: get-migrate
	migrate -path $(MIGRATION_DIR) -database $(MIGRATION_DB_URL) up

migrate-down: get-migrate
	migrate -path $(MIGRATION_DIR) -database $(MIGRATION_DB_URL) down

migrate-down-last: get-migrate
	migrate -path $(MIGRATION_DIR) -database $(MIGRATION_DB_URL) down 1

infra:
	@sudo docker compose up -d

down:
	sudo docker compose down

reset: down infra migrate-up

swagger:
	@echo "🚀 Starting Swagger UI..."
	@sudo docker compose up swagger-ui -d
	@echo "📖 Swagger UI available at: http://localhost:8080"
	@echo "📝 Viewing: api/swagger.yaml"
