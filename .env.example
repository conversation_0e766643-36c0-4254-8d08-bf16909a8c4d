# Application Settings
APP_NAME=price-list
ENVIRONMENT=local
LOG_LEVEL=debug
HOST=localhost
PORT=:8080

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/price_list?sslmode=disable

# Congnito Configuration
# Access keys
AWS_COGNITO_IAM_R4B_ACCESS_KEY='your access key'
AWS_COGNITO_IAM_R4B_SECRET_KEY='your secret key'

# User
AWS_COGNITO_PATIENT_USER_POOL_ID='user pool id'
AWS_COGNITO_PATIENT_USER_REGION=eu-north-1
AWS_COGNITO_R4B_IGNITE_APP_CLIENT_ID='user client id'

CACHE_URL=redis://localhost:6379
CACHE_PASSWORD=password

# AWS SQS Configuration
AWS_SQS_REGION=eu-north-1
AWS_SQS_ACCESS_KEY=test-access-key
AWS_SQS_SECRET_KEY=test-secret-key
AWS_SQS_QUEUE_URL_PRICE_LIST='https://sqs.eu-north-1.amazonaws.com/123456789012/test-queue'
AWS_SQS_QUEUE_URL_AREA='https://sqs.eu-north-1.amazonaws.com/123456789012/test-queue'
AWS_SQS_QUEUE_URL_FIELD='https://sqs.eu-north-1.amazonaws.com/123456789012/test-queue'
AWS_SQS_QUEUE_URL_NATURE='https://sqs.eu-north-1.amazonaws.com/123456789012/test-queue'
AWS_SQS_QUEUE_URL_BRANCH_AREA='https://sqs.eu-north-1.amazonaws.com/123456789012/test-queue'
AWS_SQS_QUEUE_URL_CLINICIAN_STATUS='https://sqs.eu-north-1.amazonaws.com/123456789012/test-queue'
AWS_SQS_QUEUE_URL_APPOINTMENT_TYPE='https://sqs.eu-north-1.amazonaws.com/123456789012/test-queue'

# Security Configuration
CORS_ALLOW_ORIGINS=your-origin-list separate by commas
CORS_ALLOW_METHODS=your-method-list separate by commas
CORS_ALLOW_HEADERS=your-allowed headers-list separate by commas
CORS_ALLOW_CREDENTIALS=true
MAX_BODY_SIZE=Size in bytes
RATE_LIMIT_PER_SECOND=20

# JWT Configuration
JWT_SECRET_KEY=your-secret-key

# AWS Cognito Configuration
AWS_COGNITO_CLIENT_ID=your_access_key
AWS_COGNITO_CLIENT_SECRET=your_secret_key
AWS_COGNITO_REGION=your-aws-region
AWS_COGNITO_USER_POOL_ID=your_user_pool_id