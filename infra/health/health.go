package health

import (
	"context"
	"net/http"

	"github.com/labstack/echo/v4"
)

type DB interface {
	Ping() error
}

type healthChecker struct {
	db DB
}

type HealthCheckerResponse struct {
	HttpStatus int    `json:"status"`
	Database   string `json:"database"`
}

func NewHealthChecker(db DB) *healthChecker {
	return &healthChecker{
		db: db,
	}
}

func (h *healthChecker) Handle() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		check := h.Check(ctx)
		return c.JSON(check.HttpStatus, check)
	}
}

const (
	StatusOK    = http.StatusOK
	StatusError = http.StatusInternalServerError
)

func (h *healthChecker) Check(ctx context.Context) HealthCheckerResponse {
	resp := HealthCheckerResponse{
		HttpStatus: StatusOK,
		Database:   "connected",
	}

	if err := h.checkDatabase(); err != nil {
		resp.HttpStatus = StatusError
		resp.Database = err.Error()
	}

	return resp
}

func (h *healthChecker) checkDatabase() error {
	if err := h.db.Ping(); err != nil {
		return err
	}
	return nil
}
