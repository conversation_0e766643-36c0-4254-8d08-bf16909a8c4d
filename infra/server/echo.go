package server

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sort"
	"strings"
	"syscall"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/infra/server/route"
	accountBranchAreaInternal "gitlab.viswalslab.com/backend/price-list/internal/account_branch_area"
	accountBranchAreaEvent "gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/event"
	appointmentTypeInternal "gitlab.viswalslab.com/backend/price-list/internal/appointment_type"
	appointmentTypeEvent "gitlab.viswalslab.com/backend/price-list/internal/appointment_type/event"
	areaInternal "gitlab.viswalslab.com/backend/price-list/internal/area"
	areaEvent "gitlab.viswalslab.com/backend/price-list/internal/area/event"
	clinicianStatusInternal "gitlab.viswalslab.com/backend/price-list/internal/clinician_status"
	clinicianStatusEvent "gitlab.viswalslab.com/backend/price-list/internal/clinician_status/event"
	fieldInternal "gitlab.viswalslab.com/backend/price-list/internal/field"
	fieldEvent "gitlab.viswalslab.com/backend/price-list/internal/field/event"
	"gitlab.viswalslab.com/backend/price-list/internal/nature"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/transport"
	priceListEvent "gitlab.viswalslab.com/backend/price-list/internal/price_list/event"
	"gitlab.viswalslab.com/backend/standard/v2/auth"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

const timeout = time.Second * 10

// ApplicationRunner is the main entry point for the application.
type ApplicationRunner interface {
	Run()
}

// AppBuilder represents a builder for creating an application.
// It allows setting different dependencies such as router, database, gRPC,
// file manager, and configuration through its methods.
// Finally, the Build method returns an ApplicationRunner interface implementation.
type AppBuilder struct {
	ctx                            context.Context
	cfg                            config.Configuration
	db                             *sqlx.DB
	router                         *echo.Echo
	baseGroup                      *echo.Group
	logger                         vlog.Logger
	ac                             auth.Config
	natureEventListener            *transport.NatureEventListener
	areaEventListener              *areaEvent.AreaEventListener
	accountBranchAreaEventListener *accountBranchAreaEvent.AccountBranchAreaEventListener
	clinicianStatusEventListener   *clinicianStatusEvent.ClinicianStatusEventListener
	appointmentTypeEventListener   *appointmentTypeEvent.AppointmentTypeEventListener
	fieldEventListener             *fieldEvent.FieldEventListener
	priceListEventListener         *priceListEvent.PriceListEventListener
}

// NewBuilder returns new instance of AppBuilder.
func NewBuilder() *AppBuilder {
	return &AppBuilder{}
}

// WithContext sets the context for the application.
func (app *AppBuilder) WithContext(ctx context.Context) *AppBuilder {
	app.ctx = ctx
	return app
}

// WithConfiguration sets the configuration for the application.
func (app *AppBuilder) WithConfiguration(cfg config.Configuration) *AppBuilder {
	app.cfg = cfg
	return app
}

// WithDatabase sets the database for the application.
func (app *AppBuilder) WithDatabase(db *sqlx.DB) *AppBuilder {
	app.db = db
	return app
}

// WithRouter sets the router for the application.
func (app *AppBuilder) WithRouter(echoRouter *echo.Echo) *AppBuilder {
	app.router = echoRouter
	return app
}

// WithBaseGroup sets the base group for the application.
func (app *AppBuilder) WithBaseGroup(group *echo.Group) *AppBuilder {
	app.baseGroup = group
	return app
}

// WithLogger sets the base group for the application.
func (app *AppBuilder) WithLogger(logger vlog.Logger) *AppBuilder {
	if logger == nil {
		logger = vlog.New()
	}

	app.logger = logger
	return app
}

func (app *AppBuilder) WithAuth(config auth.Config) *AppBuilder {
	app.ac = config
	return app
}

// Build finalizes the building process and returns the ApplicationRunner instance.
func (app *AppBuilder) Build() (ApplicationRunner, error) {
	if app.ctx == nil {
		app.ctx = context.Background()
	}

	if app.logger == nil {
		vlog.New().Fatal("logger is missing")
	}

	if app.router == nil {
		app.logger.Fatal("router is missing")
	}

	if app.db == nil {
		app.logger.Fatal("database is missing")
	}

	return app, nil
}

func (app *AppBuilder) PrintRoutes() {
	routes := app.router.Routes()
	sort.Slice(routes, func(i, j int) bool {
		return routes[i].Path < routes[j].Path
	})

	fmt.Printf("\nServer Routes:")
	for _, r := range routes {
		fmt.Printf("\n%s\t %s", r.Method, r.Path)
	}
	fmt.Printf("\n")
}

func (app *AppBuilder) startSubscribers() {
	// Initialize Nature SQS subscriber service
	if app.cfg.AWS.SQSQueueURLNature != "" {
		sqlxDB := sqlx.NewDb(app.db.DB, "postgres")
		natureFactory, err := nature.NewFactory(sqlxDB, app.cfg)
		if err != nil {
			app.logger.Error("failed to initialize nature factory", vlog.F("error", err))
			return
		}

		app.natureEventListener = natureFactory.NatureEventListener
		app.logger.Info("Nature event listener initialized successfully")
	} else {
		app.logger.Info("Nature SQS configuration not provided, skipping Nature event listener initialization")
	}

	// Initialize Area SQS subscriber service
	if app.cfg.AWS.SQSQueueURLArea != "" {
		sqlxDB := sqlx.NewDb(app.db.DB, "postgres")
		areaFactory, err := areaInternal.NewFactory(sqlxDB, app.cfg, app.logger)
		if err != nil {
			app.logger.Error("failed to initialize area factory", vlog.F("error", err))
			return
		}

		app.areaEventListener = areaFactory.AreaEventListener
		app.logger.Info("Area event listener initialized successfully")
	} else {
		app.logger.Info("Area SQS configuration not provided, skipping Area event listener initialization")
	}

	// Initialize Account Branch Area SQS subscriber service
	if app.cfg.AWS.SQSQueueURLBranchArea != "" {
		sqlxDB := sqlx.NewDb(app.db.DB, "postgres")
		accountBranchAreaFactory, err := accountBranchAreaInternal.NewFactory(sqlxDB, app.cfg)
		if err != nil {
			app.logger.Error("failed to initialize branch area factory", vlog.F("error", err))
			return
		}

		app.accountBranchAreaEventListener = accountBranchAreaFactory.AccountBranchAreaEventListener
		app.logger.Info("Account Branch Area event listener initialized successfully")
	} else {
		app.logger.Info("Account Branch Area SQS configuration not provided, skipping Account Branch Area event listener initialization")
	}

	// Initialize Clinician Status SQS subscriber service
	if app.cfg.AWS.SQSQueueURLClinicianStatus != "" {
		sqlxDB := sqlx.NewDb(app.db.DB, "postgres")
		clinicianStatusFactory, err := clinicianStatusInternal.NewFactory(sqlxDB, app.cfg)
		if err != nil {
			app.logger.Error("failed to initialize clinician status factory", vlog.F("error", err))
			return
		}

		app.clinicianStatusEventListener = clinicianStatusFactory.ClinicianStatusEventListener
		app.logger.Info("Clinician Status event listener initialized successfully")
	} else {
		app.logger.Info("Clinician Status SQS configuration not provided, skipping Clinician Status event listener initialization")
	}

	// Initialize Appointment Type SQS subscriber service
	if app.cfg.AWS.SQSQueueURLAppointmentType != "" {
		sqlxDB := sqlx.NewDb(app.db.DB, "postgres")
		appointmentTypeFactory, err := appointmentTypeInternal.NewFactory(sqlxDB, app.cfg)
		if err != nil {
			app.logger.Error("failed to initialize appointment type factory", vlog.F("error", err))
			return
		}

		app.appointmentTypeEventListener = appointmentTypeFactory.AppointmentTypeEventListener
		app.logger.Info("Appointment Type event listener initialized successfully")
	} else {
		app.logger.Info("Appointment Type SQS configuration not provided, skipping Appointment Type event listener initialization")
	}

	// Initialize Field SQS subscriber service
	if app.cfg.AWS.SQSQueueURLField != "" {
		sqlxDB := sqlx.NewDb(app.db.DB, "postgres")
		fieldFactory, err := fieldInternal.NewFactory(sqlxDB, app.cfg)
		if err != nil {
			app.logger.Error("failed to initialize field factory", vlog.F("error", err))
			return
		}

		app.fieldEventListener = fieldFactory.FieldEventListener
		app.logger.Info("Field event listener initialized successfully")
	} else {
		app.logger.Info("Field SQS configuration not provided, skipping Field event listener initialization")
	}

	// Initialize Price List SQS subscriber service
	if app.cfg.AWS.SQSQueueURLPriceList != "" {
		sqlxDB := sqlx.NewDb(app.db.DB, "postgres")
		priceListFactory, err := priceListInternal.NewFactory(app.cfg, sqlxDB)
		if err != nil {
			app.logger.Error("failed to initialize price list factory", vlog.F("error", err))
			return
		}

		app.priceListEventListener = priceListFactory.PriceListEventListener
		app.logger.Info("Price List event listener initialized successfully")
	} else {
		app.logger.Info("Price List SQS configuration not provided, skipping Price List event listener initialization")
	}

}

// Run starts the server and handles incoming HTTP requests.
// It gracefully shuts down the server on interrupt signal.
func (app *AppBuilder) Run() {
	// Initialize subscribers
	app.startSubscribers()

	// Start Nature event listener if configured
	if app.natureEventListener != nil {
		if err := app.natureEventListener.Start(app.ctx); err != nil {
			app.logger.Error("failed to start Nature event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Nature event listener started successfully")
		}
	}

	// Start Area event listener if configured
	if app.areaEventListener != nil {
		if err := app.areaEventListener.Start(app.ctx); err != nil {
			app.logger.Error("failed to start Area event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Area event listener started successfully")
		}
	}

	// Start Account Branch Area event listener if configured
	if app.accountBranchAreaEventListener != nil {
		if err := app.accountBranchAreaEventListener.Start(app.ctx); err != nil {
			app.logger.Error("failed to start Account Branch Area event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Account Branch Area event listener started successfully")
		}
	}

	// Start Clinician Status event listener if configured
	if app.clinicianStatusEventListener != nil {
		if err := app.clinicianStatusEventListener.Start(app.ctx); err != nil {
			app.logger.Error("failed to start Clinician Status event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Clinician Status event listener started successfully")
		}
	}

	// Start Appointment Type event listener if configured
	if app.appointmentTypeEventListener != nil {
		if err := app.appointmentTypeEventListener.Start(app.ctx); err != nil {
			app.logger.Error("failed to start Appointment Type event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Appointment Type event listener started successfully")
		}
	}

	// Start Field event listener if configured
	if app.fieldEventListener != nil {
		if err := app.fieldEventListener.Start(app.ctx); err != nil {
			app.logger.Error("failed to start Field event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Field event listener started successfully")
		}
	}

	// Start Price List event listener if configured
	if app.priceListEventListener != nil {
		if err := app.priceListEventListener.Start(app.ctx); err != nil {
			app.logger.Error("failed to start Price List event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Price List event listener started successfully")
		}
	}

	// Start the server in a separate goroutine with recovery
	go func() {
		defer func() {
			if r := recover(); r != nil {
				app.logger.Error("panic recovered in server", vlog.F("error", r))
			}
		}()

		app.logger.Info("starting server", vlog.F("port", app.cfg.Port))

		app.PrintRoutes()

		if err := app.router.Start(app.cfg.Port); err != nil && !errors.Is(err, http.ErrServerClosed) {
			app.logger.Fatal("shutting down the server due to error", vlog.F("error", err))
		}
	}()

	// Graceful shutdown handling
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	app.logger.Info("shutdown signal received, starting graceful shutdown...")

	// Stop Nature event listener first
	if app.natureEventListener != nil {
		if err := app.natureEventListener.Stop(); err != nil {
			app.logger.Error("error stopping Nature event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Nature event listener stopped successfully")
		}
	}

	// Stop Area event listener
	if app.areaEventListener != nil {
		if err := app.areaEventListener.Stop(); err != nil {
			app.logger.Error("error stopping Area event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Area event listener stopped successfully")
		}
	}

	// Stop Account Branch Area event listener
	if app.accountBranchAreaEventListener != nil {
		if err := app.accountBranchAreaEventListener.Stop(); err != nil {
			app.logger.Error("error stopping Account Branch Area event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Account Branch Area event listener stopped successfully")
		}
	}

	// Stop Clinician Status event listener
	if app.clinicianStatusEventListener != nil {
		if err := app.clinicianStatusEventListener.Stop(); err != nil {
			app.logger.Error("error stopping Clinician Status event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Clinician Status event listener stopped successfully")
		}
	}

	// Stop Appointment Type event listener
	if app.appointmentTypeEventListener != nil {
		if err := app.appointmentTypeEventListener.Stop(); err != nil {
			app.logger.Error("error stopping Appointment Type event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Appointment Type event listener stopped successfully")
		}
	}

	// Stop Field event listener
	if app.fieldEventListener != nil {
		if err := app.fieldEventListener.Stop(); err != nil {
			app.logger.Error("error stopping Field event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Field event listener stopped successfully")
		}
	}

	// Stop Price List event listener
	if app.priceListEventListener != nil {
		if err := app.priceListEventListener.Stop(); err != nil {
			app.logger.Error("error stopping Price List event listener", vlog.F("error", err))
		} else {
			app.logger.Info("Price List event listener stopped successfully")
		}
	}

	ctx, cancel := context.WithTimeout(app.ctx, timeout)
	defer cancel()

	if err := app.router.Shutdown(ctx); err != nil {
		app.logger.Error("error shutting down the server", vlog.F("error", err))
	}

	app.logger.Info("server shut down")
}

func RegisterRoutes(e *echo.Echo, db *sqlx.DB, logger vlog.Logger) {
	route.RegisterAreaRoutes(e, db, logger)
	route.RegisterFieldNatureRoutes(e, db, logger)
	route.RegisterAppointmentTypeRoutes(e, db, logger)
	route.RegisterBranchFieldNatureRoutes(e, db, logger)
}

func BootstrapSecurity(router *echo.Echo, cfg config.Configuration) {
	router.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins:     cfg.Security.AllowOrigins,
		AllowMethods:     cfg.Security.AllowMethods,
		AllowHeaders:     cfg.Security.AllowHeaders,
		AllowCredentials: cfg.Security.AllowCredentials,
	}))

	router.Use(middleware.SecureWithConfig(middleware.SecureConfig{
		XSSProtection:         "1; mode=block",
		ContentTypeNosniff:    "nosniff",
		XFrameOptions:         "DENY",
		HSTSMaxAge:            63072000, // 2 years
		ContentSecurityPolicy: "default-src 'self'",
	}))

	router.Use(middleware.BodyLimit(cfg.Security.MaxBodySize))

	router.Use(middleware.RateLimiter(
		middleware.NewRateLimiterMemoryStore(cfg.Security.RateLimitPerSecond),
	))

	if strings.ToLower(cfg.Environment) == "production" {
		router.Pre(middleware.HTTPSRedirect())
	}
}
