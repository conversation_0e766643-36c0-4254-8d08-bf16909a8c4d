package middleware

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt"
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awsutil "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// CognitoJWT returns an Echo middleware that validates the token:
// - RS256: via Cognito (pkg/aws.ParseJWT), requires kid in header
// - HS256: via HMAC using cfg.JWTSecretKey (legacy support)
// Then extracts account UUID from claims (sub, cognito:username, username, account_uuid)
func CognitoJWT(cfg config.Configuration) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authz := c.Request().Header.Get("Authorization")
			if authz == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{"error": "Unauthorized no authorization header"})
			}

			tokenString := strings.TrimSpace(strings.TrimPrefix(authz, "Bearer "))

			alg, kid, ok := jwtHeaderAlgKid(tokenString)
			if !ok {
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{"error": "Unauthorized invalid token header"})
			}

			switch {
			case strings.HasPrefix(alg, "RS"): // Cognito path
				if kid == "" {
					return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{"error": "Unauthorized invalid token header"})
				}
				var parseErr error
				func() {
					defer func() {
						if r := recover(); r != nil {
							parseErr = echo.NewHTTPError(http.StatusUnauthorized, map[string]string{"error": "Unauthorized invalid token"})
						}
					}()
					parseErr = awsutil.ParseJWT(cfg.AWS.CognitoUserPoolID, cfg.AWS.CognitoRegion, c)
				}()
				if parseErr != nil {
					return parseErr
				}

				// Extract claims unverified (signature already checked by ParseJWT)
				t, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
				if err == nil {
					if claims, ok := t.Claims.(jwt.MapClaims); ok {
						setUserUUIDFromClaims(c, claims)
					}
				}

			case strings.HasPrefix(alg, "HS"): // Legacy HMAC path
				token, err := jwt.Parse(tokenString, func(t *jwt.Token) (interface{}, error) {
					if _, ok := t.Method.(*jwt.SigningMethodHMAC); !ok {
						return nil, fmt.Errorf("unexpected signing method")
					}
					return []byte(cfg.JWTSecretKey), nil
				})
				if err != nil || !token.Valid {
					return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{"error": "Unauthorized invalid token"})
				}
				if claims, ok := token.Claims.(jwt.MapClaims); ok {
					setUserUUIDFromClaims(c, claims)
				}

			default:
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{"error": "Unauthorized unsupported algorithm"})
			}

			// Back-compat with pkg/aws ParseJWT if it set userID
			if v := c.Get("userID"); v != nil {
				if s, ok := v.(string); ok && c.Get("account_uuid") == nil {
					c.Set("account_uuid", s)
				}
			}

			return next(c)
		}
	}
}

func setUserUUIDFromClaims(c echo.Context, claims jwt.MapClaims) {
	if sub, ok := claims["sub"].(string); ok && sub != "" {
		c.Set("account_uuid", sub)
		return
	}
	if cu, ok := claims["cognito:username"].(string); ok && cu != "" {
		c.Set("account_uuid", cu)
		return
	}
	if u, ok := claims["username"].(string); ok && u != "" {
		c.Set("account_uuid", u)
		return
	}
	if uu, ok := claims["account_uuid"].(string); ok && uu != "" {
		c.Set("account_uuid", uu)
		return
	}
}

func jwtHeaderAlgKid(token string) (alg string, kid string, ok bool) {
	parts := strings.Split(token, ".")
	if len(parts) < 2 {
		return "", "", false
	}
	hdrBytes, err := base64.RawURLEncoding.DecodeString(parts[0])
	if err != nil {
		return "", "", false
	}
	var header map[string]any
	if err := json.Unmarshal(hdrBytes, &header); err != nil {
		return "", "", false
	}
	if a, exists := header["alg"].(string); exists {
		alg = a
	}
	if k, exists := header["kid"].(string); exists {
		kid = k
	}
	return alg, kid, alg != ""
}
