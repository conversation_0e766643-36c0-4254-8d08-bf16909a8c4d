package route

import (
	"github.com/jmoiron/sqlx"
	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	authmw "gitlab.viswalslab.com/backend/price-list/infra/server/middleware"
	branchfieldnature "gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func RegisterBranchFieldNatureRoutes(e *echo.Echo, db *sqlx.DB, logger vlog.Logger) {
	repo := repository.NewBranchFieldNatureRepository(db, logger)
	handler := branchfieldnature.NewBranchFieldNatureHandlerFactory(repo, logger)
	cfg := config.FromEnv()

	g := e.Group("/branch-field-nature", authmw.CognitoJWT(cfg))
	g.POST("", handler.Create)
}
