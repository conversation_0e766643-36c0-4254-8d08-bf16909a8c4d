package route

import (
	"github.com/jmoiron/sqlx"
	"github.com/labstack/echo/v4"
	internal "gitlab.viswalslab.com/backend/price-list/internal/field_nature"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// RegisterFieldRoutes registers field routes with the Echo router
func RegisterFieldNatureRoutes(e *echo.Echo, db *sqlx.DB, logger vlog.Logger) {
	handler := internal.NewFieldHandlerFactory(db, logger)
	g := e.Group("/fields")
	g.GET("", handler.ListFields)
	g.GET("/:fieldId", handler.GetField)
}
