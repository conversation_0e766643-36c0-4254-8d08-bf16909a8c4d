package route

import (
	"github.com/jmoiron/sqlx"
	"github.com/labstack/echo/v4"
	internal "gitlab.viswalslab.com/backend/price-list/internal/appointment_type"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// RegisterAppointmentTypeRoutes registers appointment type routes with the Echo router
func RegisterAppointmentTypeRoutes(e *echo.Echo, db *sqlx.DB, logger vlog.Logger) {
	handler := internal.NewAppointmentTypeHandlerFactory(db, logger)
	g := e.Group("/appointment-types")
	g.GET("", handler.ListAppointmentTypes)
	g.GET("/:appointmentTypeId", handler.GetAppointmentType)
}
