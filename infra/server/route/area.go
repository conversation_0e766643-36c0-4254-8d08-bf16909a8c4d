package route

import (
	"github.com/jmoiron/sqlx"
	"github.com/labstack/echo/v4"
	internal "gitlab.viswalslab.com/backend/price-list/internal/area"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// RegisterAreaRoutes registers area routes with the Echo router
func RegisterAreaRoutes(e *echo.Echo, db *sqlx.DB, logger vlog.Logger) {
	handler := internal.NewAreaHandlerFactory(db, logger)
	g := e.Group("/areas")
	g.GET("", handler.ListAreas)
	g.GET("/:areaId", handler.GetArea)
}
