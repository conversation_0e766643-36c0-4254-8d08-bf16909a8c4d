package config

import (
	"log"
	"os"

	"strconv"
	"strings"

	"github.com/joho/godotenv"
	"golang.org/x/time/rate"
)

// Configuration represents the application settings.
type Configuration struct {
	AppName     string
	Environment string
	LogLevel    string
	Host        string
	Port        string

	DataBase     DataBase
	Security     Security
	AWS          AWSConfig
	JWTSecretKey string
}

type DataBase struct {
	DriverName string
	URL        string
}

type Security struct {
	AllowOrigins       []string
	AllowMethods       []string
	AllowHeaders       []string
	AllowCredentials   bool
	MaxBodySize        string
	RateLimitPerSecond rate.Limit
}

func parseRateLimit(value string, defaultValue float64) rate.Limit {
	if value == "" {
		return rate.Limit(defaultValue)
	}
	limit, err := strconv.ParseFloat(value, 64)
	if err != nil {
		log.Printf("Warning: Invalid rate limit value (%s), using default value", value)
		return rate.Limit(defaultValue)
	}
	return rate.Limit(limit)
}

type AWSConfig struct {
	Region                     string
	AccessKeyID                string
	SecretAccessKey            string
	SQSQueueURLNature          string
	SQSQueueURLPriceList       string
	SQSQueueURLArea            string
	SQSQueueURLField           string
	SQSQueueURLBranchArea      string
	SQSQueueURLClinicianStatus string
	SQSQueueURLAppointmentType string

	// Cognito configuration
	CognitoRegion       string
	CognitoUserPoolID   string
	CognitoClientID     string
	CognitoClientSecret string
}

func FromEnv() Configuration {
	var configuration Configuration

	err := godotenv.Load()
	if err != nil && os.Getenv("ENVIRONMENT") == "local" {
		log.Fatal("error loading .env file")
	}

	mustGet := func(key string) string {
		val := os.Getenv(key)
		if val == "" {
			log.Fatalf("Missing required environment variable: %s", key)
		}
		return val
	}

	configuration.AppName = mustGet("APP_NAME")
	configuration.Environment = mustGet("ENVIRONMENT")
	configuration.LogLevel = mustGet("LOG_LEVEL")
	configuration.Host = mustGet("HOST")
	configuration.Port = mustGet("PORT")

	dbURL := mustGet("DATABASE_URL")

	configuration.DataBase = DataBase{
		DriverName: "postgres",
		URL:        dbURL,
	}

	configuration.Security = Security{
		AllowOrigins:       strings.Split(mustGet("CORS_ALLOW_ORIGINS"), ","),
		AllowMethods:       strings.Split(mustGet("CORS_ALLOW_METHODS"), ","),
		AllowHeaders:       strings.Split(mustGet("CORS_ALLOW_HEADERS"), ","),
		AllowCredentials:   os.Getenv("CORS_ALLOW_CREDENTIALS") == "true",
		MaxBodySize:        mustGet("MAX_BODY_SIZE"),
		RateLimitPerSecond: parseRateLimit(mustGet("RATE_LIMIT_PER_SECOND"), 100),
	}

	configuration.AWS = AWSConfig{
		Region:                     os.Getenv("AWS_SQS_REGION"),
		AccessKeyID:                os.Getenv("AWS_SQS_ACCESS_KEY"),
		SecretAccessKey:            os.Getenv("AWS_SQS_SECRET_KEY"),
		SQSQueueURLNature:          os.Getenv("AWS_SQS_QUEUE_URL_NATURE"),
		SQSQueueURLPriceList:       os.Getenv("AWS_SQS_QUEUE_URL_PRICE_LIST"),
		SQSQueueURLArea:            os.Getenv("AWS_SQS_QUEUE_URL_AREA"),
		SQSQueueURLField:           os.Getenv("AWS_SQS_QUEUE_URL_FIELD"),
		SQSQueueURLBranchArea:      os.Getenv("AWS_SQS_QUEUE_URL_BRANCH_AREA"),
		SQSQueueURLClinicianStatus: os.Getenv("AWS_SQS_QUEUE_URL_CLINICIAN_STATUS"),
		SQSQueueURLAppointmentType: os.Getenv("AWS_SQS_QUEUE_URL_APPOINTMENT_TYPE"),

		// Cognito
		CognitoRegion:       mustGet("AWS_COGNITO_REGION"),
		CognitoUserPoolID:   mustGet("AWS_COGNITO_USER_POOL_ID"),
		CognitoClientID:     mustGet("AWS_COGNITO_CLIENT_ID"),
		CognitoClientSecret: mustGet("AWS_COGNITO_CLIENT_SECRET"),
	}

	configuration.JWTSecretKey = mustGet("JWT_SECRET_KEY")

	return configuration
}
