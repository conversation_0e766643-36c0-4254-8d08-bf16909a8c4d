package usecase_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
)

// MockUpsertNatureUsecase is a mock implementation of UpsertNatureUsecase
type MockUpsertNatureUsecase struct {
	mock.Mock
}

func (m *MockUpsertNatureUsecase) Execute(ctx context.Context, input *usecase.UpsertNatureInput) (*usecase.UpsertNatureOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.UpsertNatureOutput), args.Error(1)
}
