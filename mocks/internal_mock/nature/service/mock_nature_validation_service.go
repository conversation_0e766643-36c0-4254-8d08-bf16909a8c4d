package service_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/service"
)

// MockNatureValidationService is a mock implementation of NatureValidationService
type MockNatureValidationService struct {
	mock.Mock
}

func (m *MockNatureValidationService) ValidateNameAndUUID(ctx context.Context, name string, uuid string) error {
	args := m.Called(ctx, name, uuid)
	return args.Error(0)
}

func (m *MockNatureValidationService) ValidateBusinessRules(ctx context.Context, nature *entity.Nature) error {
	args := m.Called(ctx, nature)
	return args.Error(0)
}

func (m *MockNatureValidationService) ValidateUpsertInput(ctx context.Context, input *service.UpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}
