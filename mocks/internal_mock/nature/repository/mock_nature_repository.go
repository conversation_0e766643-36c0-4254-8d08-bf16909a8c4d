package repository_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
)

// MockNatureRepository is a mock implementation of NatureRepository
type MockNatureRepository struct {
	mock.Mock
}

func (m *MockNatureRepository) UpsertNature(ctx context.Context, nature *entity.Nature) error {
	args := m.Called(ctx, nature)
	return args.Error(0)
}
