package sql_mocks

import (
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
)

// SetupTestDB creates a mock database for testing
func SetupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

// ExpectUpsertNature sets up common upsert expectations
func ExpectUpsertNature(mock sqlmock.Sqlmock, id, name, description, icon string, enabled bool) {
	expectedQuery := `INSERT INTO public\.nature.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(id, name, description, icon, enabled).
		WillReturnResult(sqlmock.NewResult(1, 1))
}

// ExpectUpsertNatureError sets up constraint violation expectations
func ExpectUpsertNatureError(mock sqlmock.Sqlmock, id, name, description, icon string, enabled bool, constraint string) {
	expectedQuery := `INSERT INTO public\.nature.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(id, name, description, icon, enabled).
		WillReturnError(errors.New(`duplicate key value violates unique constraint "` + constraint + `"`))
}
