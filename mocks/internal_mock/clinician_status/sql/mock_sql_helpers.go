package sql_mocks

import (
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
)

// SetupTestDB creates a mock database for testing
func SetupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

// ExpectUpsertClinicianStatus sets up common upsert expectations
func ExpectUpsertClinicianStatus(mock sqlmock.Sqlmock, id, name string, enabled bool) {
	expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(id, name, enabled).
		WillReturnResult(sqlmock.NewResult(1, 1))
}

// ExpectUpsertClinicianStatusError sets up upsert expectations that return an error
func ExpectUpsertClinicianStatusError(mock sqlmock.Sqlmock, id, name string, enabled bool, err error) {
	expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(id, name, enabled).
		WillReturnError(err)
}

// ExpectNameConstraintViolation sets up expectations for a name constraint violation
func ExpectNameConstraintViolation(mock sqlmock.Sqlmock, id, name string, enabled bool) {
	expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
	constraintErr := errors.New(`pq: duplicate key value violates unique constraint "clinician_status_name_key"`)
	mock.ExpectExec(expectedQuery).
		WithArgs(id, name, enabled).
		WillReturnError(constraintErr)
}
