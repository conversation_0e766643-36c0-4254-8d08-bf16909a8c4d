package repository_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
)

// MockClinicianStatusUpsertRepository is a mock implementation of ClinicianStatusUpsertRepository
type MockClinicianStatusUpsertRepository struct {
	mock.Mock
}

func (m *MockClinicianStatusUpsertRepository) UpsertClinicianStatus(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error {
	args := m.Called(ctx, clinicianStatus)
	return args.Error(0)
}
