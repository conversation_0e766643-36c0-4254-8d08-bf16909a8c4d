package usecase_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/usecase"
)

// MockUpsertClinicianStatusUsecase is a mock implementation of UpsertClinicianStatusUsecase
type MockUpsertClinicianStatusUsecase struct {
	mock.Mock
}

func (m *MockUpsertClinicianStatusUsecase) Execute(ctx context.Context, input *usecase.UpsertClinicianStatusInput) (*usecase.UpsertClinicianStatusOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.UpsertClinicianStatusOutput), args.Error(1)
}
