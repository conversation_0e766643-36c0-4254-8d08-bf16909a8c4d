package usecase_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/usecase"
)

// MockUpsertAccountBranchAreaUsecase is a mock implementation of UpsertAccountBranchAreaUsecase
type MockUpsertAccountBranchAreaUsecase struct {
	mock.Mock
}

func (m *MockUpsertAccountBranchAreaUsecase) Execute(ctx context.Context, input *usecase.UpsertAccountBranchAreaInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}
