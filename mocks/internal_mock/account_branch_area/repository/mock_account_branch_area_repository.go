package repository_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
)

// MockAccountBranchAreaUpsertRepository is a mock implementation of AccountBranchAreaUpsertRepository
type MockAccountBranchAreaUpsertRepository struct {
	mock.Mock
}

func (m *MockAccountBranchAreaUpsertRepository) UpsertAccountBranchArea(ctx context.Context, accountBranchArea *entity.AccountBranchArea) error {
	args := m.Called(ctx, accountBranchArea)
	return args.Error(0)
}
