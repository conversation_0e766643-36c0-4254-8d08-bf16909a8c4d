package sql_mocks

import (
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
)

// SetupTestDB creates a mock database for testing
func SetupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

// ExpectUpsertAccountBranchArea sets up common upsert expectations
func ExpectUpsertAccountBranchArea(mock sqlmock.Sqlmock, id, accountID, branchID, areaID string) {
	expectedQuery := `INSERT INTO public\.account_branch_area.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(id, accountID, branchID, areaID, sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnResult(sqlmock.NewResult(1, 1))
}

// ExpectUpsertAccountBranchAreaError sets up constraint violation expectations
func ExpectUpsertAccountBranchAreaError(mock sqlmock.Sqlmock, id, accountID, branchID, areaID string, constraint string) {
	expectedQuery := `INSERT INTO public\.account_branch_area.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(id, accountID, branchID, areaID, sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnError(errors.New(`duplicate key value violates unique constraint "` + constraint + `"`))
}

// ExpectUpsertAccountBranchAreaForeignKeyError sets up foreign key violation expectations
func ExpectUpsertAccountBranchAreaForeignKeyError(mock sqlmock.Sqlmock, id, accountID, branchID, areaID string, constraint string) {
	expectedQuery := `INSERT INTO public\.account_branch_area.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(id, accountID, branchID, areaID, sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnError(errors.New(`insert or update on table "account_branch_area" violates foreign key constraint "` + constraint + `"`))
}
