package service_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/service"
)

// MockAccountBranchAreaValidationService is a mock implementation of AccountBranchAreaValidationService
type MockAccountBranchAreaValidationService struct {
	mock.Mock
}

func (m *MockAccountBranchAreaValidationService) ValidateBusinessRules(ctx context.Context, accountBranchArea *entity.AccountBranchArea) error {
	args := m.Called(ctx, accountBranchArea)
	return args.Error(0)
}

func (m *MockAccountBranchAreaValidationService) ValidateUpsertInput(ctx context.Context, input *service.UpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}
