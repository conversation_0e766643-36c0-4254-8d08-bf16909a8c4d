package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// AreaEventData represents the data for area events
type AreaEventData struct {
	ID      *string `json:"id,omitempty"`
	Label   string  `json:"label"`
	Acronym string  `json:"acronym"`
	Enabled bool    `json:"enabled"`
}

// AreaEventMessage represents the event message structure
type AreaEventMessage struct {
	Data      AreaEventData `json:"data"`
	Timestamp time.Time     `json:"timestamp"`
	Source    string        `json:"source"`
	EventType string        `json:"event_type"`
}

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message AreaEventMessage, description string) error {
	log.Printf("📨 %s: %s (%s)", description, message.Data.Label, message.Data.Acronym)

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "area",
		"source":     "test-provider",
		"event_type": "area.upsert",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("Starting Area Test Provider - Testing INSERT + INSERT + UPDATE flow...")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Area queue config
	if cfg.AWS.SQSQueueURLArea == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_AREA not configured in .env")
	}

	log.Printf("Using Area SQS Queue: %s", cfg.AWS.SQSQueueURLArea)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLArea,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()

	// Generate UUIDs for our test records
	firstID := uuid.New().String()
	secondID := uuid.New().String()

	log.Println("\n🔹 PHASE 1: Sending 2 INSERT operations")

	// 1. First INSERT
	firstInsert := AreaEventMessage{
		Data: AreaEventData{
			ID:      &firstID,
			Label:   "Medicine",
			Acronym: "MED",
			Enabled: true,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "area.upsert",
	}

	err = sendMessage(ctx, sqsClient, firstInsert, "INSERT 1/2")
	if err != nil {
		log.Fatalf("Failed first insert: %v", err)
	}

	time.Sleep(3 * time.Second)

	// 2. Second INSERT
	secondInsert := AreaEventMessage{
		Data: AreaEventData{
			ID:      &secondID,
			Label:   "Dentistry",
			Acronym: "DENT",
			Enabled: true,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "area.upsert",
	}

	err = sendMessage(ctx, sqsClient, secondInsert, "INSERT 2/2")
	if err != nil {
		log.Fatalf("Failed second insert: %v", err)
	}

	log.Println("\n🔹 PHASE 2: Waiting for inserts to be processed...")
	time.Sleep(8 * time.Second) // Give time for inserts to be processed

	log.Println("\n🔹 PHASE 3: Sending 1 UPDATE operation")

	// 3. UPDATE the first record
	updateMessage := AreaEventMessage{
		Data: AreaEventData{
			ID:      &firstID,           // Same ID as first insert
			Label:   "General Medicine", // Updated label
			Acronym: "GMED",             // Updated acronym
			Enabled: false,              // Changed to disabled
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "area.upsert",
	}

	err = sendMessage(ctx, sqsClient, updateMessage, "UPDATE")
	if err != nil {
		log.Fatalf("Failed update: %v", err)
	}

	log.Println("\nTEST FLOW COMPLETE!")
	log.Printf("Summary:")
	log.Printf("   INSERT: Medicine (ID: %s)", firstID)
	log.Printf("   INSERT: Dentistry (ID: %s)", secondID)
	log.Printf("   UPDATE: General Medicine (ID: %s)", firstID)
	log.Println("\n Check your database to verify:")
	log.Println("   1. Two records should exist")
	log.Printf("   2. Record %s should be UPDATED with new label/acronym", firstID[:8]+"...")
	log.Printf("   3. Record %s should remain unchanged", secondID[:8]+"...")
	log.Println("   4. created_at timestamps should be different")
	log.Println("   5. updated_at should be newer for the updated record")
	log.Println("\n   SQL: SELECT id, label, acronym, enabled, created_at, updated_at FROM area ORDER BY created_at DESC;")
}
