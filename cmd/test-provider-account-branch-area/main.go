package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// AccountBranchAreaMapping represents a single branch with its associated areas
type AccountBranchAreaMapping struct {
	Branch string   `json:"branch"`
	Areas  []string `json:"areas"`
}

// AccountBranchAreaEventData represents the data structure for account branch area mapping events
type AccountBranchAreaEventData struct {
	AccountID         string                     `json:"account_id"`  // branch manager userID (jwt)
	AccountBranchArea []AccountBranchAreaMapping `json:"branch_area"` // array of branch-area mappings
}

// AccountBranchAreaEventMessage represents the event message structure for account branch area mapping operations
type AccountBranchAreaEventMessage struct {
	Data      AccountBranchAreaEventData `json:"data"`
	Timestamp time.Time                  `json:"timestamp"`
	Source    string                     `json:"source"`
	EventType string                     `json:"event_type"`
}

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message AccountBranchAreaEventMessage, description string) error {
	log.Printf("%s: Account %s with %d branch mappings",
		description,
		message.Data.AccountID[:8]+"...",
		len(message.Data.AccountBranchArea))

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "account-branch-area",
		"source":     "test-provider",
		"event_type": "account-branch-area.upsert",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("Starting Account Branch Area Test Provider - Testing Batch Mapping Operations...")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Account Branch Area queue config
	if cfg.AWS.SQSQueueURLBranchArea == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_BRANCH_AREA not configured in .env")
	}

	log.Printf("Using Account Branch Area SQS Queue: %s", cfg.AWS.SQSQueueURLBranchArea)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLBranchArea,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()

	// Generate test UUIDs
	accountID := uuid.New().String()
	branch1ID := uuid.New().String()
	branch2ID := uuid.New().String()

	// Use REAL area UUIDs from database
	area1ID := "84ce4d70-a442-412b-bf27-06f4544a8661" // Medicine
	area2ID := "4beed17b-a38a-4da1-8b26-94d2f1513001" // Dentistry
	area3ID := "e117dcf1-4acd-499f-80d2-7c868f23d6d0" // Psychology

	log.Println("\n🔹 PHASE 1: Initial batch mapping - 2 branches with multiple areas")

	// 1. Initial batch mapping
	initialMapping := AccountBranchAreaEventMessage{
		Data: AccountBranchAreaEventData{
			AccountID: accountID,
			AccountBranchArea: []AccountBranchAreaMapping{
				{
					Branch: branch1ID,
					Areas:  []string{area1ID, area2ID}, // Branch 1 → Medicine, Dentistry
				},
				{
					Branch: branch2ID,
					Areas:  []string{area1ID}, // Branch 2 → Medicine only
				},
			},
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "account-branch-area.upsert",
	}

	err = sendMessage(ctx, sqsClient, initialMapping, "INITIAL BATCH MAPPING")
	if err != nil {
		log.Fatalf("Failed initial mapping: %v", err)
	}

	time.Sleep(3 * time.Second)

	log.Printf("   Branch %s → Areas: [%s, %s]",
		branch1ID[:8]+"...",
		area1ID[:8]+"...",
		area2ID[:8]+"...")
	log.Printf("   Branch %s → Areas: [%s]",
		branch2ID[:8]+"...",
		area1ID[:8]+"...")

	log.Println("\n🔹 PHASE 2: Waiting for initial mapping to be processed...")
	time.Sleep(8 * time.Second) // Give time for inserts to be processed

	log.Println("\n🔹 PHASE 3: Update mapping - add/modify areas for branches")

	// 2. Update mapping (add psychology to branch1, add dentistry+psychology to branch2)
	updateMapping := AccountBranchAreaEventMessage{
		Data: AccountBranchAreaEventData{
			AccountID: accountID,
			AccountBranchArea: []AccountBranchAreaMapping{
				{
					Branch: branch1ID,
					Areas:  []string{area1ID, area2ID, area3ID}, // Branch 1 → Medicine, Dentistry, Psychology
				},
				{
					Branch: branch2ID,
					Areas:  []string{area1ID, area2ID, area3ID}, // Branch 2 → Medicine, Dentistry, Psychology
				},
			},
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "account-branch-area.upsert",
	}

	err = sendMessage(ctx, sqsClient, updateMapping, "UPDATE BATCH MAPPING")
	if err != nil {
		log.Fatalf("Failed update mapping: %v", err)
	}

	time.Sleep(3 * time.Second)

	log.Printf("   Branch %s → Areas: [%s, %s, %s]",
		branch1ID[:8]+"...",
		area1ID[:8]+"...",
		area2ID[:8]+"...",
		area3ID[:8]+"...")
	log.Printf("   Branch %s → Areas: [%s, %s, %s]",
		branch2ID[:8]+"...",
		area1ID[:8]+"...",
		area2ID[:8]+"...",
		area3ID[:8]+"...")

	log.Println("\n🔹 PHASE 4: Waiting for update to be processed...")
	time.Sleep(8 * time.Second) // Give time for updates to be processed

	log.Println("\n🔹 PHASE 5: Single branch mapping - test individual branch")

	// 3. Single branch mapping (only branch1, remove some areas)
	singleMapping := AccountBranchAreaEventMessage{
		Data: AccountBranchAreaEventData{
			AccountID: accountID,
			AccountBranchArea: []AccountBranchAreaMapping{
				{
					Branch: branch1ID,
					Areas:  []string{area3ID}, // Branch 1 → Psychology only
				},
			},
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "account-branch-area.upsert",
	}

	err = sendMessage(ctx, sqsClient, singleMapping, "SINGLE BRANCH MAPPING")
	if err != nil {
		log.Fatalf("Failed single branch mapping: %v", err)
	}

	time.Sleep(3 * time.Second)

	log.Printf("   Branch %s → Areas: [%s]",
		branch1ID[:8]+"...",
		area3ID[:8]+"...")

	log.Println("\nTEST FLOW COMPLETE!")
	log.Printf("Summary:")
	log.Printf("   Account ID: %s", accountID)
	log.Printf("   Branch 1 ID: %s", branch1ID)
	log.Printf("   Branch 2 ID: %s", branch2ID)
	log.Printf("   Area 1 ID (Medicine): %s", area1ID)
	log.Printf("   Area 2 ID (Dentistry): %s", area2ID)
	log.Printf("   Area 3 ID (Psychology): %s", area3ID)

	log.Println("\n Check your database to verify:")
	log.Println("   1. Account branch area mappings should exist")
	log.Printf("   2. Branch %s should have mapping to area %s (Psychology)", branch1ID[:8]+"...", area3ID[:8]+"...")
	log.Printf("   3. Branch %s should have mappings to all 3 areas", branch2ID[:8]+"...")
	log.Println("   4. All mappings should belong to account", accountID[:8]+"...")
	log.Println("   5. Each mapping should show correct account_id, branch_id, and area_id")

	log.Println("\n   SQL: SELECT account_id, branch_id, area_id, created_at, updated_at FROM account_branch_area ORDER BY branch_id, area_id;")
	log.Println("   Expected final state:")
	log.Printf("     %s | %s | %s", accountID, branch1ID, area3ID)
	log.Printf("     %s | %s | %s", accountID, branch2ID, area1ID)
	log.Printf("     %s | %s | %s", accountID, branch2ID, area2ID)
	log.Printf("     %s | %s | %s", accountID, branch2ID, area3ID)
}
