package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/field/event"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message event.FieldEventMessage, description string) error {
	log.Printf("%s: Field '%s' (enabled: %t)",
		description,
		message.Data.Name,
		message.Data.Enabled)

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "field",
		"source":     "test-provider",
		"event_type": "field.upsert",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("Starting Field Test Provider - Testing Field Upsert Operations...")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Field queue config
	if cfg.AWS.SQSQueueURLField == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_FIELD not configured in .env")
	}

	log.Printf("Using Field SQS Queue: %s", cfg.AWS.SQSQueueURLField)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLField,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()

	// Use REAL area UUIDs from database
	areaUUIDMedicine := "84ce4d70-a442-412b-bf27-06f4544a8661"  // Medicine
	areaUUIDDentistry := "4beed17b-a38a-4da1-8b26-94d2f1513001" // Dentistry

	log.Println("\n🔹 PHASE 1: Create new field without UUID (system generates)")

	// 1. Create field without UUID - system generates one
	newField := event.FieldEventMessage{
		Data: event.FieldEventData{
			Name:        "Test Cardiology",
			Description: core.ToPtr("Heart-related medical specialty for testing"),
			Icon:        core.ToPtr("heart-icon"),
			Position:    core.ToPtr(1),
			Enabled:     true,
			AreaID:      &areaUUIDMedicine,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "field.upsert",
	}

	err = sendMessage(ctx, sqsClient, newField, "CREATE NEW FIELD")
	if err != nil {
		log.Fatalf("Failed to create new field: %v", err)
	}

	time.Sleep(3 * time.Second)
	log.Printf("   Field: %s → Area: %s", newField.Data.Name, areaUUIDMedicine[:8]+"...")

	log.Println("\n🔹 PHASE 2: Create field with specific UUID")

	// 2. Create field with specific UUID
	fieldUUID := core.NewID().Value()
	specificField := event.FieldEventMessage{
		Data: event.FieldEventData{
			ID:          &fieldUUID,
			Name:        "Test Neurology",
			Description: core.ToPtr("Brain and nervous system specialty"),
			Icon:        core.ToPtr("brain-icon"),
			Position:    core.ToPtr(2),
			Enabled:     true,
			AreaID:      &areaUUIDMedicine,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "field.upsert",
	}

	err = sendMessage(ctx, sqsClient, specificField, "CREATE FIELD WITH UUID")
	if err != nil {
		log.Fatalf("Failed to create field with UUID: %v", err)
	}

	time.Sleep(3 * time.Second)
	log.Printf("   Field: %s (UUID: %s) → Area: %s",
		specificField.Data.Name,
		fieldUUID[:8]+"...",
		areaUUIDMedicine[:8]+"...")

	log.Println("\n🔹 PHASE 3: Waiting for fields to be processed...")
	time.Sleep(8 * time.Second)

	log.Println("\n🔹 PHASE 4: Update existing field (same UUID)")

	// 3. Update existing field - change description, disable it, move to dentistry
	updatedField := event.FieldEventMessage{
		Data: event.FieldEventData{
			ID:          &fieldUUID, // Same UUID = update
			Name:        "Updated Neurology",
			Description: core.ToPtr("Updated: Brain and nervous system specialty - now disabled"),
			Icon:        core.ToPtr("updated-brain-icon"),
			Position:    core.ToPtr(10),
			Enabled:     false,              // Disable it
			AreaID:      &areaUUIDDentistry, // Move to dentistry
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "field.upsert",
	}

	err = sendMessage(ctx, sqsClient, updatedField, "UPDATE EXISTING FIELD")
	if err != nil {
		log.Fatalf("Failed to update field: %v", err)
	}

	time.Sleep(3 * time.Second)
	log.Printf("   Field: %s (UUID: %s) → Area: %s (Enabled: %t)",
		updatedField.Data.Name,
		fieldUUID[:8]+"...",
		areaUUIDDentistry[:8]+"...",
		updatedField.Data.Enabled)

	log.Println("\n🔹 PHASE 5: Create minimal field (no optional fields)")

	// 4. Create minimal field - only required fields
	minimalField := event.FieldEventMessage{
		Data: event.FieldEventData{
			Name:    "Minimal Test Field",
			Enabled: true,
			// No ID, Description, Icon, Position, AreaID
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "field.upsert",
	}

	err = sendMessage(ctx, sqsClient, minimalField, "CREATE MINIMAL FIELD")
	if err != nil {
		log.Fatalf("Failed to create minimal field: %v", err)
	}

	time.Sleep(3 * time.Second)
	log.Printf("   Field: %s (No area association)", minimalField.Data.Name)

	log.Println("\n🔹 PHASE 6: Waiting for all operations to complete...")
	time.Sleep(8 * time.Second)

	log.Println("\nTEST FLOW COMPLETE!")
	log.Printf("Summary:")
	log.Printf("   Field 1: %s (system generated UUID, area: Medicine)", newField.Data.Name)
	log.Printf("   Field 2: %s (UUID: %s, updated to area: Dentistry, disabled)", updatedField.Data.Name, fieldUUID[:8]+"...")
	log.Printf("   Field 3: %s (system generated UUID, no area)", minimalField.Data.Name)

	log.Println("\n Check your database to verify:")
	log.Println("   1. Three field records should exist")
	log.Printf("   2. Field '%s' should be enabled and associated with Medicine area", newField.Data.Name)
	log.Printf("   3. Field '%s' should be DISABLED and associated with Dentistry area", updatedField.Data.Name)
	log.Printf("   4. Field '%s' should be enabled with NO area association", minimalField.Data.Name)
	log.Println("   5. All fields should have proper timestamps")

	log.Println("\n   SQL: SELECT uuid, name, description, icon, position, enabled, area_uuid, created_at, updated_at FROM field WHERE name LIKE 'Test%' OR name LIKE 'Minimal%' OR name LIKE 'Updated%' ORDER BY created_at;")
	log.Println("   Expected results:")
	log.Printf("     1. Test Cardiology | enabled=true  | area_uuid=%s", areaUUIDMedicine)
	log.Printf("     2. Updated Neurology | enabled=false | area_uuid=%s", areaUUIDDentistry)
	log.Printf("     3. Minimal Test Field | enabled=true  | area_uuid=NULL")
}
