package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// ClinicianStatusEventData represents the data structure for clinician status events
type ClinicianStatusEventData struct {
	ID      *string `json:"id,omitempty"`
	Name    string  `json:"name"`
	Enabled bool    `json:"enabled"`
}

// ClinicianStatusEventMessage represents the event message structure for clinician status operations
type ClinicianStatusEventMessage struct {
	Data      ClinicianStatusEventData `json:"data"`
	Timestamp time.Time                `json:"timestamp"`
	Source    string                   `json:"source"`
	EventType string                   `json:"event_type"`
}

func sendMessage(ctx context.Context, sqsClient *awspkg.SQSClient, message ClinicianStatusEventMessage, description string) error {
	log.Printf("%s: %s (ID: %v, Enabled: %v)",
		description,
		message.Data.Name,
		message.Data.ID,
		message.Data.Enabled)

	// Convert to JSON
	messageBody, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Send to SQS
	_, err = sqsClient.SendMessage(ctx, string(messageBody), map[string]string{
		"entity":     "clinician-status",
		"source":     "test-provider",
		"event_type": "clinician-status.upsert",
	})

	if err != nil {
		log.Printf("Failed: %v", err)
		return err
	}

	log.Printf("Success: %s", description)
	return nil
}

func main() {
	log.Println("Starting Clinician Status Test Provider - Testing Insert/Update/DLQ Operations...")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Clinician Status queue config
	if cfg.AWS.SQSQueueURLClinicianStatus == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_CLINICIAN_STATUS not configured in .env")
	}

	log.Printf("Using Clinician Status SQS Queue: %s", cfg.AWS.SQSQueueURLClinicianStatus)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLClinicianStatus,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}
	defer sqsClient.Close()

	ctx := context.Background()

	log.Println("\n🔹 PHASE 1: INSERT new clinician status (no ID)")

	// 1. Insert new clinician status (no ID provided)
	insertMessage := ClinicianStatusEventMessage{
		Data: ClinicianStatusEventData{
			ID:      nil, // No ID = INSERT
			Name:    "intern",
			Enabled: true,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "clinician-status.upsert",
	}

	err = sendMessage(ctx, sqsClient, insertMessage, "INSERT NEW CLINICIAN STATUS")
	if err != nil {
		log.Fatalf("Failed insert operation: %v", err)
	}

	log.Println("\n🔹 PHASE 2: Waiting for insert to be processed...")
	time.Sleep(5 * time.Second)

	log.Println("\n🔹 PHASE 3: UPDATE existing clinician status (with ID)")

	// 2. Update existing clinician status (with specific ID)
	existingID := "aa6a5d7d-46f4-4c41-b4c5-37a49275649b" // graduate from migration data
	updateMessage := ClinicianStatusEventMessage{
		Data: ClinicianStatusEventData{
			ID:      &existingID, // With ID = UPDATE
			Name:    "graduate-updated",
			Enabled: false,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "clinician-status.upsert",
	}

	err = sendMessage(ctx, sqsClient, updateMessage, "UPDATE EXISTING CLINICIAN STATUS")
	if err != nil {
		log.Fatalf("Failed update operation: %v", err)
	}

	log.Println("\n🔹 PHASE 4: Waiting for update to be processed...")
	time.Sleep(5 * time.Second)

	log.Println("\n🔹 PHASE 5: TEST DLQ - Name conflict (duplicate name)")

	// 3. Cause name conflict to test DLQ
	dlqMessage := ClinicianStatusEventMessage{
		Data: ClinicianStatusEventData{
			ID:      func() *string { s := uuid.New().String(); return &s }(), // Different ID
			Name:    "supreme",                                                // This name already exists in the database
			Enabled: true,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "clinician-status.upsert",
	}

	err = sendMessage(ctx, sqsClient, dlqMessage, "DLQ TEST - NAME CONFLICT")
	if err != nil {
		log.Fatalf("Failed DLQ test operation: %v", err)
	}

	log.Println("\n🔹 PHASE 6: INSERT another new status")

	// 4. Insert another new status
	insertMessage2 := ClinicianStatusEventMessage{
		Data: ClinicianStatusEventData{
			ID:      nil, // No ID = INSERT
			Name:    "resident",
			Enabled: true,
		},
		Timestamp: time.Now(),
		Source:    "test-provider",
		EventType: "clinician-status.upsert",
	}

	err = sendMessage(ctx, sqsClient, insertMessage2, "INSERT SECOND NEW CLINICIAN STATUS")
	if err != nil {
		log.Fatalf("Failed second insert operation: %v", err)
	}

	log.Println("\nTEST FLOW COMPLETE!")
	log.Printf("Summary:")
	log.Printf("   1. INSERT: 'intern' (no ID)")
	log.Printf("   2. UPDATE: 'graduate-updated' (ID: %s)", existingID)
	log.Printf("   3. DLQ: 'supreme' (name conflict - should go to DLQ)")
	log.Printf("   4. INSERT: 'resident' (no ID)")

	log.Println("\n Check your database to verify:")
	log.Println("   1. New 'intern' record should exist")
	log.Println("   2. 'graduate' should be updated to 'graduate-updated' and enabled=false")
	log.Println("   3. 'supreme' conflict should be handled (message to DLQ)")
	log.Println("   4. New 'resident' record should exist")

	log.Println("\n   SQL: SELECT uuid, clinician_status, enabled FROM clinician_status ORDER BY clinician_status;")
	log.Println("   Expected records: elite, graduate-updated, intern, master, pro, resident, supreme")
}
