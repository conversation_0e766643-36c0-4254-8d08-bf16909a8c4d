package main

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/event"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
)

// Test scenarios for appointment type events
var testScenarios = []struct {
	name        string
	description string
	message     event.AppointmentTypeEventMessage
}{
	{
		name:        "Insert New Appointment Type",
		description: "Insert a new appointment type with auto-generated ID",
		message: event.AppointmentTypeEventMessage{
			Data: event.AppointmentTypeEventData{
				AppointmentType: "Consultation",
				Enabled:         true,
			},
			Timestamp: time.Now(),
			Source:    "test-provider",
			EventType: "appointment-type.upsert",
		},
	},
	{
		name:        "Insert Appointment Type with Specific ID",
		description: "Insert a new appointment type with a specific UUID",
		message: event.AppointmentTypeEventMessage{
			Data: event.AppointmentTypeEventData{
				ID:              core.ToPtr(core.NewID().Value()),
				AppointmentType: "Follow-up",
				Enabled:         true,
			},
			Timestamp: time.Now(),
			Source:    "test-provider",
			EventType: "appointment-type.upsert",
		},
	},
	{
		name:        "Update Existing Appointment Type",
		description: "Update an existing appointment type (disable it)",
		message: event.AppointmentTypeEventMessage{
			Data: event.AppointmentTypeEventData{
				ID:              core.ToPtr("84ce4d70-a442-412b-bf27-06f4544a8661"), // Use a known ID
				AppointmentType: "Emergency",
				Enabled:         false,
			},
			Timestamp: time.Now(),
			Source:    "test-provider",
			EventType: "appointment-type.upsert",
		},
	},
	{
		name:        "Insert Disabled Appointment Type",
		description: "Insert a new appointment type that is disabled",
		message: event.AppointmentTypeEventMessage{
			Data: event.AppointmentTypeEventData{
				AppointmentType: "Cancelled",
				Enabled:         false,
			},
			Timestamp: time.Now(),
			Source:    "test-provider",
			EventType: "appointment-type.upsert",
		},
	},
}

func main() {
	log.Println("Starting Appointment Type Test Provider - Testing Insert/Update Operations...")

	// Load configuration
	cfg := config.FromEnv()

	// Validate AWS Appointment Type queue config
	if cfg.AWS.SQSQueueURLAppointmentType == "" {
		log.Fatal("AWS_SQS_QUEUE_URL_APPOINTMENT_TYPE not configured in .env")
	}

	log.Printf("Using Appointment Type SQS Queue: %s", cfg.AWS.SQSQueueURLAppointmentType)

	// Initialize SQS client
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        cfg.AWS.SQSQueueURLAppointmentType,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		log.Fatalf("Failed to initialize SQS client: %v", err)
	}

	ctx := context.Background()

	// Test connection
	log.Println("Testing SQS connection...")
	_, err = sqsClient.Connect(ctx)
	if err != nil {
		log.Fatalf("Failed to connect to SQS: %v", err)
	}
	log.Println("✅ SQS connection successful")

	// Execute test scenarios
	for i, scenario := range testScenarios {
		log.Printf("\n--- Test %d: %s ---", i+1, scenario.name)
		log.Printf("Description: %s", scenario.description)

		// Convert message to JSON
		messageBody, err := json.Marshal(scenario.message)
		if err != nil {
			log.Printf("❌ Failed to marshal message: %v", err)
			continue
		}

		log.Printf("Message: %s", string(messageBody))

		// Send message to SQS
		messageID, err := sqsClient.SendMessage(ctx, string(messageBody), nil)
		if err != nil {
			log.Printf("❌ Failed to send message: %v", err)
			continue
		}

		log.Printf("✅ Message sent successfully with ID: %s", messageID)

		// Wait a bit between messages
		time.Sleep(2 * time.Second)
	}

	log.Println("\n🎉 All test scenarios completed!")
	log.Println("Check your application logs to see the event processing results.")
	log.Println("You can also check your database to verify the appointment types were created/updated correctly.")
}
