-- create the database
CREATE DATABASE price_list
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;


-- Create user: price_list_admin
CREATE ROLE price_list_admin WITH LOGIN PASSWORD 'password here';

-- Give access to the user
GRANT CONNECT ON DATABASE price_list TO price_list_admin;

-- Grant USAGE on the public schema (or whatever schema you use)
GRANT USAGE ON SCHEMA public TO price_list_admin;

-- Grant privileges on all existing tables
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO price_list_admin;

-- Also make sure future tables are covered
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO price_list_admin;

