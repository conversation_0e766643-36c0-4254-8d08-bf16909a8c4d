---
type: "always_apply"
---

## Naming Conventions

This project follows specific naming patterns across different layers of the clean architecture:

### Entity Layer
- **Main Entity**: Use singular form (e.g., `RatingOption`)
- **Input Structs**: Prefix with `New` + EntityName + `Input` (e.g., `NewRatingOptionInput`)
- **Hydration Functions**: Prefix with `Hydrate` + EntityName (e.g., `HydrateRatingOption`)

### Repository Layer
- **Filter Structs**: Prefix with `Get` + EntityNamePlural + `Filter` (e.g., `GetRatingOptionsFilter`)
- **Model Structs**: EntityName + `Model` (e.g., `RatingOptionModel`)
- **Result Structs**: Prefix with `Get` + EntityNamePlural + `Result` (e.g., `GetRatingOptionsResult`)

### Handler/Transport Layer
- **Request Structs**: Prefix with operation + EntityNamePlural + `Request` (e.g., `GetRatingOptionsRequest`)
- **Response Structs**: Prefix with operation + EntityNamePlural + `Response` (e.g., `GetRatingOptionsResponse`)

### UseCase Layer
- **Input Structs**: Prefix with operation + EntityNamePlural + `Input` (e.g., `GetRatingOptionsInput`)
- **Output Structs**: Prefix with operation + EntityNamePlural + `Output` (e.g., `GetRatingOptionsOutput`)

### General Rules
- Use PascalCase for all struct names
- Entity names should be singular in the entity layer, plural in other layers when referring to collections
- Operation prefixes: `Get`, `List`, `Create`, `Update`, `Delete`, `Upsert`
- Maintain consistency across all domains in the `internal/` directory