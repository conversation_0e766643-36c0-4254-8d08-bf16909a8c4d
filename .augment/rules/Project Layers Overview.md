---
type: "always_apply"
---

Entity Layer: This is the core domain layer that contains the business objects and their behavior. These are pure Go structs that include business rules and validation, and they have no dependencies on other layers. An example is an 

Account entity with its properties and validation rules.

Use Case Layer: This layer contains the application's business logic and implements specific use cases. It orchestrates data flow between entities and repositories and depends on interfaces defined in the domain layer. Examples include 

ComplianceAuditUsecase and BudgetPlanningUsecase. Use case interfaces can have contracts like 

Execute(Input) (*Output, error) or Execute() error.

Service Layer: This layer is for shared business logic and contains reusable operations that are not full use cases. Services can be used by multiple use cases to prevent code duplication, are typically stateless, and can depend on repositories. Examples of service layer scenarios include validation services, calculation services, and business rules used in multiple contexts. Services should be focused and cohesive, and their contracts should be defined using interfaces. An example is a 

CompanyValidationService interface with a ValidateCompanyStructure method that can be used by a CreateCompanyUseCase.

Repository Layer: This layer is responsible for data persistence and retrieval. It implements interfaces defined by the domain layer and abstracts database implementation details, allowing support for different storage types like SQL or NoSQL. An example is an 

AccountRepository with methods such as Create() and FindByID().

Transport Layer: This layer handles HTTP, gRPC, and REST endpoints. It is responsible for converting external requests into internal domain objects, handling serialization and deserialization, and contains request/response DTOs. For instance, it would contain the HTTP handlers for creating and updating an account.

Event Layer: This layer manages asynchronous operations, including event publishing and subscription. It can be integrated with message brokers like RabbitMQ or Kafka. An example would be handling events for account creation or notifications.

Factory: The factory handles dependency injection by wiring up all the components. It creates instances of repositories, use cases, and services, and manages the lifecycle of these dependencies