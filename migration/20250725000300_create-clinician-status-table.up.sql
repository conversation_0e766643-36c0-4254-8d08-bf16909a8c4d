-- Create clinician_status table
CREATE TABLE public.clinician_status (
    "uuid" uuid NOT NULL,
    clinician_status varchar NOT NULL,
    enabled bool NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    CONSTRAINT clinician_status_pk PRIMARY KEY (uuid),
    CONSTRAINT clinician_status_name_key UNIQUE (clinician_status)
);

-- Create index on enabled for filtering
CREATE INDEX idx_clinician_status_enabled ON public.clinician_status(enabled);

-- Create index on clinician_status for searching
CREATE INDEX idx_clinician_status_name ON public.clinician_status(clinician_status);