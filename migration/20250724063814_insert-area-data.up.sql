-- Insert area data
-- This migration populates the area table with initial data

INSERT INTO public.area
("id", "label", enabled, acronym, created_at, updated_at)
VALUES('84ce4d70-a442-412b-bf27-06f4544a8661'::uuid, 'Medicine', true, 'MP', '2025-07-25 13:00:00.000+00', '2025-07-25 13:00:00.000+00');

INSERT INTO public.area
("id", "label", enabled, acronym, created_at, updated_at)
VALUES('4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid, 'Dentistry', true, 'DP', '2025-07-25 13:00:00.000+00', '2025-07-25 13:00:00.000+00');

INSERT INTO public.area
("id", "label", enabled, acronym, created_at, updated_at)
VALUES('e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid, 'Psychology', true, 'TP', '2025-07-25 13:00:00.000+00', '2025-07-25 13:00:00.000+00');
