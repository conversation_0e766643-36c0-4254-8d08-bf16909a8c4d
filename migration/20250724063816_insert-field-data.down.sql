-- Remove field data
-- This migration removes all field data when rolling back

-- Delete all field records in reverse order (remove test data first, then main data)
DELETE FROM public.field WHERE area_uuid = 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid;
DELETE FROM public.field WHERE area_uuid = '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid;
DELETE FROM public.field WHERE area_uuid = '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid;

-- Alternative: Delete all records from field table
-- TRUNCATE TABLE public.field; 