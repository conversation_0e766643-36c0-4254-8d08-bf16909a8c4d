CREATE TABLE price_list_field_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL,
    account_id UUID NOT NULL,
    field_id UUID NOT NULL,
    status_id UUID NOT NULL,
    role_id UUID NOT NULL,
    acronym VA<PERSON>HAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

    -- unique per branch, field, account, and role
    CONSTRAINT unique_branch_field_account_role UNIQUE (branch_id, field_id, account_id, role_id),

    CONSTRAINT fk_field FOREIGN KEY (field_id) REFERENCES field(uuid),
    CONSTRAINT fk_status FOREIGN KEY (status_id) REFERENCES field_status(uuid)
    -- CONSTRAINT fk_account FOREIGN KEY (account_id) REFERENCES users(id) -- uncomment when user/account table is ready
    -- CONSTRAINT fk_role FOR<PERSON><PERSON><PERSON> KEY (role_id) REFERENCES roles(id) -- uncomment when role table is ready
);

-- <PERSON><PERSON> indexes for better query performance
CREATE INDEX idx_price_list_field_status_branch_id ON price_list_field_status(branch_id);
CREATE INDEX idx_price_list_field_status_account_id ON price_list_field_status(account_id);
CREATE INDEX idx_price_list_field_status_field_id ON price_list_field_status(field_id);
CREATE INDEX idx_price_list_field_status_status_id ON price_list_field_status(status_id);
CREATE INDEX idx_price_list_field_status_role_id ON price_list_field_status(role_id);
CREATE INDEX idx_price_list_field_status_created_at ON price_list_field_status(created_at);
