CREATE TABLE IF NOT EXISTS user_role (
    id UUID,
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    branch_id UUID NOT NULL,
    created_at TIMESTAMP default (now() AT TIME ZONE 'UTC') NOT NULL,
    updated_at TIMESTAMP default (now() AT TIME ZONE 'UTC') NOT NULL,

    CONSTRAINT user_role_pk PRIMARY KEY (id),
    CONSTRAINT user_role_role_id_fk FOREIGN KEY (role_id) REFERENCES public.system_role (id)
);

CREATE UNIQUE INDEX IF NOT EXISTS user_role_user_id_branch_id_unique ON user_role (
    user_id,
    branch_id
);
