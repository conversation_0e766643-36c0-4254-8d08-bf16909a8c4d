-- Create field table
CREATE TABLE public.field (
    "uuid" uuid NOT NULL,
    "name" varchar NOT NULL,
    description varchar NULL,
    icon varchar NULL,
    "position" int2 NULL,
    enabled bool NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    area_uuid uuid NULL,
    CONSTRAINT field_name_key UNIQUE (name),
    CONSTRAINT field_pk PRIMARY KEY (uuid),
    CONSTRAINT field_area_fk FOREIGN KEY (area_uuid) REFERENCES public.area("id")
);

-- Create index on area_uuid for better performance
CREATE INDEX idx_field_area_uuid ON public.field(area_uuid);

-- Create index on enabled for filtering
CREATE INDEX idx_field_enabled ON public.field(enabled);

-- Create index on position for ordering
CREATE INDEX idx_field_position ON public.field("position"); 