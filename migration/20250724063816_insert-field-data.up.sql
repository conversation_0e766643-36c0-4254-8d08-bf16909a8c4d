-- Insert field data
-- This migration populates the field table with initial data

-- Medicine Fields (area_uuid: 84ce4d70-a442-412b-bf27-06f4544a8661)
INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('1be15415-015a-462f-b6ac-e83a3e09dadf'::uuid, 'Ophthalmology', NULL, NULL, NULL, true, '2023-03-30 12:01:31.732', '2023-03-30 12:01:31.732', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('691e182c-bd85-4409-bfc6-85e651c8f931'::uuid, 'Anesthesiology', NULL, NULL, NULL, true, '2023-03-30 11:57:57.000', '2023-03-30 11:57:57.000', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c7319e25-7cc1-474b-8c93-b18c56f7a5f3'::uuid, 'Cardiology', NULL, NULL, NULL, true, '2023-03-30 11:58:07.345', '2023-03-30 11:58:07.345', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('08f32485-2c0a-48dc-b2cf-10a29112ba5b'::uuid, 'Critical Care Medicine', NULL, NULL, NULL, true, '2023-03-30 11:58:22.349', '2023-03-30 11:58:22.349', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('1b98f8e3-1e38-48b4-af6f-9a9fb7def04b'::uuid, 'Dermatology', NULL, NULL, NULL, true, '2023-03-30 11:58:32.100', '2023-03-30 11:58:32.100', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('28014f8b-67a0-4349-bdce-a063df41d72a'::uuid, 'Emergency Medicine', NULL, NULL, NULL, true, '2023-03-30 11:59:03.380', '2023-03-30 11:59:03.380', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('64c04ad4-d489-4504-ab4c-41b6299036f3'::uuid, 'Endocrinology', NULL, NULL, NULL, true, '2023-03-30 11:59:17.617', '2023-03-30 11:59:17.617', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('1afc461e-d1b4-4364-b498-5ddab562e2ff'::uuid, 'Family Medicine', NULL, NULL, NULL, true, '2023-03-30 11:59:32.775', '2023-03-30 11:59:32.775', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('1168ae18-287c-4b0f-8e95-ea18896e45a7'::uuid, 'Gastroenterology', NULL, NULL, NULL, true, '2023-03-30 11:59:41.233', '2023-03-30 11:59:41.233', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('b95258e6-e2b3-4cd6-b320-82395e40cd20'::uuid, 'Geriatric Medicine', NULL, NULL, NULL, true, '2023-03-30 11:59:49.947', '2023-03-30 11:59:49.947', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('9219f774-726f-4493-8d63-de06d0120fb2'::uuid, 'Hematology', NULL, NULL, NULL, true, '2023-03-30 11:59:58.310', '2023-03-30 11:59:58.310', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('bf9e50bc-db59-44aa-9c7c-32cf9f7750b7'::uuid, 'Internal Medicine', NULL, NULL, NULL, true, '2023-03-30 12:00:16.118', '2023-03-30 12:00:16.118', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c49a78dc-5e47-4d22-a87c-9504708f2a3a'::uuid, 'Medical Genetics', NULL, NULL, NULL, true, '2023-03-30 12:00:33.964', '2023-03-30 12:00:33.964', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c73f4118-bc3c-4189-9234-0600e8a9fda8'::uuid, 'Neurology', NULL, NULL, NULL, true, '2023-03-30 12:00:50.248', '2023-03-30 12:00:50.248', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c7168628-0596-4635-a215-d14638a1a2d0'::uuid, 'Neurosurgery', NULL, NULL, NULL, true, '2023-03-30 12:00:57.027', '2023-03-30 12:00:57.027', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('1423a8c0-71c3-45db-923a-f338f94324dc'::uuid, 'Nuclear Medicine', NULL, NULL, NULL, true, '2023-03-30 12:01:08.190', '2023-03-30 12:01:08.190', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('faf1ab53-1c66-4289-a6cf-d2c1c97cf048'::uuid, 'Obstetrics and Gynecology', NULL, NULL, NULL, true, '2023-03-30 12:01:20.560', '2023-03-30 12:01:20.560', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('f5173634-f90d-4707-a1ed-6ba562b66426'::uuid, 'Oncology', NULL, NULL, NULL, true, '2023-03-30 12:01:26.392', '2023-03-30 12:01:26.392', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('ae633a2b-03f1-436a-b42d-e1290bbd5a02'::uuid, 'Otolaryngology', NULL, NULL, NULL, true, '2023-03-30 12:01:39.016', '2023-03-30 12:01:39.016', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('722501ef-667d-45ab-b06c-24a5fcf6369e'::uuid, 'Pathology', NULL, NULL, NULL, true, '2023-03-30 12:01:45.982', '2023-03-30 12:01:45.982', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('45349a4d-0a35-4683-9578-9b846749a176'::uuid, 'Pediatrics', NULL, NULL, NULL, true, '2023-03-30 12:01:52.513', '2023-03-30 12:01:52.513', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('95a576a4-52d2-48e5-9466-dc45eaf25512'::uuid, 'Plastic Surgery', NULL, NULL, NULL, true, '2023-03-30 12:02:16.935', '2023-03-30 12:02:16.935', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('6babc091-1c59-4c02-8057-e982bb9fd78b'::uuid, 'Preventive Medicine', NULL, NULL, NULL, true, '2023-03-30 12:02:26.501', '2023-03-30 12:02:26.501', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('0ccffc25-d330-4301-b919-ccd674a07f58'::uuid, 'Psychiatry', NULL, NULL, NULL, true, '2023-03-30 12:02:33.202', '2023-03-30 12:02:33.202', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('cf455e68-a420-4f13-902f-e425da2b3d81'::uuid, 'Rheumatology', NULL, NULL, NULL, true, '2023-03-30 12:02:39.348', '2023-03-30 12:02:39.348', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('2242d94d-e41b-4183-b984-c94fbee39066'::uuid, 'Surgery', NULL, NULL, NULL, true, '2023-03-30 12:02:59.764', '2023-03-30 12:02:59.764', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('3755f536-fcbc-42b7-bef2-c810d21ef5a2'::uuid, 'Urology', NULL, NULL, NULL, true, '2023-03-30 12:03:05.509', '2023-03-30 12:03:05.509', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c1e3e0df-f7e5-4b8e-a0c3-ab9fe6224559'::uuid, 'Orthopedics', NULL, NULL, NULL, true, '2023-11-19 12:07:53.476', '2023-11-19 12:07:53.476', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('615f3741-2c45-42e3-ba7f-91f82f394952'::uuid, 'Pulmonology', NULL, NULL, NULL, true, '2023-11-19 12:09:41.416', '2023-11-19 12:09:41.416', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('141f282d-dc33-4073-9468-840dd34e049f'::uuid, 'Radiology', NULL, NULL, NULL, true, '2023-11-19 12:10:11.226', '2023-11-19 12:10:11.226', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('b9bcf0c5-eb33-4c9b-9a14-1753f1fb819d'::uuid, 'Sports Medicine', NULL, NULL, NULL, true, '2023-11-19 12:12:48.358', '2023-11-19 12:12:48.358', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('ba82439c-2b82-4b13-a7f5-574e64cbca71'::uuid, 'Allergy and Immunology', NULL, NULL, NULL, true, '2023-03-30 09:39:21.159', '2024-05-31 07:32:19.833', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('a83437a7-5c5d-4396-8117-c460a0d8234f'::uuid, 'Diagnostic Radiology', NULL, NULL, NULL, true, '2023-03-30 11:58:45.316', '2023-03-30 11:58:45.316', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('57530e6e-a4de-4048-a852-44dc5ce199e1'::uuid, 'Nephrology', NULL, NULL, NULL, true, '2023-03-30 12:00:40.643', '2023-03-30 12:00:40.643', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('06e53d2e-77e5-4524-ab81-9caa2ebff002'::uuid, 'Physical Medicine and Rehabilitation', NULL, NULL, NULL, true, '2023-03-30 12:02:10.868', '2023-03-30 12:02:10.868', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('6b9f80ec-52c7-4b08-abff-6658b9b27d90'::uuid, 'Radiation Oncology', NULL, NULL, NULL, true, '2023-11-19 12:11:41.018', '2023-11-19 12:11:41.018', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

-- Test Medicine Fields
INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('4c4ade44-939e-4efc-953a-25d7b18664f4'::uuid, 'Test Medicine 1', 'medicine', 'medicine', 1, true, '2024-07-02 10:04:50.962', '2024-07-02 10:04:50.962', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('bff7ca06-5bc4-4cee-a672-0597b2ccc7b9'::uuid, 'A Test 1', 'medicine', 'medicine', 1, false, '2025-03-06 12:31:35.016', '2025-04-11 09:25:39.902', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('2082b00b-50fd-4f1c-b768-555a00d8de00'::uuid, 'Test', 'medicine', 'medicine', 1, false, '2025-03-06 12:18:03.332', '2025-03-06 12:18:03.332', '84ce4d70-a442-412b-bf27-06f4544a8661'::uuid);

-- Dentistry Fields (area_uuid: 4beed17b-a38a-4da1-8b26-94d2f1513001)
INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('3135e655-afd4-4bb7-ba0e-4d47cfd9bd75'::uuid, 'Aesthetic Dentistry', 'Smile design and dental aesthetic treatments', 'AestheticDentistryPreMaps', 1, true, '2023-02-15 11:41:41.368', '2024-05-31 07:32:03.103', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('71ba1430-0c47-4af0-bbc7-966f79059316'::uuid, 'Dental Hygiene', 'Recognition, treatment and prevention of oral diseases', 'Dental_Hygiene', 2, true, '2023-02-15 11:41:41.671', '2023-02-15 11:41:41.671', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('8b0a6c1a-ca69-4567-815c-647b6f1e0f92'::uuid, 'Endodontics', 'Root canal treatments and those related to the dental pulp', 'Endodontics', 3, true, '2023-02-15 11:41:41.971', '2023-02-15 11:41:41.971', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('aca6db40-1469-482a-b906-9c5b8beae914'::uuid, 'General Dentistry', 'Diagnosis, treatment and prevention of overall oral health disease', 'BondingConsultation', 4, true, '2023-02-15 11:41:42.268', '2023-02-15 11:41:42.268', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('af6bd426-23f9-443e-866e-d0fe3b7b811b'::uuid, 'Implant Dentistry', 'Rehabilitation of missing teeth with dental implants', 'ImplantDentistry', 5, true, '2023-02-15 11:41:42.569', '2023-02-15 11:41:42.569', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('9a6467b1-2e89-456f-97c0-47bca24438e3'::uuid, 'Occlusion & TMJ', 'Treatment of the bite and temporomandibular joint ', 'OcclusionTMJ', 6, true, '2023-02-15 11:41:42.864', '2023-02-15 11:41:42.864', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('40772236-3be5-4110-bdd3-1d6a4b6d341e'::uuid, 'Oral & Maxillofacial Surgery', 'Surgical & reconstructive treatments of the mouth and face', 'OralSurgery', 7, true, '2023-02-15 11:41:43.161', '2023-02-15 11:41:43.161', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('6bef4260-8772-4e55-bf91-f6ca85e5f036'::uuid, 'Oral Medicine', 'Treatment of mouth & maxillofacial diseases', 'DentalConsultation', 8, true, '2023-02-15 11:41:43.458', '2023-02-15 11:41:43.458', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('38fbb9a5-40a6-4faf-a3a1-9ef31d792c0e'::uuid, 'Oral Surgery', 'Surgical treatments of the mouth and jaws', 'Oral_Surgery', 9, true, '2023-02-15 11:41:43.755', '2023-02-15 11:41:43.755', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c75efbca-e31c-441b-97d5-8dc3693bc7c0'::uuid, 'Orthodontics', 'Treatment of malpositioned teeth and jaws', 'Orthodontics', 10, true, '2023-02-15 11:41:44.051', '2023-02-15 11:41:44.051', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('3eb9c52f-5686-4316-93c2-c4e71e62a14c'::uuid, 'Pediatric Dentistry', 'Treatment of oral diseases of infants, children and teenagers', 'Dentist1', 11, true, '2023-02-15 11:41:44.347', '2023-02-15 11:41:44.347', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('f63d0d24-1049-469d-81dc-e50b38e1acea'::uuid, 'Periodontology', 'Treatment of periodontal disease, including gum and bone problems ', 'PeriodonticsConsultation', 12, true, '2023-02-15 11:41:44.647', '2023-02-15 11:41:44.647', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('e5e54691-1677-4869-a2ec-8cfee5e19650'::uuid, 'Prosthodontics', 'Rehabilitation of damaged or missing teeth with dental prostheses', 'ProsthodonticConsultation', 13, true, '2023-02-15 11:41:44.943', '2023-02-15 11:41:44.943', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('ac607676-e5aa-4d3e-97bd-74fc024d57f6'::uuid, 'Restorative Dentistry', 'Rehabilitation of damaged teeth with dental restorative treatments', 'RestorativeConsultation', 14, true, '2023-02-15 11:41:45.235', '2023-02-15 11:41:45.235', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

-- Test Dentistry Fields
INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('0ee00d9f-c8e0-4c53-be28-241ef1926f37'::uuid, 'test', 'dentistry', 'in', 1, false, '2025-03-06 11:57:43.716', '2025-03-06 11:57:43.716', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('b55ffdb4-1a66-4edc-8107-119117d8e2b6'::uuid, 'test 2', 'dentistry', '1', 1, false, '2025-03-06 12:12:03.970', '2025-03-06 12:12:03.970', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('e2fd639d-8c27-41c1-8315-a09bf784e708'::uuid, 'A Test', 'dentistry', 'a', 1, false, '2025-03-06 12:13:03.235', '2025-03-06 12:13:17.583', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('7ced3ba5-9cc2-4caa-9ba7-2a6c3a37ce2b'::uuid, 'AAAAAAAA', 'dentistry', 'a', 1, false, '2024-07-02 10:07:27.926', '2025-03-06 12:10:37.946', '4beed17b-a38a-4da1-8b26-94d2f1513001'::uuid);

-- Psychology Fields (area_uuid: e117dcf1-4acd-499f-80d2-7c868f23d6d0)
INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('311bff6b-4cb8-4871-9f9e-9109a87411f3'::uuid, 'Addiction Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('61848827-5b81-42c8-93f3-d160f8ff54d2'::uuid, 'Child and Adolescent Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('74f62d3a-ccac-4617-8ef7-07252008c9bd'::uuid, 'Clinical Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('6bfc0ef5-f447-4fc8-b1d5-5a625505fd02'::uuid, 'Counseling Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('d14b7a67-8e38-4982-9126-66a517568380'::uuid, 'Developmental Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('2bfd6157-6efe-47ed-a93a-3c5acbeca73d'::uuid, 'Educational Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('bdd746fb-4089-426d-901d-df2b6525c812'::uuid, 'Experimental Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('62c50a8d-aaf7-4924-956b-3767ca96d282'::uuid, 'Forensic Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('f21de48d-3553-41ed-848a-d9f7de5008f5'::uuid, 'Geriatric Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('e8a5a947-d1c9-4399-b194-be55e91d431a'::uuid, 'Health Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('fe0efd69-5263-412f-ab22-cc453dfbbdb5'::uuid, 'Industrial-Organizational Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('8242fae6-76c1-49c6-b7fb-cc0ffdc7df7e'::uuid, 'Life Coaching', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('415e6d30-3297-4283-a37c-f958c3f3b174'::uuid, 'Marriage and Family Therapy', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('d1a25607-f897-4836-b53b-4a4fc2becd0d'::uuid, 'Neuropsychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('eb6a5f3b-d6a2-4396-b925-44fe2079ea92'::uuid, 'Psychotherapy', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('31c90cac-c49e-43a1-b8ba-244a58b52705'::uuid, 'Rehabilitation Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('3e06f028-8248-406c-80dc-660976be0730'::uuid, 'Sexology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('e5f0d4c1-fd6b-4981-ac80-76e2dfecac99'::uuid, 'Social Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('eebc247c-aded-4637-92a0-25fbadf90f7b'::uuid, 'Sports Psychology', NULL, NULL, NULL, true, '2024-05-31 15:50:51.492', '2024-05-31 15:50:51.492', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

-- Test Psychology Fields
INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('b70cc6fa-86fc-4507-9cb1-4f63919d957f'::uuid, 'test 22', 'psychologist', 'psychologist', 1, false, '2025-03-06 12:33:49.953', '2025-04-02 13:09:04.029', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c4f6e466-9082-42eb-9a8a-185ef0357f5a'::uuid, 'a test', 'psychologist', 'psychologist', 1, false, '2025-03-06 12:38:30.664', '2025-03-06 12:38:33.871', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('844c824a-fb9a-4cb2-a7a4-41f30957cc1c'::uuid, 'aaaaaaaaa', 'psychologist', 'psychologist', 1, false, '2024-07-02 10:44:17.713', '2024-07-02 15:38:44.997', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid);

INSERT INTO public.field
("uuid", "name", description, icon, "position", enabled, created_at, updated_at, area_uuid)
VALUES('c48ff75a-95cc-458a-945f-52ff546cd0d4'::uuid, 'Lucas Name XXX', 'psychologist', 'psychologist', 1, false, '2024-06-28 16:25:45.440', '2025-04-02 13:09:13.002', 'e117dcf1-4acd-499f-80d2-7c868f23d6d0'::uuid); 