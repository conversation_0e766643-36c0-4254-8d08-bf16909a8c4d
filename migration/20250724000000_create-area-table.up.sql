-- Create area table
CREATE TABLE public.area (
    "id" uuid NOT NULL,
    "label" varchar NOT NULL,
    enabled bool NOT NULL DEFAULT true,
    acronym varchar NOT NULL,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    CONSTRAINT area_pk PRIMARY KEY (id),
    CONSTRAINT area_unique UNIQUE (acronym)
);

-- Create index on enabled for filtering
CREATE INDEX idx_area_enabled ON public.area(enabled);

-- Create index on acronym for searching
CREATE INDEX idx_area_acronym ON public.area(acronym);
