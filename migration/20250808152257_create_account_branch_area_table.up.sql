CREATE TABLE public.account_branch_area (
	id uuid PRIMARY KEY,
	account_id uuid NOT NULL,
	branch_id uuid NOT NULL,
	area_id uuid NOT NULL,
	created_at timestamp with time zone NOT NULL DEFAULT now(),
	updated_at timestamp with time zone NOT NULL DEFAULT now(),
	CONSTRAINT account_branch_area_branch_id_area_id_key UNIQUE (branch_id, area_id),
	CONSTRAINT area_id_fk FOREIGN KEY (area_id) REFERENCES public.area("id") ON DELETE CASCADE
);

-- Create index for better query performance
CREATE INDEX idx_account_branch_area_account_id ON public.account_branch_area(account_id);
CREATE INDEX idx_account_branch_area_branch_id ON public.account_branch_area(branch_id);
CREATE INDEX idx_account_branch_area_area_id ON public.account_branch_area(area_id);
