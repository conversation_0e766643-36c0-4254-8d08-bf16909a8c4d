-- Create appointment_type table
CREATE TABLE public.appointment_type (
    "uuid" uuid NOT NULL,
    appointment_type varchar NOT NULL,
    enabled bool NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    CONSTRAINT appointment_type_pk PRIMARY KEY (uuid),
    CONSTRAINT appointment_type_name_key UNIQUE (appointment_type)
);

-- Create index on enabled for filtering
CREATE INDEX idx_appointment_type_enabled ON public.appointment_type(enabled);

-- Create index on appointment_type for searching
CREATE INDEX idx_appointment_type_name ON public.appointment_type(appointment_type);
