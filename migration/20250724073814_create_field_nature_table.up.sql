CREATE TABLE public.field_nature (
	field_uuid uuid NOT NULL,
	nature_uuid uuid NOT NULL,
	"position" int2 NOT NULL,
	CONSTRAINT field_nature_field_uuid_nature_uuid_key UNIQUE (field_uuid, nature_uuid),
	CONSTRAINT field_uuid_fk FOREIGN KEY (field_uuid) REFERENCES public.field("uuid") ON DELETE CASCADE,
	CONSTRAINT nature_uuid_fk FOREIGN KEY (nature_uuid) REFERENCES public.nature("uuid") ON DELETE CASCADE
); 