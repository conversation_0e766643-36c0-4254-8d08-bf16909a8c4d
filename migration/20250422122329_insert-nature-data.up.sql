INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('171977db-0846-4dac-af9f-30d6a43f3dac'::uuid, 'Smile Consultation', 'A consultation regarding the smileand dental aesthetic treatment options', 'SmileConsultation', true, '2023-02-15 11:41:54.338', '2023-02-15 11:41:54.338');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('5fc76a15-e2a9-4442-89ee-7cc8e1134de7'::uuid, 'Veneers Consultation', 'A consultation regarding treatment with porcelain veneers', 'VeneersConsultation', true, '2023-02-15 11:41:54.636', '2023-02-15 11:41:54.636');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('0a73ed11-da6f-428c-b48a-0c54a299972d'::uuid, 'Bonding Consultation', 'A consultation regarding treatment with composite veneers', 'BondingConsultation', true, '2023-02-15 11:41:54.937', '2023-02-15 11:41:54.937');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('d6c3b196-84ee-4452-8c7f-3c64bc0d09c1'::uuid, 'Whitening Consultation', 'A consultation regarding teeth whitening Treatment', 'WhiteningConsultation', true, '2023-02-15 11:41:55.232', '2023-02-15 11:41:55.232');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('f05b32f0-6c20-435b-aab7-61381087add7'::uuid, 'Emergency Pain', 'An emergency consultation regarding a dental aesthetic problem, with pain', 'EmergencyPain', true, '2023-02-15 11:41:56.118', '2023-02-15 11:41:56.118');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('2fe0ec5e-9c85-45e6-a2d7-0551d37141b3'::uuid, 'Emergency Non-Pain', 'An emergency consultation regarding a dental aesthetic problem, with no pain', 'EmergencyNonPain', true, '2023-02-15 11:41:56.414', '2023-02-15 11:41:56.414');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('6e1654d1-e5c1-4b4d-b401-6df71a18408a'::uuid, '2nd Opinion Consultation', 'A consultation to ask for a 2nd opinion regarding a past, ongoing or future case', 'Dentist1', true, '2023-02-15 11:41:55.825', '2023-02-15 11:41:55.825');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('*************-41da-8241-991f159de4bc'::uuid, 'Endodontic Consultation', 'A consultation regarding root canal and other dental pulp related problems', 'Endodontics', true, '2023-02-15 11:41:56.707', '2023-02-15 11:41:56.707');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('567b6d17-1733-476d-bea9-16eeb967a5a0'::uuid, 'Dental Consultation', 'A consultation regarding dental health concerns, problems and treatments', 'DentalConsultation', true, '2023-02-15 11:42:02.028', '2023-02-15 11:42:02.028');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('809f3a80-2ca7-488e-9322-494e371f13f3'::uuid, 'Invisible Braces Consultation', 'A consultation regarding orthodontic treatment with invisible braces', 'SmileConsultation', true, '2023-02-15 11:41:59.952', '2023-02-15 11:41:59.952');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('3d9e9771-221b-48c6-807a-ab9c0368b601'::uuid, 'Classic Braces Consultation', 'A consultation regarding orthodontic treatment with train-track braces', 'Orthodontics', true, '2023-02-15 11:42:00.247', '2023-02-15 11:42:00.247');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('39cc44aa-cb4f-4e7e-9583-b58ae89c65bf'::uuid, 'Lingual Braces Consultation', 'A consultation regarding orthodontic treatment with lingual braces', 'Orthodontics', true, '2023-02-15 11:42:00.543', '2023-02-15 11:42:00.543');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('06b96726-e0a6-43c6-ace4-18508a34d33a'::uuid, 'Aesthetic Consultation', 'A consultation regarding ceramic crowns, onlays, white fillings etc', 'AestheticDentistryPreMaps', true, '2023-02-15 11:41:55.527', '2023-02-15 11:41:55.527');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('f48d551f-cb97-429e-bf71-35ed65294a8f'::uuid, 'Oral Health Education', 'A consultation about oral health and prevention of disease', 'AestheticDentistryPreMaps', true, '2023-02-15 11:41:57.884', '2023-02-15 11:41:57.884');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('2d2401b6-5262-4b87-b2fb-dfe93f3fd06a'::uuid, 'Dental Check-Up', 'A routine consultation aiming at checking your overall mouth condition', 'CheckUp3dMouth', true, '2023-02-15 11:42:02.321', '2023-02-15 11:42:02.321');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('e6e92476-b329-46fb-a072-4a974e4dffc7'::uuid, 'Implant Consultation', 'A consultation regarding missing teeth and dental implants', 'DentalConsultation', true, '2023-02-15 11:41:58.182', '2023-02-15 11:41:58.182');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('28d58437-53ce-409e-9cbe-70f9dc3266be'::uuid, 'MaxFac Consultation', 'A consultation regarding surgery & reconstruction of the mouth and face', 'DentalConsultation', true, '2023-02-15 11:41:58.478', '2023-02-15 11:41:58.478');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('8ac8cd0f-246f-4cf3-b17d-985491c8df3a'::uuid, 'Oral Medicine Consultation', 'A consultation regarding diseases of the mouth and maxillofacial region', 'DentalConsultation', true, '2023-02-15 11:41:58.770', '2023-02-15 11:41:58.770');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('d67fd8f4-b6d0-4c96-b5af-2964b7ee1cb0'::uuid, 'Oral Surgery Consultation', 'A consultation regarding surgical treatments of the mouth and jaws', 'DentalConsultation', true, '2023-02-15 11:41:59.068', '2023-02-15 11:41:59.068');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('d34961e3-f6d8-41b6-8387-0cdd6923a17e'::uuid, 'Occlusion Consultation', 'A consultation regarding problems of the bite and temporomandibular joint (TMJ)', 'DentalConsultation', true, '2023-02-15 11:41:59.362', '2023-02-15 11:41:59.362');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('a8a0c26b-5da6-4edd-a72d-46e9600db720'::uuid, 'Orthodontic Consultation', 'A consultation regarding malpositioned teeth and jaws, to discuss all options', 'DentalConsultation', true, '2023-02-15 11:41:59.655', '2023-02-15 11:41:59.655');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('cd630025-4829-4492-b2da-a8b523f9656d'::uuid, 'Pediatric Dentistry Consultation', 'A consultation regarding the oral health of infants, children and teenagers', 'DentalConsultation', true, '2023-02-15 11:42:00.838', '2023-02-15 11:42:00.838');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('fa7ee61a-dd6c-44fc-9542-34e6edaacc6f'::uuid, 'Periodontics Consultation', 'A consultation regarding gum disease, bone loss and other periodontal problems', 'DentalConsultation', true, '2023-02-15 11:42:01.130', '2023-02-15 11:42:01.130');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('b43dbcc1-5bf2-4ff0-9859-43fcf79e0a9d'::uuid, 'Prosthodontic Consultation', 'A consultation regarding damaged or missing teeth and dental prostheses', 'DentalConsultation', true, '2023-02-15 11:42:01.424', '2023-02-15 11:42:01.424');

INSERT INTO public.nature
("uuid", "name", description, icon, enabled, created_at, updated_at)
VALUES('e9efffa5-307e-43d0-8f6c-3c2829653e68'::uuid, 'Restorative Dentistry Consultation', 'A consultation regarding damaged teeth and dental restorative treatments', 'DentalConsultation', true, '2023-02-15 11:42:01.731', '2023-02-15 11:42:01.731'); 