package database

import (
	"context"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq" // import for postgres driver
)

type DriverName string

var (
	Postgres DriverName = "postgres"
)

func (d DriverName) String() string {
	return string(d)
}

type conn struct {
	driverName string
	dsn        string

	db *sqlx.DB
}

func New(driver DriverName, dsn string) (*conn, error) {
	return &conn{
		driverName: driver.String(),
		dsn:        dsn,
	}, nil
}

// Open establishes connection to the Postgres database using sqlx
func (c *conn) Open(ctx context.Context) (*sqlx.DB, error) {
	db, err := sqlx.Open(c.driverName, c.dsn)
	if err != nil {
		return nil, err
	}

	if err := db.PingContext(ctx); err != nil {
		return nil, err
	}

	c.db = db

	return db, nil
}

// Close it's a wrapper to call close on a database with defer
func (c *conn) Close() error {
	return c.db.Close()
}
