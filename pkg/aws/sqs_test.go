package aws

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewSQSClient_Success(t *testing.T) {
	config := SQSConfig{
		Region:          "us-east-1",
		AccessKeyID:     "test-access-key",
		SecretAccessKey: "test-secret-key",
		QueueURL:        "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
	}

	client, err := NewSQSClient(config)

	require.NoError(t, err)
	assert.NotNil(t, client)
	assert.Equal(t, config.Region, client.region)
	assert.Equal(t, config.AccessKeyID, client.accessKeyID)
	assert.Equal(t, config.SecretAccess<PERSON>ey, client.secretAccessKey)
	assert.Equal(t, config.QueueURL, client.queueURL)
}

func TestNewSQSClient_MissingRegion(t *testing.T) {
	config := SQSConfig{
		AccessKeyID:     "test-access-key",
		SecretAccess<PERSON>ey: "test-secret-key",
		QueueURL:        "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
	}

	client, err := NewSQSClient(config)

	assert.Error(t, err)
	assert.Nil(t, client)
	assert.Contains(t, err.Error(), "AWS region is required")
}

func TestNewSQSClient_MissingAccessKey(t *testing.T) {
	config := SQSConfig{
		Region:          "us-east-1",
		SecretAccessKey: "test-secret-key",
		QueueURL:        "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
	}

	client, err := NewSQSClient(config)

	assert.Error(t, err)
	assert.Nil(t, client)
	assert.Contains(t, err.Error(), "AWS access key ID is required")
}

func TestNewSQSClient_MissingSecretKey(t *testing.T) {
	config := SQSConfig{
		Region:      "us-east-1",
		AccessKeyID: "test-access-key",
		QueueURL:    "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
	}

	client, err := NewSQSClient(config)

	assert.Error(t, err)
	assert.Nil(t, client)
	assert.Contains(t, err.Error(), "AWS secret access key is required")
}

func TestNewSQSClient_MissingQueueURL(t *testing.T) {
	config := SQSConfig{
		Region:          "us-east-1",
		AccessKeyID:     "test-access-key",
		SecretAccessKey: "test-secret-key",
	}

	client, err := NewSQSClient(config)

	assert.Error(t, err)
	assert.Nil(t, client)
	assert.Contains(t, err.Error(), "SQS queue URL is required")
}

func TestSQSClient_GetQueueURL(t *testing.T) {
	config := SQSConfig{
		Region:          "us-east-1",
		AccessKeyID:     "test-access-key",
		SecretAccessKey: "test-secret-key",
		QueueURL:        "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
	}

	client, err := NewSQSClient(config)
	require.NoError(t, err)

	queueURL := client.GetQueueURL()
	assert.Equal(t, config.QueueURL, queueURL)
}

func TestSQSClient_Close(t *testing.T) {
	config := SQSConfig{
		Region:          "us-east-1",
		AccessKeyID:     "test-access-key",
		SecretAccessKey: "test-secret-key",
		QueueURL:        "https://sqs.us-east-1.amazonaws.com/123456789012/test-queue",
	}

	client, err := NewSQSClient(config)
	require.NoError(t, err)

	err = client.Close()
	assert.NoError(t, err)
	assert.Nil(t, client.client)
}
