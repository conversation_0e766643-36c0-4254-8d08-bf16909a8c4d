package core

import (
	"strings"
)

type IgniteRaceType string

const (
	IgniteRaceTypePro      IgniteRaceType = "pro"
	IgniteRaceTypeUser     IgniteRaceType = "user"
	IgniteRaceTypeBrand    IgniteRaceType = "brand"
	IgniteRaceTypeBusiness IgniteRaceType = "business"
)

func (i IgniteRaceType) Value() string {
	return string(i)
}

func NewIgniteRaceType(i string) (*IgniteRaceType, error) {
	if i == "" {
		return nil, NewBusinessError("ignite race type cannot be empty")
	}
	switch strings.ToLower(i) {
	case IgniteRaceTypePro.Value(), IgniteRaceTypeUser.Value(), IgniteRaceTypeBrand.Value(), IgniteRaceTypeBusiness.Value():
		out := IgniteRaceType(i)
		return &out, nil
	default:
		return nil, NewBusinessError("invalid ignite type")
	}
}

func (i IgniteRaceType) IsValid() bool {
	switch i {
	case IgniteRaceTypePro, IgniteRaceTypeUser, IgniteRaceTypeBrand, IgniteRaceTypeBusiness:
		return true
	default:
		return false
	}
}
