package core

import (
	"errors"
	"fmt"
	"strings"
)

var ErrInvalidGroupRepresentativeRole = NewBusinessError("invalid group representative role")

type RepresentativeRole struct {
	value string
}

func NewRepresentativeRole(v string) (*RepresentativeRole, error) {
	n := strings.TrimSpace(v)
	if n == "" {
		return nil, errors.Join(ErrInvalidGroupRepresentativeRole, fmt.Errorf("representative role must not be empty"))
	}

	return &RepresentativeRole{
		value: v,
	}, nil
}

func (n RepresentativeRole) Value() string {
	return n.value
}
