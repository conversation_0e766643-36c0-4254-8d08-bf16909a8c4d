package core

import (
	"errors"
	"fmt"
	"strings"
)

var ErrInvalidGroupRepresentativeName = NewBusinessError("invalid group representative name")

type RepresentativeName struct {
	value string
}

func NewRepresentativeName(v string) (*RepresentativeName, error) {
	n := strings.TrimSpace(v)
	if n == "" {
		return nil, errors.Join(ErrInvalidGroupRepresentativeName, fmt.Errorf("representative name must not be empty"))
	}

	return &RepresentativeName{
		value: v,
	}, nil
}

func (n RepresentativeName) Value() string {
	return n.value
}
