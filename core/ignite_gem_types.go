package core

const (
	IgniteGemTypePro      IgniteGemType = "pro"
	IgniteGemTypeUser     IgniteGemType = "user"
	IgniteGemTypeBrand    IgniteGemType = "brand"
	IgniteGemTypeBusiness IgniteGemType = "business"
)

type IgniteGemType string

func (i IgniteGemType) Value() string {
	return string(i)
}

func NewIgniteGemType(i string) (*IgniteGemType, error) {
	switch i {
	case IgniteGemTypePro.Value(), IgniteGemTypeUser.Value(), IgniteGemTypeBrand.Value(), IgniteGemTypeBusiness.Value():
		out := IgniteGemType(i)
		return &out, nil
	default:
		return nil, NewBusinessError("invalid ignite type")
	}
}

func (i IgniteGemType) IsValid() bool {
	switch i {
	case IgniteGemTypePro, IgniteGemTypeUser, IgniteGemTypeBrand, IgniteGemTypeBusiness:
		return true
	default:
		return false
	}
}
