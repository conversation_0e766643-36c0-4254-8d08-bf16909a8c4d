package core

import (
	"regexp"
	"strings"
)

var (
	ErrInvalidEmail = NewBusinessError("invalid e-mail")
)

type Email struct {
	value string
}

func NewEmail(v string) (*Email, error) {
	v = strings.TrimSpace(v)
	if v == "" {
		return nil, ErrInvalidEmail
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(v) {
		return nil, ErrInvalidEmail
	}

	return &Email{value: v}, nil
}

func (p Email) Value() string {
	return p.value
}
