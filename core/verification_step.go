package core

type VerificationStep string

const (
	StepRevisionSubmission VerificationStep = "S1R"
	StepInitialSubmission  VerificationStep = "S1"
)

func NewVerificationStep(step string) (VerificationStep, error) {
	switch step {
	case string(StepRevisionSubmission), string(StepInitialSubmission):
		return VerificationStep(step), nil
	default:
		return "", NewBusinessError("invalid step type")
	}
}

func (s VerificationStep) IsValid() bool {
	return s == StepRevisionSubmission || s == StepInitialSubmission
}

func (s VerificationStep) String() string {
	return string(s)
}
