package core

import "fmt"

var (
	ErrInvalidFieldGroup = NewBusinessError("invalid field group")
)

type FieldGroup string

const (
	NopGroup       FieldGroup = "nop_group"
	Profile        FieldGroup = "profile"
	License        FieldGroup = "license"
	Representative FieldGroup = "representative"
)

var fieldGroups = map[string]FieldGroup{
	string(Profile):        Profile,
	string(License):        License,
	string(Representative): Representative,
}

func NewFieldGroupFromString(v string) (FieldGroup, error) {
	g, ok := fieldGroups[v]
	if !ok {
		return NopGroup, fmt.Errorf("invalid group %s %w", v, ErrInvalidFieldGroup)
	}

	return g, nil
}

func (f FieldGroup) Value() string {
	return string(f)
}
