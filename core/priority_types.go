package core

type Priority string

const (
	PriorityFrozen     Priority = "frozen"
	PriorityLow        Priority = "low"
	PriorityMedium     Priority = "medium"
	PriorityHigh       Priority = "high"
	PriorityInProgress Priority = "in_progress"
	PriorityCompleted  Priority = "completed"
)

func NewPriority(p string) (Priority, error) {
	switch p {
	case string(PriorityFrozen), string(PriorityLow), string(PriorityMedium), string(PriorityHigh), string(PriorityInProgress), string(PriorityCompleted):
		return Priority(p), nil
	default:
		return "", NewBusinessError("invalid priority type")
	}
}

func (p Priority) IsValid() bool {
	return p == PriorityLow ||
		p == PriorityMedium ||
		p == PriorityHigh ||
		p == PriorityInProgress ||
		p == PriorityCompleted || p == PriorityFrozen
}

func (p Priority) String() string {
	return string(p)
}
