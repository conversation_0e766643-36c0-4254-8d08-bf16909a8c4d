package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVerificationStep(t *testing.T) {
	assert.True(t, StepInitialSubmission.IsValid())
	assert.True(t, StepRevisionSubmission.IsValid())
	assert.False(t, VerificationStep("invalid_step").IsValid())

	assert.Equal(t, "S1", StepInitialSubmission.String())
	assert.Equal(t, "S1R", StepRevisionSubmission.String())

	step, err := NewVerificationStep("S1")
	assert.NoError(t, err)
	assert.Equal(t, StepInitialSubmission, step)

	step, err = NewVerificationStep("S1R")
	assert.NoError(t, err)
	assert.Equal(t, StepRevisionSubmission, step)

	step, err = NewVerificationStep("invalid_step")
	assert.<PERSON>rror(t, err)
	assert.Equal(t, VerificationStep(""), step)
}
