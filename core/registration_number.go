package core

import (
	"errors"
	"fmt"
	"strings"
)

var (
	ErrInvalidRegistrationNumber = NewBusinessError("invalid registration number")
)

type RegistrationNumber struct {
	value string
}

func NewRegistrationNumber(v string) (*RegistrationNumber, error) {
	v = strings.TrimSpace(v)
	if len(v) < 5 {
		return nil, errors.Join(ErrInvalidRegistrationNumber, fmt.Errorf("regstration number must have at least 5 characters"))
	}

	return &RegistrationNumber{
		value: v,
	}, nil
}

func (r RegistrationNumber) Value() string {
	return r.value
}
