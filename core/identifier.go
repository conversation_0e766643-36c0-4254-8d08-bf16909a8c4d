package core

import (
	"errors"
	"strings"

	"github.com/google/uuid"
)

var (
	ErrInvalidIdentifier = NewBusinessError("invalid identifier")
)

type Identifier struct {
	value string
}

func NewID() *Identifier {
	return &Identifier{
		value: uuid.NewString(),
	}
}

func NewIDFromString(v string) (*Identifier, error) {
	if strings.TrimSpace(v) == "" {
		return nil, errors.Join(ErrInvalidIdentifier, errors.New("empty value for identifier"))
	}

	parsedUUID, err := uuid.Parse(v)
	if err != nil {
		return nil, errors.Join(ErrInvalidIdentifier, err)
	}

	return &Identifier{
		value: parsedUUID.String(),
	}, nil
}

func NewIDFromStrings(v []string) ([]Identifier, error) {
	ids := make([]Identifier, 0, len(v))
	for _, id := range v {
		parsedUUID, err := NewIDFromString(id)
		if err != nil {
			return nil, err
		}
		ids = append(ids, *parsedUUID)
	}

	return ids, nil
}

func (i Identifier) Value() string {
	return i.value
}
