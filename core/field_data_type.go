package core

import (
	"errors"
)

var (
	ErrInvalidFieldDataType = NewBusinessError("invalid field data type")
)

type FieldDataType string

const (
	NopFieldDataType FieldDataType = "nop_data_type"
	Text             FieldDataType = "string"
	Integer          FieldDataType = "int64"
	Float            FieldDataType = "float64"
)

var fieldDataTypes = map[string]FieldDataType{
	string(Text):    Text,
	string(Integer): Integer,
	string(Float):   Float,
}

func NewFormDataTypeFromString(v string) (FieldDataType, error) {
	t, ok := fieldDataTypes[v]
	if !ok {
		return NopFieldDataType, errors.Join(ErrInvalidFieldDataType, errors.New(v))
	}
	return t, nil
}

func (f FieldDataType) Value() string {
	return string(f)
}
