package core

import (
	"errors"
	"fmt"
)

var (
	ErrInvalidPassword = NewBusinessError("invalid password")
)

type Password struct {
	value string
}

func NewPassword(v string) (*Password, error) {
	if len(v) < 8 {
		return nil, errors.Join(ErrInvalidPassword, fmt.Errorf("password must have at least 8 characters"))
	}
	return &Password{
		value: v,
	}, nil
}

func (p Password) Value() string {
	return p.value
}
