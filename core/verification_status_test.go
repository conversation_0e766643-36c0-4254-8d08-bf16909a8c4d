package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVerificationStatus(t *testing.T) {
	assert.True(t, VerificationStatusAwaitingReview.IsValid())
	assert.True(t, VerificationStatusUnderReview.IsValid())
	assert.True(t, VerificationStatusVerified.IsValid())
	assert.True(t, VerificationStatusNotVerified.IsValid())
	assert.False(t, VerificationStatus("invalid_status").IsValid())

	assert.Equal(t, "awaiting_review", VerificationStatusAwaitingReview.String())
	assert.Equal(t, "under_review", VerificationStatusUnderReview.String())
	assert.Equal(t, "verified", VerificationStatusVerified.String())
	assert.Equal(t, "not_verified", VerificationStatusNotVerified.String())

	_, err := NewVerificationStatus("awaiting_review")
	assert.NoError(t, err)

	_, err = NewVerificationStatus("under_review")
	assert.NoError(t, err)

	_, err = NewVerificationStatus("verified")
	assert.NoError(t, err)

	_, err = NewVerificationStatus("not_verified")
	assert.NoError(t, err)

	_, err = NewVerificationStatus("invalid_status")
	assert.Error(t, err)
}
