package core

type VerificationStatus string

const (
	VerificationStatusAwaitingReview VerificationStatus = "awaiting_review"
	VerificationStatusUnderReview    VerificationStatus = "under_review"
	VerificationStatusVerified       VerificationStatus = "verified"
	VerificationStatusNotVerified    VerificationStatus = "not_verified"
)

func NewVerificationStatus(status string) (VerificationStatus, error) {
	switch status {
	case string(VerificationStatusAwaitingReview),
		string(VerificationStatusUnderReview),
		string(VerificationStatusVerified),
		string(VerificationStatusNotVerified):
		return VerificationStatus(status), nil
	default:
		return "", NewBusinessError("invalid verification status")
	}
}

func (v VerificationStatus) IsValid() bool {
	return v == VerificationStatusAwaitingReview ||
		v == VerificationStatusUnderReview ||
		v == VerificationStatusVerified ||
		v == VerificationStatusNotVerified
}

func (v VerificationStatus) String() string {
	return string(v)
}
