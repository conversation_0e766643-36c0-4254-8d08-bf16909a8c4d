package core

import (
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"regexp"

	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
	s3Type "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
)

var (
	ErrNoItemFound = errors.New("no item found")
	InvalidRequest = errors.New("invalid request")
	ErrForbidden   = errors.New("forbidden access")
)

type BusinessError struct {
	err error
}

func NewBusinessError(format string, a ...any) *BusinessError {
	return &BusinessError{err: fmt.Errorf(format, a...)}
}

func (e *BusinessError) Error() string {
	return e.err.Error()
}

func IsErrNoItemFound(err error) bool {
	return errors.Is(err, sql.ErrNoRows) ||
		errors.Is(err, ErrNoItemFound)
}

// ParseDBError checks for constraints violation error and forms a new error descriptions
// in case of constraint not violated it will return passed error.
func ParseDBError(err error) error {
	var pqErr *pq.Error
	if errors.As(err, &pqErr) {
		// checks for unique constraints
		switch pqErr.Code {
		case "23505": // unique_violation
			field, value := parseValueAndField(pqErr.Detail)
			return NewBusinessError("Duplicate value '%s' for field '%s'", value, field)

		case "23503": // foreign_key_violation
			field, value := parseValueAndField(pqErr.Detail)
			return NewBusinessError("Value '%s' for field '%s' does not exist", value, field)

		case "23502": // not_null_violation
			return NewBusinessError("Field '%s' cannot be null", pqErr.Column)

		case "23514": // check_violation
			return NewBusinessError("Check constraint '%s' failed on field '%s'", pqErr.Constraint, pqErr.Column)

		case "22001": // string_data_right_truncation
			return NewBusinessError("Value too long for field '%s'", pqErr.Column)

		case "22007": // invalid_datetime_format
			return NewBusinessError("Invalid date/time format for field '%s'", pqErr.Column)

		case "22P02": // invalid_text_representation
			return NewBusinessError("Invalid text representation format for field '%s'", pqErr.Message)

		case "P0002": // no_data_found
			return NewBusinessError("No data found for the requested operation on field '%s', %s", pqErr.Column, pqErr.Message)

		default:
			return NewBusinessError("Unhandled DB error: %v", pqErr.Message)
		}

	}

	if IsErrNoItemFound(err) {
		return errors.Join(ErrNoItemFound, errors.New("requested data is not found"))
	}

	return err
}

// parseValueAndField returns column name and value who cause constraint violation
func parseValueAndField(details string) (field, value string) {
	re := regexp.MustCompile(`Key \((\w+)\)=\(([^)]+)\)`)
	matches := re.FindStringSubmatch(details)
	if len(matches) == 3 {
		field = matches[1]
		value = matches[2]
	}
	return
}

func ParseCognitoError(err error) error {
	var noAuth *types.NotAuthorizedException
	var userNotFound *types.UserNotFoundException
	var userNotConfirmed *types.UserNotConfirmedException
	var passwordResetRequired *types.PasswordResetRequiredException
	var codeMisMatch *types.CodeMismatchException
	var codeExpired *types.ExpiredCodeException
	var tooManyRequest *types.TooManyRequestsException
	switch {
	case errors.As(err, &noAuth):
		return echo.NewHTTPError(http.StatusUnauthorized, "Invalid username or password or user not found")

	case errors.As(err, &userNotFound):
		return echo.NewHTTPError(http.StatusNotFound, "Invalid username or password or user not found") // prevent enumeration

	case errors.As(err, &userNotConfirmed):
		return echo.NewHTTPError(http.StatusForbidden, "Account not confirmed. Please check your email")

	case errors.As(err, &passwordResetRequired):
		return echo.NewHTTPError(http.StatusForbidden, "Password reset required. Please reset your password")

	case errors.As(err, &codeMisMatch):
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid confirmation code")

	case errors.As(err, &codeExpired):
		return echo.NewHTTPError(http.StatusBadRequest, "Confirmation code has expired")

	case errors.As(err, &tooManyRequest):
		return echo.NewHTTPError(http.StatusTooManyRequests, "Too many attempts. Please try again later")

	default:
		return err
	}
}

func ParseS3BucketError(err error) error {
	switch {
	case errors.As(err, new(*s3Type.NoSuchBucket)):
		return echo.NewHTTPError(http.StatusNotFound, "Bucket does not exist")
	case errors.As(err, new(*s3Type.NoSuchKey)):
		return echo.NewHTTPError(http.StatusNotFound, "Object not found in bucket")
	case errors.As(err, new(*s3Type.NoSuchUpload)):
		return echo.NewHTTPError(http.StatusNotFound, "This multipart upload not exist")
	case errors.As(err, new(*s3Type.InvalidRequest)):
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request or empty object")
	case errors.As(err, new(*s3Type.NotFound)):
		return echo.NewHTTPError(http.StatusNotFound, "Object not found")
	case errors.As(err, new(*s3Type.TooManyParts)):
		return echo.NewHTTPError(http.StatusBadRequest, "Too many parts")
	case errors.As(err, new(*s3Type.InvalidObjectState)):
		return echo.NewHTTPError(http.StatusConflict, "Object is archived and cannot be retrieved")
	default:
		return echo.NewHTTPError(http.StatusInternalServerError, "Unexpected S3 error")
	}
}
