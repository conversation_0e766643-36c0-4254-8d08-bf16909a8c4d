package core

var (
	ErrInvalidGroupStatus = NewBusinessError("invalid group status")
)

type GroupStatus struct {
	tp   string
	name string
}

var (
	NopGroupStatus = GroupStatus{tp: "nop_group_status", name: "Nop Group Status"}
	Bronze         = GroupStatus{tp: "bronze", name: "Bronze"}
	Silver         = GroupStatus{tp: "silver", name: "Silver"}
	Gold           = GroupStatus{tp: "gold", name: "Gold"}
	Diamond        = GroupStatus{tp: "diamond", name: "Diamond"}
	Platinum       = GroupStatus{tp: "platinum", name: "Platinum"}
)

var groupStatuses = map[string]GroupStatus{
	Bronze.tp:   Bronze,
	Silver.tp:   Silver,
	Gold.tp:     Gold,
	Diamond.tp:  Diamond,
	Platinum.tp: Platinum,
}

func NewGroupStatusFromString(v string) (GroupStatus, error) {
	g, ok := groupStatuses[v]
	if !ok {
		return NopGroupStatus, ErrInvalidGroupStatus
	}

	return g, nil
}

// GroupStatuses returns a slice of all available group statuses
func GroupStatuses() []GroupStatus {
	statuses := make([]GroupStatus, 0, len(groupStatuses))
	for _, gs := range groupStatuses {
		statuses = append(statuses, gs)
	}
	return statuses
}

func (s GroupStatus) Value() string {
	return s.tp
}

func (s GroupStatus) Type() string {
	return s.tp
}

func (s GroupStatus) Name() string {
	return s.name
}
