package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewEmail(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected *Email
		wantErr  bool
	}{
		{
			name:    "valid email",
			input:   "<EMAIL>",
			wantErr: false,
			expected: &Email{
				value: "<EMAIL>",
			},
		},
		{
			name:    "valid email with spaces",
			input:   "  <EMAIL>  ",
			wantErr: false,
			expected: &Email{
				value: "<EMAIL>",
			},
		},
		{
			name:    "invalid email no @",
			input:   "testexample.com",
			wantErr: true,
		},
		{
			name:    "invalid email empty",
			input:   "",
			wantErr: true,
		},
		{
			name:    "invalid email missing domain",
			input:   "test@",
			wantErr: true,
		},
		{
			name:    "invalid email missing local part",
			input:   "@example.com",
			wantErr: true,
		},
		{
			name:    "invalid email special characters",
			input:   "test!@example.com",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			email, err := NewEmail(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, email)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, email)
				assert.Equal(t, tt.expected.value, email.Value())
			}
		})
	}
}
