package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewLatitude(t *testing.T) {
	t.Run("valid latitude", func(t *testing.T) {
		lat, err := NewLatitude(45.0)
		assert.No<PERSON>rror(t, err)
		assert.NotNil(t, lat)
		assert.Equal(t, 45.0, lat.Value())
	})

	t.Run("invalid latitude (too high)", func(t *testing.T) {
		lat, err := NewLatitude(95.0)
		assert.<PERSON>rror(t, err)
		assert.<PERSON><PERSON>(t, lat)
		assert.Contains(t, err.Error(), "latitude must be between -90 and 90 degrees")
	})

	t.Run("invalid latitude (too low)", func(t *testing.T) {
		lat, err := NewLatitude(-95.0)
		assert.Error(t, err)
		assert.Nil(t, lat)
		assert.Contains(t, err.Error(), "latitude must be between -90 and 90 degrees")
	})
}

func TestNewLongitude(t *testing.T) {
	t.Run("valid longitude", func(t *testing.T) {
		lng, err := NewLongitude(120.0)
		assert.NoError(t, err)
		assert.NotNil(t, lng)
		assert.Equal(t, 120.0, lng.Value())
	})

	t.Run("invalid longitude (too high)", func(t *testing.T) {
		lng, err := NewLongitude(190.0)
		assert.Error(t, err)
		assert.Nil(t, lng)
		assert.Contains(t, err.Error(), "longitude must be between -180 and 180 degrees")
	})

	t.Run("invalid longitude (too low)", func(t *testing.T) {
		lng, err := NewLongitude(-190.0)
		assert.Error(t, err)
		assert.Nil(t, lng)
		assert.Contains(t, err.Error(), "longitude must be between -180 and 180 degrees")
	})
}

func TestNewGeoLocation(t *testing.T) {
	t.Run("valid geolocation", func(t *testing.T) {
		geo, err := NewGeoLocation(40.7128, -74.0060)
		assert.NoError(t, err)
		assert.NotNil(t, geo)
		assert.Equal(t, 40.7128, geo.Latitude().Value())
		assert.Equal(t, -74.0060, geo.Longitude().Value())
		assert.Equal(t, "Latitude: 40.712800, Longitude: -74.006000", geo.String())
	})

	t.Run("invalid geolocation (latitude)", func(t *testing.T) {
		geo, err := NewGeoLocation(95.0, -74.0060)
		assert.Error(t, err)
		assert.Nil(t, geo)
		assert.Contains(t, err.Error(), "latitude must be between -90 and 90 degrees")
	})

	t.Run("invalid geolocation (longitude)", func(t *testing.T) {
		geo, err := NewGeoLocation(40.7128, 190.0)
		assert.Error(t, err)
		assert.Nil(t, geo)
		assert.Contains(t, err.Error(), "longitude must be between -180 and 180 degrees")
	})
}

func TestGeoLocationString(t *testing.T) {
	geo, err := NewGeoLocation(51.5074, -0.1278)
	assert.NoError(t, err)
	assert.NotNil(t, geo)
	assert.Equal(t, "Latitude: 51.507400, Longitude: -0.127800", geo.String())
}
