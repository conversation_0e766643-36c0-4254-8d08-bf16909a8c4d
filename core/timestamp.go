package core

import "time"

type Timestamp struct {
	value time.Time
}

func NewTimestamp() Timestamp {
	utc := time.Now().UTC()
	return Timestamp{
		value: utc,
	}
}

func NewTimestampFromTime(v time.Time) (*Timestamp, error) {
	if v.Is<PERSON>ero() {
		return nil, NewBusinessError("invalid date empty date and time value")
	}

	utc := v.UTC()
	return &Timestamp{
		value: utc,
	}, nil
}

func (t Timestamp) Value() time.Time {
	return t.value
}

func (t Timestamp) String() string {
	return t.value.UTC().Format(time.RFC3339)
}
