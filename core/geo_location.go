package core

import (
	"fmt"
)

var (
	ErrInvalidLatitude  = NewBusinessError("invalid latitude: latitude must be between -90 and 90 degrees")
	ErrInvalidLongitude = NewBusinessError("invalid longitude: longitude must be between -180 and 180 degrees")
)

type GeoLocation struct {
	lat Latitude
	lng Longitude
}

func NewGeoLocation(lat, lng float64) (*GeoLocation, error) {
	latitude, err := NewLatitude(lat)
	if err != nil {
		return nil, err
	}

	longitude, err := NewLongitude(lng)
	if err != nil {
		return nil, err
	}

	return &GeoLocation{
		lat: *latitude,
		lng: *longitude,
	}, nil
}

func (g GeoLocation) Latitude() Latitude   { return g.lat }
func (g GeoLocation) Longitude() Longitude { return g.lng }

func (g GeoLocation) String() string {
	return fmt.Sprintf("Latitude: %.6f, Longitude: %.6f", g.lat.Value(), g.lng.Value())
}

type Latitude struct {
	value float64
}

func NewLatitude(v float64) (*Latitude, error) {
	if v < -90 || v > 90 {
		return nil, ErrInvalidLatitude
	}
	return &Latitude{
		value: v,
	}, nil
}

func (lat Latitude) Value() float64 {
	return lat.value
}

type Longitude struct {
	value float64
}

func NewLongitude(v float64) (*Longitude, error) {
	if v < -180 || v > 180 {
		return nil, ErrInvalidLongitude
	}
	return &Longitude{
		value: v,
	}, nil
}

func (lng Longitude) Value() float64 {
	return lng.value
}
