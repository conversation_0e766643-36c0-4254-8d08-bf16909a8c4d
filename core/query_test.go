package core

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestQueryGenerator_Basic(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	assert.Equal(t, "SELECT * FROM area", q.Query())
	assert.Equal(t, []any{}, q.Args())
}

func TestQueryGenerator_Where_And_Or(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.Where("label", EQ, "Radiology").And("acronym", EQ, "RAD").Or("enabled", EQ, true)
	expected := "SELECT * FROM area WHERE label = $1 AND acronym = $2 OR enabled = $3"
	assert.Equal(t, expected, q.Query())
	assert.Equal(t, []any{"Radiology", "RAD", true}, q.Args())
}

func TestQueryGenerator_OrderBy_Limit_Offset(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.OrderBy("label", "ASC").Limit(10).Offset(5)
	expected := "SELECT * FROM area ORDER BY $1 ASC LIMIT $2 OFFSET $3"
	assert.Equal(t, expected, q.Query())
	assert.Equal(t, []any{"label", 10, 5}, q.Args())
}

func TestQueryGenerator_CountQuery(t *testing.T) {
	q := NewQueryGenerator("area").CountQuery()
	assert.Equal(t, "SELECT COUNT(*) FROM area", q.Query())
}

func TestQueryGenerator_Done(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.Where("label", EQ, "Radiology").Done()
	expected := "SELECT * FROM area WHERE label = $1;"
	assert.Equal(t, expected, q.Query())
}

func TestQueryGenerator_Clear(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.Where("label", EQ, "Radiology").And("acronym", EQ, "RAD")
	q.Clear()
	assert.Equal(t, 1, q.argsCounter)
	assert.Empty(t, q.Args())
}

func TestQueryGenerator_WhereRaw(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.WhereRaw("label = $1 OR acronym = $2", "Radiology", "RAD")
	expected := "SELECT * FROM area WHERE label = $1 OR acronym = $2"
	assert.Equal(t, expected, q.Query())
	assert.Equal(t, []any{"Radiology", "RAD"}, q.Args())

	// Add another WhereRaw, should add AND
	q.WhereRaw("enabled = $3", true)
	expected2 := "SELECT * FROM area WHERE label = $1 OR acronym = $2 AND enabled = $3"
	assert.Equal(t, expected2, q.Query())
	assert.Equal(t, []any{"Radiology", "RAD", true}, q.Args())
}

func TestQueryGenerator_EmptyQuery(t *testing.T) {
	q := NewQueryGenerator("area")
	assert.Equal(t, "", q.Query())
	assert.Empty(t, q.Args())
}

func TestQueryGenerator_MultipleWhereClauses(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.Where("label", EQ, "Radiology").And("acronym", EQ, "RAD").And("enabled", EQ, true)
	expected := "SELECT * FROM area WHERE label = $1 AND acronym = $2 AND enabled = $3"
	assert.Equal(t, expected, q.Query())
	assert.Equal(t, []any{"Radiology", "RAD", true}, q.Args())
}

func TestQueryGenerator_SQLInjectionPrevention(t *testing.T) {
	injection := "Radiology'); DROP TABLE area;--"
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.Where("label", EQ, injection)
	expected := "SELECT * FROM area WHERE label = $1"
	assert.Equal(t, expected, q.Query())
	assert.Equal(t, []any{injection}, q.Args())
}

func TestQueryGenerator_CountQueryWithWhere(t *testing.T) {
	q := NewQueryGenerator("area").CountQuery()
	q.Where("enabled", EQ, true)
	expected := "SELECT COUNT(*) FROM area WHERE enabled = $1"
	assert.Equal(t, expected, q.Query())
	assert.Equal(t, []any{true}, q.Args())
}

func TestQueryGenerator_ArgumentsOrder(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.Where("label", EQ, "Radiology").And("acronym", EQ, "RAD").Or("enabled", EQ, true)
	args := q.Args()
	assert.Equal(t, "Radiology", args[0])
	assert.Equal(t, "RAD", args[1])
	assert.Equal(t, true, args[2])
}

func TestQueryGenerator_WhitespaceFormatting(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area ") // trailing space
	q.Where("label", EQ, "Radiology")
	expected := "SELECT * FROM area  WHERE label = $1" // double space before WHERE
	assert.Equal(t, expected, q.Query())
}

func TestQueryGenerator_INClause(t *testing.T) {
	q := NewQueryGenerator("area").WithBaseQuery("SELECT * FROM area")
	q.Where("id", IN, []int{1, 2, 3})
	expected := "SELECT * FROM area WHERE id IN $1"
	assert.Equal(t, expected, q.Query())
	assert.Equal(t, []any{[]int{1, 2, 3}}, q.Args())
}
