package core

import (
	"errors"
	"time"
)

var (
	ErrInvalidDuration = NewBusinessError("invalid duration: ")
)

const (
	maxDurationAllowed = 10
)

// Duration represents a simple date type.
// It stores the date information with validation
type Duration struct {
	value time.Time
}

// NewDurationFromString creates a new Duration instance with the provided value.
func NewDurationFromString(v string) (*Duration, error) {
	if v == "" {
		return nil, errors.Join(ErrInvalidDuration, errors.New("empty duration value"))
	}

	t, err := time.Parse(time.DateOnly, v)
	if err != nil {
		return nil, errors.Join(ErrInvalidDuration, err)
	}

	utcTime := t.UTC()

	err = validateTime(utcTime)
	if err != nil {
		return nil, errors.Join(ErrInvalidDuration, err)
	}

	return &Duration{
		value: utcTime,
	}, nil
}

// MustNewDurationFromString should only be used in testing
func MustNewDurationFromString(v string) *Duration {
	d, err := NewDurationFromString(v)
	if err != nil {
		panic(err)
	}
	return d
}

func validateTime(d time.Time) error {
	// allow maximum duration of 10 years excluding current day in calculation
	// also check for the duration is not expired from current date
	// maxDuration is in string like 10y and duration is expiring time
	maxDuration := time.Now().UTC().AddDate(maxDurationAllowed, 0, 0)

	// check for duration already expired
	if d.Before(time.Now().UTC()) || d.Equal(time.Now().UTC()) {
		return errors.New("duration already expired")
	}

	// start counting from the previous day
	if d.After(maxDuration) || d.Equal(maxDuration) {
		return errors.New("duration is more than 10 years")
	}

	return nil
}

func (d Duration) Value() time.Time {
	return d.value
}

func (d Duration) String() string {
	return d.value.UTC().Format(time.DateOnly)
}
