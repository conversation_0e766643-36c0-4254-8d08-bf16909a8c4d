package core

var ErrSocialMediaURL = NewBusinessError("invalid social media url")

type SocialMediaURL struct {
	socialMedia SocialMedia
	url         string
}

func NewSocialMediaURL(socialMediaType string, url string) (*SocialMediaURL, error) {
	sm, err := NewSocialMediaFromString(socialMediaType)
	if err != nil {
		return nil, err
	}
	return &SocialMediaURL{
		socialMedia: sm,
		url:         url,
	}, nil
}

func (r SocialMediaURL) Type() string {
	return r.socialMedia.Type()
}

func (r SocialMediaURL) URL() string {
	return r.url
}
