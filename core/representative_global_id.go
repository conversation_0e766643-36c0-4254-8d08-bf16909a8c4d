package core

import (
	"errors"
	"fmt"
	"strings"
)

var ErrInvalidGroupRepresentativeGlobalID = NewBusinessError("invalid group representative global id")

type RepresentativeGlobalID struct {
	value string
}

func NewRepresentativeGlobalID(v string) (*RepresentativeGlobalID, error) {
	n := strings.TrimSpace(v)
	if n == "" {
		return nil, errors.Join(ErrInvalidGroupRepresentativeGlobalID, fmt.Errorf("representative global id must not be empty"))
	}

	return &RepresentativeGlobalID{
		value: v,
	}, nil
}

func (n RepresentativeGlobalID) Value() string {
	return n.value
}
