package core

import (
	"errors"
	"strings"
)

var ErrInvalidFieldTag = NewBusinessError("invalid field tag")

type FieldTag struct {
	value *string
}

// NewFieldTag creates a new FieldTag. Empty string or nil creates a nil FieldTag (applies to all fields)
func NewFieldTag(v *string) (*FieldTag, error) {
	if v == nil {
		return &FieldTag{value: nil}, nil
	}

	normalized := strings.TrimSpace(*v)
	if normalized == "" {
		return &FieldTag{value: nil}, nil
	}

	// Basic validation - field tags should be alphanumeric with underscores
	if !isValidFieldTagFormat(normalized) {
		return nil, errors.Join(ErrInvalidFieldTag, errors.New("field tag must contain only letters, numbers, and underscores"))
	}

	return &FieldTag{
		value: &normalized,
	}, nil
}

func NewFieldTagFromString(v string) (*FieldTag, error) {
	if v == "" {
		return &FieldTag{value: nil}, nil
	}
	return NewFieldTag(&v)
}

func (f FieldTag) Value() *string {
	return f.value
}

func (f FieldTag) IsNil() bool {
	return f.value == nil
}

func (f FieldTag) String() string {
	if f.value == nil {
		return ""
	}
	return *f.value
}

// isValidFieldTagFormat checks if the field tag contains only valid characters
func isValidFieldTagFormat(tag string) bool {
	if len(tag) == 0 {
		return false
	}

	for _, char := range tag {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_') {
			return false
		}
	}
	return true
}
