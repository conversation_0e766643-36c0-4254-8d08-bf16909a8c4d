package core

import (
	"fmt"
	"strings"
)

// TODO: create test file for query generator

type Operator string

const (
	EQ    Operator = "="
	NEQ   Operator = "!="
	LT    Operator = "<"
	LTE   Operator = "<="
	GT    Operator = ">"
	GTE   Operator = ">="
	LIKE  Operator = "LIKE"
	IN    Operator = "IN"
	NOT   Operator = "NOT"
	ILIKE Operator = "ILIKE"
	ANY   Operator = "= ANY"
)

type QueryGenerator struct {
	argsCounter int
	args        []any
	tableName   string
	query       string
}

func NewQueryGenerator(tableName string) *QueryGenerator {
	return &QueryGenerator{
		argsCounter: 1,
		args:        []any{},
		tableName:   tableName,
	}
}

// WithBaseQuery lets you initialize a query
func (q *QueryGenerator) WithBaseQuery(query string) *QueryGenerator {
	q.query = query
	return q
}

// Where lets you add a where clause in the query
func (q *QueryGenerator) Where(columnName string, op Operator, value any) *QueryGenerator {
	q.query += fmt.Sprintf(" WHERE %s %s $%d", columnName, op, q.argsCounter)
	q.argsCounter++
	q.args = append(q.args, value)
	return q
}

// OrderBy lets you add an order by clause in the query
func (q *QueryGenerator) OrderBy(columnName string, direction string) *QueryGenerator {
	q.query += fmt.Sprintf(" ORDER BY $%d %s", q.argsCounter, direction)
	q.argsCounter++
	q.args = append(q.args, columnName)
	return q
}

// Limit lets you add a limit clause in the query
func (q *QueryGenerator) Limit(limit int) *QueryGenerator {
	q.query += fmt.Sprintf(" LIMIT $%d", q.argsCounter)
	q.argsCounter++
	q.args = append(q.args, limit)
	return q
}

// Offset lets you add an offset clause in the query
func (q *QueryGenerator) Offset(offset int) *QueryGenerator {
	q.query += fmt.Sprintf(" OFFSET $%d", q.argsCounter)
	q.argsCounter++
	q.args = append(q.args, offset)
	return q
}

// Done lets you finalize the query
func (q *QueryGenerator) Done() string {
	q.query += ";"
	return q.Query()
}

// CountQuery returns the count query
func (q *QueryGenerator) CountQuery() *QueryGenerator {
	q.query = fmt.Sprintf("SELECT COUNT(*) FROM %s", q.tableName)
	return q
}

// Query returns the generated query
func (q *QueryGenerator) Query() string {
	return q.query
}

// Args returns the arguments for the query
func (q *QueryGenerator) Args() []any {
	return q.args
}

func (q *QueryGenerator) And(columnName string, op Operator, value any) *QueryGenerator {
	q.query += fmt.Sprintf(" AND %s %s $%d", columnName, op, q.argsCounter)
	q.argsCounter++
	q.args = append(q.args, value)
	return q
}

func (q *QueryGenerator) Or(columnName string, op Operator, value any) *QueryGenerator {
	q.query += fmt.Sprintf(" OR %s %s $%d", columnName, op, q.argsCounter)
	q.argsCounter++
	q.args = append(q.args, value)
	return q
}

func (q *QueryGenerator) Clear() *QueryGenerator {
	q.args = []any{}
	q.argsCounter = 1
	return q
}

func (q *QueryGenerator) WhereRaw(clause string, args ...interface{}) *QueryGenerator {
	if !strings.Contains(q.query, "WHERE") {
		q.query += " WHERE "
	} else {
		q.query += " AND "
	}
	q.query += clause
	q.args = append(q.args, args...)
	return q
}