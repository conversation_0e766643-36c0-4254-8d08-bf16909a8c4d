package core

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
)

var (
	ErrInvalidPaginationData = NewBusinessError("invalid pagination data")
)

const (
	defaultSize = 10
	defaultPage = 0
)

// Pagination represents a pagination value object
type Pagination struct {
	Size      int
	Page      int
	OrderBy   string
	Direction string
}

type PaginationOpts struct {
	Size      string
	Page      string
	OrderBy   string
	Direction string
}

func NewPagination(opts PaginationOpts) (*Pagination, error) {
	s, err := size(opts.Size)
	if err != nil {
		return nil, err
	}

	p, err := page(opts.Page)
	if err != nil {
		return nil, err
	}

	dir, err := direction(opts.Direction)
	if err != nil {
		return nil, err
	}

	return &Pagination{
		Size:      s,
		Page:      p,
		OrderBy:   opts.OrderBy,
		Direction: dir,
	}, nil
}

func size(size string) (int, error) {
	if size == "" {
		return defaultSize, nil
	}

	v, err := strconv.Atoi(size)
	if err != nil {
		return 0, errors.Join(ErrInvalidPaginationData, fmt.Errorf("invalid pagination size: %v", err))
	}

	return v, nil
}

func page(page string) (int, error) {
	if page == "" {
		return defaultPage, nil
	}

	v, err := strconv.Atoi(page)
	if err != nil {
		return 0, errors.Join(ErrInvalidPaginationData, fmt.Errorf("invalid pagination page: %v", err))
	}

	return v, nil
}

func direction(direction string) (string, error) {
	directionInUpperCase := strings.ToUpper(direction)
	if directionInUpperCase == "ASC" || directionInUpperCase == "DESC" {
		return directionInUpperCase, nil
	} else if directionInUpperCase == "" {
		return "ASC", nil
	} else {
		return "", errors.Join(ErrInvalidPaginationData, fmt.Errorf("invalid pagination direction it should be either ASC or DESC"))
	}
}

func (q *Pagination) GetSize() int {
	return q.Size
}

func (q *Pagination) GetPage() int {
	return q.Page
}

// GetOffset calculates and returns the offset based on the current page and size.
func (q *Pagination) GetOffset() int {
	if q.Page == 0 {
		return 0
	}
	return (q.Page - 1) * q.Size
}

// GetLimit returns the size of the pagination query. This value represents the
// maximum number of items to retrieve in a single query.
func (q *Pagination) GetLimit() int {
	return q.Size
}

// GetOrderBy returns the current order by clause for the pagination query.
func (q *Pagination) GetOrderBy() string {
	return q.OrderBy
}

func (q *Pagination) GetOrderDirection() string {
	return q.Direction
}

// GetQueryString constructs and returns the query string from the pagination parameters: page, size, and orderBy.
func (q *Pagination) GetQueryString() string {
	return fmt.Sprintf("page=%v&size=%v&orderBy=%s", q.GetPage(), q.GetSize(), q.GetOrderBy())
}

// GetTotalPages calculates the total number of pages based on the total count of items and the page size.
func GetTotalPages(totalCount int, pageSize int) int {
	if pageSize <= 0 {
		pageSize = 1
	}
	d := float64(totalCount) / float64(pageSize)
	return int(math.Ceil(d))
}

// GetHasMore returns true if there are more pages to fetch based on the current page, total items, and page size.
func GetHasMore(currentPage int, totalCount int, pageSize int) bool {
	size := 1
	if pageSize > 0 {
		size = pageSize
	}
	//TODO: review currently using page 0 and page 1 as same page
	if currentPage == 0 {
		currentPage = 1
	}
	return currentPage < totalCount/size
}
