package core

import "time"

// Date represents a simple UTC-truncated date,
// storing time information truncated to the hour level.
type Date struct {
	value time.Time
}

// NewDateFromTime creates a new Date instance by truncating
// the provided time to the hour and converting it to UTC.
func NewDateFromTime(v time.Time) (*Date, error) {
	if v.Is<PERSON>ero() {
		return nil, NewBusinessError("invalid date empty date and time value")
	}

	dt := v.Truncate(time.Hour).UTC()
	return &Date{value: dt}, nil
}

// NewDateFromString creates a new Date instance by parsing
// the provided string to date useful for date of birth fields
func NewDateFromString(d string) (*Date, error) {
	if d == "" {
		return nil, NewBusinessError("invalid date empty date and time value")
	}

	t, err := time.Parse(time.DateOnly, d)
	if err != nil {
		return nil, NewBusinessError("invalid date %w", err)
	}

	dt := t.Truncate(time.Hour).UTC()
	return &Date{value: dt}, nil
}

// NewDate creates a new Date instance representing the current time,
// truncated to the hour and converted to UTC.
func NewDate() Date {
	now, _ := NewDateFromTime(time.Now().UTC())
	return *now
}

func (d Date) Value() time.Time {
	return d.value
}

func (d Date) String() string {
	return d.value.UTC().Format(time.DateOnly)
}
