package core

import (
	"crypto/rand"
	"encoding/base64"
	"strings"
	"time"
)

const alphanumericStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
const raceIDLength = 6

func GenerateRaceID() (string, error) {

	b := make([]byte, raceIDLength)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}

	combined := append(b, []byte(time.Now().UTC().String())...)
	encoded := base64.StdEncoding.EncodeToString(combined)

	cleaned := strings.Map(func(r rune) rune {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') {
			return r
		}

		return rune(alphanumericStr[b[0]%62])
	}, encoded)

	return cleaned[:6], nil
}
