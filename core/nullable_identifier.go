package core

import (
	"errors"
	"github.com/google/uuid"
)

var (
	ErrInvalidNullableIdentifier = NewBusinessError("invalid identifier")
)

type NullableIdentifier struct {
	value *string
}

func NewNullableID() *NullableIdentifier {
	id := uuid.NewString()
	return &NullableIdentifier{
		value: &id,
	}
}

func NewNullableIDFromString(v string) (*NullableIdentifier, error) {
	parsedUUID, err := uuid.Parse(v)
	if err != nil {
		return nil, errors.Join(ErrInvalidNullableIdentifier, err)
	}

	value := parsedUUID.String()
	return &NullableIdentifier{
		value: &value,
	}, nil
}

func NewNullableIDFromStrings(v []string) ([]NullableIdentifier, error) {
	ids := make([]NullableIdentifier, 0, len(v))
	for _, id := range v {
		parsedUUID, err := NewNullableIDFromString(id)
		if err != nil {
			return nil, err
		}
		ids = append(ids, *parsedUUID)
	}

	return ids, nil
}

func (i NullableIdentifier) Value() *string {
	return i.value
}
