package core

// should move it to common?

import (
	"errors"
	"fmt"
)

var (
	ErrInvalidSocialMedia = NewBusinessError("invalid social media")
)

type SocialMedia struct {
	tp   string
	name string
}

var (
	NopSocialMedia = SocialMedia{tp: "nop_social_media", name: "Nop Social Media"}
	Website        = SocialMedia{tp: "website", name: "Website"}
	Instagram      = SocialMedia{tp: "instagram", name: "Instagram"}
	Facebook       = SocialMedia{tp: "facebook", name: "Facebook"}
	Youtube        = SocialMedia{tp: "youtube", name: "Youtube"}
	Linkedin       = SocialMedia{tp: "linkedin", name: "<PERSON>edin"}
	X              = SocialMedia{tp: "x", name: "X"}
)

var socialMedias = map[string]SocialMedia{
	Website.tp:   Website,
	Instagram.tp: Instagram,
	Facebook.tp:  Facebook,
	Youtube.tp:   Youtube,
	Linkedin.tp:  Linkedin,
	X.tp:         X,
}

func NewSocialMediaFromString(v string) (SocialMedia, error) {
	g, ok := socialMedias[v]
	if !ok {
		return NopSocialMedia, errors.Join(ErrInvalidSocialMedia, fmt.Errorf("invalid social media %s", v))
	}

	return g, nil
}

// SocialMedias returns a slice of all available group statuses
func SocialMedias() []SocialMedia {
	statuses := make([]SocialMedia, 0, len(socialMedias))
	for _, gs := range socialMedias {
		statuses = append(statuses, gs)
	}
	return statuses
}

func (s SocialMedia) Value() string {
	return s.tp
}

func (s SocialMedia) Type() string {
	return s.tp
}

func (s SocialMedia) Name() string {
	return s.name
}
