package core

import (
	"strings"
)

var (
	ErrInvalidAddress = NewBusinessError("invalid address")
)

type Address struct {
	value string
}

func NewAddress(v string) (*Address, error) {
	v = strings.TrimSpace(v)
	if len(v) < 3 {
		return nil, NewBusinessError("address must have at least 3 characters %w", ErrInvalidAddress)
	}

	return &Address{
		value: v,
	}, nil
}

func (a Address) Value() string {
	return a.value
}
