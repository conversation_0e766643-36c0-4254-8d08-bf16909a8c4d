services:
  postgres:
    image: postgres:15
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: price_list
    volumes:
      - ./scripts/database/initial-script.sql:/docker-entrypoint-initdb.d/initial-script.sql
      - ./scripts/database/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 10s
      timeout: 5s
      retries: 5
    command: [ "postgres", "-c", "config_file=/etc/postgresql/postgresql.conf" ]
    networks:
      - price-list-network

  wait_for_postgres:
    image: alpine:latest
    container_name: postgres-checker
    depends_on:
      postgres:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      apk add --no-cache make &&
      make migrate-up-after-infra"
    networks:
      - price-list-network

  swagger-ui:
    image: swaggerapi/swagger-ui:latest
    container_name: swagger-ui
    ports:
      - "8088:8088"
    environment:
      SWAGGER_JSON: /api/swagger.yaml
    volumes:
      - ./api/swagger.yaml:/api/swagger.yaml:ro
    networks:
      - price-list-network

networks:
  price-list-network:
    driver: bridge