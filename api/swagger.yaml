openapi: 3.0.3
info:
  title: Price List Field Management API
  description: |
    Read-only API for retrieving medical fields in the price list system.
  version: 1.0.0

paths:
  /fields:
    get:
      tags:
        - Fields
      summary: List all fields
      description: Retrieves a paginated list of medical fields with optional filtering and sorting
      operationId: listFields
      parameters:
        - name: page
          in: query
          description: Page number for pagination (0-based)
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
          example: 0
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          example: 10
        - name: area_uuid
          in: query
          description: Filter by medical area UUID
          required: false
          schema:
            type: string
            format: uuid
          example: "84ce4d70-a442-412b-bf27-06f4544a8661"
        - name: enabled
          in: query
          description: Filter by enabled status
          required: false
          schema:
            type: boolean
          example: true
        - name: name
          in: query
          description: Filter by field name (partial match, case-insensitive)
          required: false
          schema:
            type: string
            maxLength: 255
          example: "cardio"
        - name: orderBy
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
            enum: [name, position, created_at, updated_at]
            default: "name"
          example: "position"
        - name: direction
          in: query
          description: Sort direction
          required: false
          schema:
            type: string
            enum: [ASC, DESC]
            default: "ASC"
          example: "ASC"
      responses:
        '200':
          description: Fields retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FieldListResponse'
              example:
                status: true
                message: ""
                data:
                  - uuid: "550e8400-e29b-41d4-a716-************"
                    name: "Cardiology"
                    icon: "heart"
                    position: 1
                    enabled: true
                    area_uuid: "84ce4d70-a442-412b-bf27-06f4544a8661"
                    natures:
                      - uuid: "171977db-0846-4dac-af9f-30d6a43f3dac"
                        name: "Smile Consultation"
                        icon: "SmileConsultation"
                        enabled: true
                        position: 1
                      - uuid: "5fc76a15-e2a9-4442-89ee-7cc8e1134de7"
                        name: "Veneers Consultation"
                        icon: "VeneersConsultation"
                        enabled: true
                        position: 2
                  - uuid: "660f9511-f3ac-52e5-b827-************"
                    name: "Orthodontics"
                    icon: "teeth"
                    position: 2
                    enabled: true
                    area_uuid: "4beed17b-a38a-4da1-8b26-94d2f1513001"
                    natures:
                      - uuid: "a8a0c26b-5da6-4edd-a72d-46e9600db720"
                        name: "Orthodontic Consultation"
                        icon: "DentalConsultation"
                        enabled: true
                        position: 1
                pagination:
                  current_page: 0
                  total: 42
        '400':
          description: Bad request - Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FieldListResponse'
              example:
                status: false
                message: "Invalid query parameters provided"
                data: []
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FieldListResponse'
              example:
                status: false
                message: "Internal server error"
                data: []

  /fields/{fieldId}:
    get:
      tags:
        - Fields
      summary: Get a specific field
      description: Retrieves a single medical field by its UUID
      operationId: getField
      parameters:
        - name: fieldId
          in: path
          required: true
          description: UUID of the field to retrieve
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: Field retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FieldSingleResponse'
              example:
                status: true
                message: ""
                data:
                  uuid: "550e8400-e29b-41d4-a716-************"
                  name: "Cardiology"
                  icon: "heart"
                  position: 1
                  enabled: true
                  area_uuid: "84ce4d70-a442-412b-bf27-06f4544a8661"
                  natures:
                    - uuid: "171977db-0846-4dac-af9f-30d6a43f3dac"
                      name: "Smile Consultation"
                      icon: "SmileConsultation"
                      enabled: true
                      position: 1
                    - uuid: "5fc76a15-e2a9-4442-89ee-7cc8e1134de7"
                      name: "Veneers Consultation"
                      icon: "VeneersConsultation"
                      enabled: true
                      position: 2
        '400':
          description: Bad request - Invalid field ID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FieldSingleResponse'
              example:
                status: false
                message: "Invalid field ID format"
                data: null
        '404':
          description: Field not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FieldSingleResponse'
              example:
                status: false
                message: "Field with the specified ID does not exist"
                data: null
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FieldSingleResponse'
              example:
                status: false
                message: "Internal server error"
                data: null

  /appointment-types:
    get:
      tags:
        - Appointment Types
      summary: List all appointment types
      description: Retrieves a list of all appointment types with optional filtering
      operationId: listAppointmentTypes
      parameters:
        - name: enabled
          in: query
          description: Filter by enabled status
          required: false
          schema:
            type: boolean
          example: true
      responses:
        '200':
          description: Appointment types retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentTypeListResponse'
              example:
                status: true
                message: ""
                data:
                  - uuid: "969c3e7d-6853-46f3-b433-2b3f11d9e76e"
                    appointment_type: "live now"
                    enabled: true
                  - uuid: "d59f1959-ddf9-43bd-ab8b-5e0edd887dcf"
                    appointment_type: "live latter"
                    enabled: true
                  - uuid: "c9585818-43c7-4ddb-a7e8-4ccaee1049df"
                    appointment_type: "in_clinic"
                    enabled: true
        '400':
          description: Bad request - Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentTypeListResponse'
              example:
                status: false
                message: "Invalid query parameters provided"
                data: []
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentTypeListResponse'
              example:
                status: false
                message: "Internal server error"
                data: []

  /appointment-types/{appointmentTypeId}:
    get:
      tags:
        - Appointment Types
      summary: Get a specific appointment type
      description: Retrieves a single appointment type by its UUID
      operationId: getAppointmentType
      parameters:
        - name: appointmentTypeId
          in: path
          required: true
          description: UUID of the appointment type to retrieve
          schema:
            type: string
            format: uuid
          example: "969c3e7d-6853-46f3-b433-2b3f11d9e76e"
      responses:
        '200':
          description: Appointment type retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentTypeSingleResponse'
              example:
                status: true
                message: ""
                data:
                  uuid: "969c3e7d-6853-46f3-b433-2b3f11d9e76e"
                  appointment_type: "live now"
                  enabled: true
        '400':
          description: Bad request - Invalid appointment type ID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentTypeSingleResponse'
              example:
                status: false
                message: "Invalid appointment type ID format"
                data: null
        '404':
          description: Appointment type not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentTypeSingleResponse'
              example:
                status: false
                message: "Appointment type with the specified ID does not exist"
                data: null
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppointmentTypeSingleResponse'
              example:
                status: false
                message: "Internal server error"
                data: null

  /fields/natures:
    post:
      tags:
        - Fields
      summary: Get natures by field ID
      description: Retrieves consultation nature types associated with a specific field, ordered by position
      operationId: getNaturesByField
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FieldNatureRequest'
            example:
              field_id: "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"
      responses:
        '200':
          description: Natures retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NatureListResponse'
              example:
                status: true
                message: ""
                data:
                  - uuid: "171977db-0846-4dac-af9f-30d6a43f3dac"
                    name: "Smile Consultation"
                    icon: "SmileConsultation"
                    enabled: true
                    position: 1
                  - uuid: "5fc76a15-e2a9-4442-89ee-7cc8e1134de7"
                    name: "Veneers Consultation"
                    icon: "VeneersConsultation"
                    enabled: true
                    position: 2
                  - uuid: "0a73ed11-da6f-428c-b48a-0c54a299972d"
                    name: "Bonding Consultation"
                    icon: "BondingConsultation"
                    enabled: true
                    position: 3
        '400':
          description: Bad request - Invalid field ID format or missing field ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NatureListResponse'
              example:
                status: false
                message: "Invalid field ID format"
                data: []
        '404':
          description: Field not found or no natures associated with the field
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NatureListResponse'
              example:
                status: false
                message: "Field with the specified ID does not exist or has no associated natures"
                data: []
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NatureListResponse'
              example:
                status: false
                message: "Internal server error"
                data: []

  /areas:
    get:
      tags:
        - Areas
      summary: List all areas
      description: Retrieves a paginated list of areas with optional filtering and sorting
      operationId: listAreas
      parameters:
        - name: page
          in: query
          description: Page number for pagination (0-based)
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
          example: 0
        - name: size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          example: 10
        - name: enabled
          in: query
          description: Filter by enabled status
          required: false
          schema:
            type: boolean
          example: true
        - name: acronym
          in: query
          description: Filter by acronym (case-insensitive)
          required: false
          schema:
            type: string
            maxLength: 16
          example: "MED"
        - name: label
          in: query
          description: Filter by area label (partial match, case-insensitive)
          required: false
          schema:
            type: string
            maxLength: 255
          example: "med"
        - name: orderBy
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
            enum: [label, acronym, created_at, updated_at]
            default: "label"
          example: "label"
        - name: direction
          in: query
          description: Sort direction
          required: false
          schema:
            type: string
            enum: [ASC, DESC]
            default: "ASC"
          example: "ASC"
      responses:
        '200':
          description: Areas retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaListResponse'
              example:
                status: true
                message: ""
                data:
                  - id: "84ce4d70-a442-412b-bf27-06f4544a8661"
                    label: "Medicine"
                    enabled: true
                    acronym: "MED"
                  - id: "4beed17b-a38a-4da1-8b26-94d2f1513001"
                    label: "Dentistry"
                    enabled: true
                    acronym: "DENT"
                pagination:
                  current_page: 0
                  total: 3
        '400':
          description: Bad request - Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaListResponse'
              example:
                status: false
                message: "Invalid query parameters provided"
                data: []
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaListResponse'
              example:
                status: false
                message: "Internal server error"
                data: []

  /areas/{areaId}:
    get:
      tags:
        - Areas
      summary: Get a specific area
      description: Retrieves a single area by its UUID
      operationId: getArea
      parameters:
        - name: areaId
          in: path
          required: true
          description: UUID of the area to retrieve
          schema:
            type: string
            format: uuid
          example: "84ce4d70-a442-412b-bf27-06f4544a8661"
      responses:
        '200':
          description: Area retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaSingleResponse'
              example:
                status: true
                message: ""
                data:
                  id: "84ce4d70-a442-412b-bf27-06f4544a8661"
                  label: "Medicine"
                  enabled: true
                  acronym: "MED"
        '400':
          description: Bad request - Invalid area ID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaSingleResponse'
              example:
                status: false
                message: "Invalid area ID format"
                data: null
        '404':
          description: Area not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaSingleResponse'
              example:
                status: false
                message: "Area with the specified ID does not exist"
                data: null
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreaSingleResponse'
              example:
                status: false
                message: "Internal server error"
                data: null

  /branch-price-configurations:
    post:
      security:
        - BearerAuth: []
      tags:
        - Branch Price Configuration
      summary: Create or associate a branch, field, and nature with price configuration
      description: Receives integration events to associate a field and nature to a branch and create a price configuration if the combination is unique.
      operationId: createBranchPriceConfiguration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BranchPriceConfigurationRequest'
            example:
               branch_id: "b1b2c3d4-e5f6-7a8b-9c0d-e1f2a3b4c5d6"
               field_id: "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"
               nature_id: "171977db-0846-4dac-af9f-30d6a43f3dac"
               clinician_status: "master"
               prices: 45
               duration: 30
               appointment_type: "in_clinic"
      responses:
        '201':
          description: Branch price configuration created or already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchPriceConfigurationResponse'
              example:
                status: true
                message: "Branch price configuration created successfully."
                data:
                  id: "a1b2c3d4-e5f6-7a8b-9c0d-e1f2a3b4c5d6"
                  account_uuid: "e7b8c1a2-4b2a-4e2a-9c1a-2b2a4e2a9c1a"
                  branch_id: "b1b2c3d4-e5f6-7a8b-9c0d-e1f2a3b4c5d6"
                  field_id: "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"
                  nature_id: "171977db-0846-4dac-af9f-30d6a43f3dac"
                  clinician_status: "master"
                  prices: 45
                  duration: 30
                  appointment_type: "in_clinic"
                  created_date: "2024-07-25T10:00:00Z"
                  updated_date: "2024-07-25T10:00:00Z"
        '400':
          description: Bad request - Invalid input or duplicate combination
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchPriceConfigurationResponse'
              example:
                status: false
                message: "Combination already exists or input invalid."
                data: null
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BranchPriceConfigurationResponse'
              example:
                status: false
                message: "Internal server error"
                data: null

components:
  schemas:
    FieldResponse:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          description: Unique identifier of the field
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          description: Name of the field
          example: "Cardiology"
        icon:
          type: string
          nullable: true
          description: Icon identifier for the field
          example: "heart"
        position:
          type: integer
          nullable: true
          description: Display position/order of the field
          example: 1
        enabled:
          type: boolean
          description: Whether the field is active/enabled
          example: true
        area_uuid:
          type: string
          format: uuid
          description: UUID of the medical area this field belongs to
          example: "84ce4d70-a442-412b-bf27-06f4544a8661"
        natures:
          type: array
          items:
            $ref: '#/components/schemas/NatureBasicResponse'
          description: List of consultation nature types associated with this field

    FieldListResponse:
      type: object
      required:
        - status
        - message
        - data
      properties:
        status:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the request result
          example: ""
        data:
          type: array
          items:
            $ref: '#/components/schemas/FieldResponse'
          description: List of fields
        pagination:
          $ref: '#/components/schemas/PaginationMetadata'

    FieldSingleResponse:
      type: object
      required:
        - status
        - message
        - data
      properties:
        status:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the request result
          example: ""
        data:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/FieldResponse'
          description: Single field object or null if not found

    PaginationMetadata:
      type: object
      required:
        - current_page
        - total
      properties:
        current_page:
          type: integer
          description: Current page number (0-based)
          example: 0
        total:
          type: integer
          description: Total number of elements across all pages
          example: 42

    ErrorResponse:
      type: object
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              description: Error code identifier
              example: "VALIDATION_ERROR"
            message:
              type: string
              description: Human-readable error message
              example: "Invalid input data"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: Field name that caused the error
                    example: "name"
                  message:
                    type: string
                    description: Specific error message for the field
                    example: "Field name is required"
              description: Detailed error information
              example:
                - field: "name"
                  message: "Field name is required"

    AppointmentTypeResponse:
      type: object
      required:
        - uuid
        - appointment_type
        - enabled
      properties:
        uuid:
          type: string
          format: uuid
          description: Unique identifier of the appointment type
          example: "969c3e7d-6853-46f3-b433-2b3f11d9e76e"
        appointment_type:
          type: string
          description: Name of the appointment type
          example: "live now"
        enabled:
          type: boolean
          description: Whether the appointment type is enabled
          example: true
    AppointmentTypeListResponse:
      type: object
      required:
        - status
        - message
        - data
      properties:
        status:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the request result
          example: ""
        data:
          type: array
          items:
            $ref: '#/components/schemas/AppointmentTypeResponse'
          description: List of appointment types

    AppointmentTypeSingleResponse:
      type: object
      required:
        - status
        - message
        - data
      properties:
        status:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the request result
          example: ""
        data:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AppointmentTypeResponse'
          description: Single appointment type object or null if not found

    FieldNatureRequest:
      type: object
      required:
        - field_id
      properties:
        field_id:
          type: string
          format: uuid
          description: UUID of the field to get associated natures for
          example: "3135e655-afd4-4bb7-ba0e-4d47cfd9bd75"

    NatureResponse:
      type: object
      required:
        - uuid
        - name
        - enabled
        - position
        - created_at
        - updated_at
      properties:
        uuid:
          type: string
          format: uuid
          description: Unique identifier of the nature
          example: "171977db-0846-4dac-af9f-30d6a43f3dac"
        name:
          type: string
          description: Name of the consultation nature type
          example: "Smile Consultation"
        description:
          type: string
          nullable: true
          description: Description of the consultation nature type
          example: "A consultation regarding the smile and dental aesthetic treatment options"
        icon:
          type: string
          nullable: true
          description: Icon identifier for the nature type
          example: "SmileConsultation"
        enabled:
          type: boolean
          description: Whether the nature type is enabled
          example: true
        position:
          type: integer
          description: Display position/order of the nature within the field
          example: 1
        created_at:
          type: string
          format: date-time
          description: When the nature was created
          example: "2023-02-15T11:41:54.338Z"
        updated_at:
          type: string
          format: date-time
          description: When the nature was last updated
          example: "2023-02-15T11:41:54.338Z"

    NatureListResponse:
      type: object
      required:
        - status
        - message
        - data
      properties:
        status:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the request result
          example: "Natures retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/NatureBasicResponse'
          description: List of consultation nature types ordered by position

    AreaResponse:
      type: object
      required:
        - id
        - label
        - enabled
        - acronym
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier of the area
          example: "84ce4d70-a442-412b-bf27-06f4544a8661"
        label:
          type: string
          description: Name of the area
          example: "Medicine"
        enabled:
          type: boolean
          description: Whether the area is enabled
          example: true
        acronym:
          type: string
          description: Acronym for the area
          example: "MED"
    AreaListResponse:
      type: object
      required:
        - status
        - message
        - data
      properties:
        status:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the request result
          example: ""
        data:
          type: array
          items:
            $ref: '#/components/schemas/AreaResponse'
          description: List of areas
        pagination:
          $ref: '#/components/schemas/PaginationMetadata'

    AreaSingleResponse:
      type: object
      required:
        - status
        - message
        - data
      properties:
        status:
          type: boolean
          description: Indicates if the request was successful
          example: true
        message:
          type: string
          description: Human-readable message about the request result
          example: ""
        data:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/AreaResponse'
          description: Single area object or null if not found

    NatureBasicResponse:
      type: object
      required:
        - uuid
        - name
        - enabled
        - position
      properties:
        uuid:
          type: string
          format: uuid
          description: Unique identifier of the nature
          example: "171977db-0846-4dac-af9f-30d6a43f3dac"
        name:
          type: string
          description: Name of the consultation nature type
          example: "Smile Consultation"
        icon:
          type: string
          nullable: true
          description: Icon identifier for the nature type
          example: "SmileConsultation"
        enabled:
          type: boolean
          description: Whether the nature type is enabled
          example: true
        position:
          type: integer
          description: Display position/order of the nature within the field
          example: 1

    BranchPriceConfigurationRequest:
      type: object
      required:
        - branch_id
        - field_id
        - nature_id
        - clinician_status
        - prices
        - duration
      properties:
        branch_id:
          type: string
          format: uuid
        field_id:
          type: string
          format: uuid
        nature_id:
          type: string
          format: uuid
        clinician_status:
          type: string
          example: "master"
        prices:
          type: integer
        duration:
          type: integer
          minimum: 15
          maximum: 120
          multipleOf: 15
          description: Duration in minutes, must be a multiple of 15 and less than 120.
        appointment_type:
          type: string
          maxLength: 32
          nullable: true
    BranchPriceConfigurationResponse:
      type: object
      required:
        - status
        - message
      properties:
        status:
          type: boolean
        message:
          type: string
        data:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/BranchPriceConfigurationData'

    BranchPriceConfigurationData:
      type: object
      properties:
        id:
          type: string
          format: uuid
        account_uuid:
          type: string
          format: uuid
        branch_id:
          type: string
          format: uuid
        field_id:
          type: string
          format: uuid
        nature_id:
          type: string
          format: uuid
        clinician_status:
          type: string
        prices:
          type: integer
        duration:
          type: integer
          minimum: 15
          maximum: 120
          multipleOf: 15
          description: Duration in minutes, must be a multiple of 15 and less than 120.
        appointment_type:
          type: string
          nullable: true
        created_date:
          type: string
          format: date-time
        updated_date:
          type: string
          format: date-time

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token authentication

security:
  - BearerAuth: []

tags:
  - name: Fields
    description: Operations for retrieving medical field data and associated consultation nature types
  - name: Appointment Types
    description: Read-only operations for retrieving appointment type data
  - name: Areas
    description: Read-only operations for retrieving area data