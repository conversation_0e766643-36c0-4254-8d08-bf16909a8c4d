package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

var validID = core.NewID()
var validAreaID = core.NewID()

func TestNewField(t *testing.T) {
	tests := []struct {
		name    string
		input   *NewFieldInput
		wantErr bool
	}{
		{
			name: "Valid input with all fields",
			input: &NewFieldInput{
				ID:          validID.Value(),
				Name:        "Cardiology",
				Description: core.ToPtr("Heart-related medical specialty"),
				Icon:        core.ToPtr("heart-icon"),
				Position:    core.ToPtr(1),
				Enabled:     true,
				AreaID:      core.ToPtr(validAreaID.Value()),
			},
			wantErr: false,
		},
		{
			name: "Valid input with minimal fields",
			input: &NewFieldInput{
				ID:      validID.Value(),
				Name:    "General Practice",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with disabled status",
			input: &NewFieldInput{
				ID:      validID.Value(),
				Name:    "Obsolete Field",
				Enabled: false,
			},
			wantErr: false,
		},
		{
			name: "Valid input with zero timestamps",
			input: &NewFieldInput{
				ID:        validID.Value(),
				Name:      "Test Field",
				Enabled:   true,
				CreatedAt: &time.Time{},
				UpdatedAt: &time.Time{},
			},
			wantErr: false,
		},
		{
			name: "Invalid ID format",
			input: &NewFieldInput{
				ID:      "invalid-uuid",
				Name:    "Test Field",
				Enabled: true,
			},
			wantErr: true,
		},
		{
			name: "Empty ID",
			input: &NewFieldInput{
				ID:      "",
				Name:    "Test Field",
				Enabled: true,
			},
			wantErr: true,
		},
		{
			name: "Empty field name",
			input: &NewFieldInput{
				ID:      validID.Value(),
				Name:    "",
				Enabled: true,
			},
			wantErr: true,
		},
		{
			name: "Invalid area ID format",
			input: &NewFieldInput{
				ID:      validID.Value(),
				Name:    "Test Field",
				Enabled: true,
				AreaID:  core.ToPtr("invalid-area-uuid"),
			},
			wantErr: true,
		},
		{
			name: "Valid input with long name",
			input: &NewFieldInput{
				ID:      validID.Value(),
				Name:    "Very Long Field Name That Should Still Be Valid For Testing Purposes",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with negative position",
			input: &NewFieldInput{
				ID:       validID.Value(),
				Name:     "Test Field",
				Position: core.ToPtr(-1),
				Enabled:  true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			field, err := NewField(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, field)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, field)

				// Verify getter
				assert.Equal(t, tt.input.ID, field.ID())
				assert.Equal(t, tt.input.Name, field.Name())
				assert.Equal(t, tt.input.Description, field.Description())
				assert.Equal(t, tt.input.Icon, field.Icon())
				assert.Equal(t, tt.input.Position, field.Position())
				assert.Equal(t, tt.input.Enabled, field.Enabled())

				// Verify area ID
				if tt.input.AreaID != nil {
					assert.Equal(t, tt.input.AreaID, field.AreaID())
				} else {
					assert.Nil(t, field.AreaID())
				}

				// Verify timestamps
				assert.NotZero(t, field.CreatedAt())
				assert.NotZero(t, field.UpdatedAt())

				if tt.input.CreatedAt != nil && !tt.input.CreatedAt.IsZero() {
					assert.Equal(t, tt.input.CreatedAt.UTC(), field.CreatedAt().UTC())
				}
				if tt.input.UpdatedAt != nil && !tt.input.UpdatedAt.IsZero() {
					assert.Equal(t, tt.input.UpdatedAt.UTC(), field.UpdatedAt().UTC())
				}
			}
		})
	}
}

func TestNewField_PanicOnNilInput(t *testing.T) {
	assert.Panics(t, func() {
		NewField(nil)
	})
}

func TestFieldGetters(t *testing.T) {
	now := time.Now()
	input := &NewFieldInput{
		ID:          validID.Value(),
		Name:        "Emergency Medicine",
		Description: core.ToPtr("Emergency medical care"),
		Icon:        core.ToPtr("emergency-icon"),
		Position:    core.ToPtr(5),
		Enabled:     true,
		AreaID:      core.ToPtr(validAreaID.Value()),
		CreatedAt:   &now,
		UpdatedAt:   &now,
	}

	field, err := NewField(input)
	assert.NoError(t, err)
	assert.NotNil(t, field)

	// Test all getter methods
	assert.Equal(t, input.ID, field.ID())
	assert.Equal(t, input.Name, field.Name())
	assert.Equal(t, input.Description, field.Description())
	assert.Equal(t, input.Icon, field.Icon())
	assert.Equal(t, input.Position, field.Position())
	assert.Equal(t, input.Enabled, field.Enabled())
	assert.Equal(t, input.AreaID, field.AreaID())
	assert.Equal(t, now.UTC(), field.CreatedAt().UTC())
	assert.Equal(t, now.UTC(), field.UpdatedAt().UTC())
}

func TestFieldWithDifferentTimestamps(t *testing.T) {
	createdTime := time.Now().Add(-time.Hour)
	updatedTime := time.Now()

	input := &NewFieldInput{
		ID:        validID.Value(),
		Name:      "Routine Check-up Field",
		Enabled:   true,
		CreatedAt: &createdTime,
		UpdatedAt: &updatedTime,
	}

	field, err := NewField(input)
	assert.NoError(t, err)
	assert.NotNil(t, field)

	// Verify timestamps are preserved correctly
	assert.Equal(t, createdTime.UTC(), field.CreatedAt().UTC())
	assert.Equal(t, updatedTime.UTC(), field.UpdatedAt().UTC())
	assert.True(t, field.CreatedAt().Before(field.UpdatedAt()))
}

func TestFieldWithNilTimestamps(t *testing.T) {
	input := &NewFieldInput{
		ID:        validID.Value(),
		Name:      "Walk-in Field",
		Enabled:   true,
		CreatedAt: nil,
		UpdatedAt: nil,
	}

	beforeCreate := time.Now()
	field, err := NewField(input)
	afterCreate := time.Now()

	assert.NoError(t, err)
	assert.NotNil(t, field)

	// Verify default timestamps are set to current time
	assert.True(t, field.CreatedAt().After(beforeCreate) || field.CreatedAt().Equal(beforeCreate))
	assert.True(t, field.CreatedAt().Before(afterCreate) || field.CreatedAt().Equal(afterCreate))
	assert.True(t, field.UpdatedAt().After(beforeCreate) || field.UpdatedAt().Equal(beforeCreate))
	assert.True(t, field.UpdatedAt().Before(afterCreate) || field.UpdatedAt().Equal(afterCreate))
}

func TestFieldEnabledDisabledStates(t *testing.T) {
	tests := []struct {
		name    string
		enabled bool
	}{
		{
			name:    "Enabled field",
			enabled: true,
		},
		{
			name:    "Disabled field",
			enabled: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := &NewFieldInput{
				ID:      validID.Value(),
				Name:    "Test Field",
				Enabled: tt.enabled,
			}

			field, err := NewField(input)
			assert.NoError(t, err)
			assert.NotNil(t, field)
			assert.Equal(t, tt.enabled, field.Enabled())
		})
	}
}

func TestFieldWithOptionalFields(t *testing.T) {
	tests := []struct {
		name        string
		description *string
		icon        *string
		position    *int
		areaID      *string
	}{
		{
			name:        "All optional fields provided",
			description: core.ToPtr("Test description"),
			icon:        core.ToPtr("test-icon"),
			position:    core.ToPtr(10),
			areaID:      core.ToPtr(validAreaID.Value()),
		},
		{
			name:        "No optional fields",
			description: nil,
			icon:        nil,
			position:    nil,
			areaID:      nil,
		},
		{
			name:        "Only description provided",
			description: core.ToPtr("Only description"),
			icon:        nil,
			position:    nil,
			areaID:      nil,
		},
		{
			name:        "Only position provided",
			description: nil,
			icon:        nil,
			position:    core.ToPtr(5),
			areaID:      nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := &NewFieldInput{
				ID:          validID.Value(),
				Name:        "Test Field",
				Description: tt.description,
				Icon:        tt.icon,
				Position:    tt.position,
				Enabled:     true,
				AreaID:      tt.areaID,
			}

			field, err := NewField(input)
			assert.NoError(t, err)
			assert.NotNil(t, field)

			assert.Equal(t, tt.description, field.Description())
			assert.Equal(t, tt.icon, field.Icon())
			assert.Equal(t, tt.position, field.Position())
			assert.Equal(t, tt.areaID, field.AreaID())
		})
	}
}
