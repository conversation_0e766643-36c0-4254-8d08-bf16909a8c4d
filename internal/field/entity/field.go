package entity

import (
	"fmt"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// Field represents a field entity
type Field struct {
	id          core.Identifier
	name        string
	description *string
	icon        *string
	position    *int
	enabled     bool
	areaID      *core.Identifier
	createdAt   core.Timestamp
	updatedAt   core.Timestamp
}

// NewFieldInput represents the input for creating a new field
type NewFieldInput struct {
	ID          string
	Name        string
	Description *string
	Icon        *string
	Position    *int
	Enabled     bool
	AreaID      *string
	CreatedAt   *time.Time
	UpdatedAt   *time.Time
}

// NewField creates a new field entity with validation
func <PERSON>Field(input *NewFieldInput) (*Field, error) {
	if input == nil {
		panic("NewFieldInput cannot be nil")
	}

	// Validate required fields
	if input.ID == "" {
		return nil, fmt.Errorf("field ID is required")
	}

	if input.Name == "" {
		return nil, fmt.Errorf("field name is required")
	}

	id, err := core.NewIDFromString(input.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid field ID format: %w", err)
	}

	var areaID *core.Identifier
	if input.AreaID != nil && *input.AreaID != "" {
		parsedAreaID, err := core.NewIDFromString(*input.AreaID)
		if err != nil {
			return nil, fmt.Errorf("invalid area ID format: %w", err)
		}
		areaID = parsedAreaID
	}

	// Handle timestamps - use provided or default to current time
	now := time.Now().UTC()
	createdTime := now
	if input.CreatedAt != nil && !input.CreatedAt.IsZero() {
		createdTime = input.CreatedAt.UTC()
	}
	updatedTime := now
	if input.UpdatedAt != nil && !input.UpdatedAt.IsZero() {
		updatedTime = input.UpdatedAt.UTC()
	}

	createdAt, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, fmt.Errorf("invalid created_at timestamp: %w", err)
	}

	updatedAt, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, fmt.Errorf("invalid updated_at timestamp: %w", err)
	}

	return &Field{
		id:          *id,
		name:        input.Name,
		description: input.Description,
		icon:        input.Icon,
		position:    input.Position,
		enabled:     input.Enabled,
		areaID:      areaID,
		createdAt:   *createdAt,
		updatedAt:   *updatedAt,
	}, nil
}

// Getter methods
func (f Field) ID() string {
	return f.id.Value()
}

func (f Field) Name() string {
	return f.name
}

func (f Field) Description() *string {
	return f.description
}

func (f Field) Icon() *string {
	return f.icon
}

func (f Field) Position() *int {
	return f.position
}

func (f Field) Enabled() bool {
	return f.enabled
}

func (f Field) AreaID() *string {
	if f.areaID == nil {
		return nil
	}
	value := f.areaID.Value()
	return &value
}

func (f Field) CreatedAt() time.Time {
	return f.createdAt.Value()
}

func (f Field) UpdatedAt() time.Time {
	return f.updatedAt.Value()
}
