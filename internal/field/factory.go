package field

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/field/event"
	"gitlab.viswalslab.com/backend/price-list/internal/field/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field/service"
	"gitlab.viswalslab.com/backend/price-list/internal/field/usecase"
)

type Factory struct {
	FieldEventListener *event.FieldEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase usecase.UpsertFieldUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertFieldInput) (*usecase.UpsertFieldOutput, error) {
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewFieldValidationService()

	// Initialize repository for upsert operations
	fieldUpsertRepo := repository.NewFieldUpsertRepository(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertFieldUsecase(fieldUpsertRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	// Initialize event listener
	eventListener, err := event.NewFieldEventListener(cfg, adapter)
	if err != nil {
		return nil, err
	}

	return &Factory{
		FieldEventListener: eventListener,
	}, nil
}
