package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/entity"
)

func TestNewFieldValidationService(t *testing.T) {
	service := NewFieldValidationService()
	assert.NotNil(t, service)
	assert.Implements(t, (*FieldValidationService)(nil), service)
}

func TestValidateBusinessRules(t *testing.T) {
	service := NewFieldValidationService()
	ctx := context.Background()

	validID := core.NewID()

	tests := []struct {
		name               string
		setupEntity        func() *entity.Field
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid entity",
			setupEntity: func() *entity.Field {
				input := &entity.NewFieldInput{
					ID:      validID.Value(),
					Name:    "Cardiology",
					Enabled: true,
				}
				field, _ := entity.NewField(input)
				return field
			},
			wantErr: false,
		},
		{
			name: "Valid disabled entity",
			setupEntity: func() *entity.Field {
				input := &entity.NewFieldInput{
					ID:      validID.Value(),
					Name:    "Obsolete Field",
					Enabled: false,
				}
				field, _ := entity.NewField(input)
				return field
			},
			wantErr: false,
		},
		{
			name: "Valid entity with area",
			setupEntity: func() *entity.Field {
				areaID := core.NewID()
				input := &entity.NewFieldInput{
					ID:      validID.Value(),
					Name:    "Neurology",
					Enabled: true,
					AreaID:  core.ToPtr(areaID.Value()),
				}
				field, _ := entity.NewField(input)
				return field
			},
			wantErr: false,
		},
		{
			name: "Nil entity",
			setupEntity: func() *entity.Field {
				return nil
			},
			wantErr:            true,
			expectedErrMessage: "field entity is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := tt.setupEntity()
			err := service.ValidateBusinessRules(ctx, entity)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpsertInput(t *testing.T) {
	service := NewFieldValidationService()
	ctx := context.Background()

	validID := core.NewID()
	validAreaID := core.NewID()

	tests := []struct {
		name               string
		input              *UpsertValidationInput
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid input with ID",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Cardiology",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input without ID",
			input: &UpsertValidationInput{
				Name:    "General Practice",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with area",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Neurology",
				Enabled: true,
				AreaID:  core.ToPtr(validAreaID.Value()),
			},
			wantErr: false,
		},
		{
			name: "Valid disabled field",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Obsolete Field",
				Enabled: false,
			},
			wantErr: false,
		},
		{
			name: "Valid input with all optional fields",
			input: &UpsertValidationInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Emergency Medicine",
				Description: core.ToPtr("Emergency medical care"),
				Icon:        core.ToPtr("emergency-icon"),
				Position:    core.ToPtr(5),
				Enabled:     true,
				AreaID:      core.ToPtr(validAreaID.Value()),
			},
			wantErr: false,
		},
		{
			name:               "Nil input",
			input:              nil,
			wantErr:            true,
			expectedErrMessage: "validation input is required",
		},
		{
			name: "Empty field name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "",
				Enabled: true,
			},
			wantErr:            true,
			expectedErrMessage: "field name is required",
		},
		{
			name: "Invalid ID format",
			input: &UpsertValidationInput{
				ID:      core.ToPtr("invalid-uuid"),
				Name:    "Test Field",
				Enabled: true,
			},
			wantErr:            true,
			expectedErrMessage: "invalid UUID format",
		},
		{
			name: "Empty ID string",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(""),
				Name:    "Test Field",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Invalid area ID format",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Test Field",
				Enabled: true,
				AreaID:  core.ToPtr("invalid-area-uuid"),
			},
			wantErr:            true,
			expectedErrMessage: "invalid area UUID format",
		},
		{
			name: "Empty area ID string",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Test Field",
				Enabled: true,
				AreaID:  core.ToPtr(""),
			},
			wantErr: false,
		},
		{
			name: "Valid input with zero position",
			input: &UpsertValidationInput{
				ID:       core.ToPtr(validID.Value()),
				Name:     "Test Field",
				Position: core.ToPtr(0),
				Enabled:  true,
			},
			wantErr: false,
		},
		{
			name: "Invalid negative position",
			input: &UpsertValidationInput{
				ID:       core.ToPtr(validID.Value()),
				Name:     "Test Field",
				Position: core.ToPtr(-1),
				Enabled:  true,
			},
			wantErr:            true,
			expectedErrMessage: "position must be non-negative",
		},
		{
			name: "Valid input with large position",
			input: &UpsertValidationInput{
				ID:       core.ToPtr(validID.Value()),
				Name:     "Test Field",
				Position: core.ToPtr(1000),
				Enabled:  true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with special characters in name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Emergency & Trauma Medicine",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with spaces in name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Internal Medicine Specialty",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with numbers in name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Type-1 Diabetes Care",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid long name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Very Long Field Name That Should Still Be Valid For Testing Purposes And Medical Specialties",
				Enabled: true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpsertInput_EdgeCases(t *testing.T) {
	service := NewFieldValidationService()
	ctx := context.Background()

	tests := []struct {
		name               string
		input              *UpsertValidationInput
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Whitespace only name - allowed by current validation",
			input: &UpsertValidationInput{
				Name:    "   ",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Tab and newline in name - allowed by current validation",
			input: &UpsertValidationInput{
				Name:    "\t\n",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Single space name - allowed by current validation",
			input: &UpsertValidationInput{
				Name:    " ",
				Enabled: true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
