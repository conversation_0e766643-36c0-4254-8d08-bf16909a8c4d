package service

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/entity"
)

// FieldValidationService defines the interface for field validation operations
type FieldValidationService interface {
	// ValidateUpsertInput validates input for field upsert operations
	ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error

	// ValidateBusinessRules validates business rules for field entities
	ValidateBusinessRules(ctx context.Context, field *entity.Field) error
}

// UpsertValidationInput represents input for field upsert validation
type UpsertValidationInput struct {
	ID          *string
	Name        string
	Description *string
	Icon        *string
	Position    *int
	Enabled     bool
	AreaID      *string
}

// fieldValidationService implements FieldValidationService
type fieldValidationService struct{}

// NewFieldValidationService creates a new field validation service
func NewFieldValidationService() FieldValidationService {
	return &fieldValidationService{}
}

// ValidateUpsertInput validates input for field upsert operations
func (s *fieldValidationService) ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error {
	if input == nil {
		return fmt.Errorf("validation input is required")
	}

	// Validate field name
	if input.Name == "" {
		return fmt.Errorf("field name is required")
	}

	// Validate ID format if provided
	if input.ID != nil && *input.ID != "" {
		_, err := core.NewIDFromString(*input.ID)
		if err != nil {
			return fmt.Errorf("invalid UUID format: %w", err)
		}
	}

	// Validate area ID format if provided
	if input.AreaID != nil && *input.AreaID != "" {
		_, err := core.NewIDFromString(*input.AreaID)
		if err != nil {
			return fmt.Errorf("invalid area UUID format: %w", err)
		}
	}

	// Validate position if provided
	if input.Position != nil && *input.Position < 0 {
		return fmt.Errorf("position must be non-negative")
	}

	return nil
}

// ValidateBusinessRules validates business rules for field entities
func (s *fieldValidationService) ValidateBusinessRules(ctx context.Context, field *entity.Field) error {
	if field == nil {
		return fmt.Errorf("field entity is required")
	}

	return nil
}
