package repository

import (
	"context"
	"fmt"
	"strings"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/field/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// FieldNameExistsError represents an error when field name already exists
type FieldNameExistsError struct {
	Name string
}

func (e *FieldNameExistsError) Error() string {
	return fmt.Sprintf("field name '%s' already exists", e.Name)
}

// FieldUpsertRepository defines the interface for field upsert operations
type FieldUpsertRepository interface {
	UpsertField(ctx context.Context, field *entity.Field) error
}

// fieldUpsertRepository implements FieldUpsertRepository
type fieldUpsertRepository struct {
	db *sqlx.DB
}

// NewFieldUpsertRepository creates a new field upsert repository
func NewFieldUpsertRepository(db *sqlx.DB) FieldUpsertRepository {
	return &fieldUpsertRepository{db: db}
}

// UpsertField inserts or updates a field
func (r *fieldUpsertRepository) UpsertField(ctx context.Context, field *entity.Field) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "FieldUpsertRepository"), vlog.F("method", "UpsertField"))

	query := `
		INSERT INTO public.field (uuid, name, description, icon, "position", enabled, area_uuid, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		ON CONFLICT (uuid) 
		DO UPDATE SET 
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			"position" = EXCLUDED."position",
			enabled = EXCLUDED.enabled,
			area_uuid = EXCLUDED.area_uuid,
			updated_at = EXCLUDED.updated_at
	`

	// Prepare arguments
	args := []interface{}{
		field.ID(),
		field.Name(),
		field.Description(),
		field.Icon(),
		field.Position(),
		field.Enabled(),
		field.AreaID(),
		field.CreatedAt(),
		field.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", query), vlog.F("args", args))

	_, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		// Check if it's a name constraint violation
		if strings.Contains(err.Error(), "field_name_key") {
			logger.Error("field name constraint violation", vlog.F("error", err), vlog.F("field_name", field.Name()))
			return &FieldNameExistsError{Name: field.Name()}
		}

		logger.Error("failed to upsert field", vlog.F("error", err))
		return fmt.Errorf("failed to upsert field: %w", err)
	}

	logger.Info("field upserted successfully", vlog.F("id", field.ID()), vlog.F("name", field.Name()))
	return nil
}
