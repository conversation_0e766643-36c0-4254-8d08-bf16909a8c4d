package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// FieldEventSubscriber defines the interface for subscribing to field events
type FieldEventSubscriber interface {
	// Subscribe starts listening to field events
	Subscribe(ctx context.Context, processor interface {
		ProcessMessage(ctx context.Context, message *FieldEventMessage) error
	}) error

	// Start starts the subscription service
	Start(ctx context.Context) error

	// Stop stops the subscription service
	Stop() error

	// IsRunning returns true if the subscriber is running
	IsRunning() bool
}

// FieldEventListener manages field event subscriptions and processing
type FieldEventListener struct {
	subscriber FieldEventSubscriber
	processor  *FieldUpsertEventProcessor
	logger     vlog.Logger
}

// NewFieldEventListener creates a new field event listener instance
func NewFieldEventListener(cfg config.Configuration, upsertUsecase UpsertFieldUsecase) (*FieldEventListener, error) {
	if cfg.AWS.SQSQueueURLField == "" {
		return nil, fmt.Errorf("AWS_SQS_QUEUE_URL_FIELD is required for field event operations")
	}

	// Initialize SQS event subscriber with field queue URL
	subscriber, err := NewSQSFieldEventSubscriber(cfg, cfg.AWS.SQSQueueURLField)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS event subscriber: %w", err)
	}

	// Create event processor
	processor := NewFieldUpsertEventProcessor(upsertUsecase)

	return &FieldEventListener{
		subscriber: subscriber,
		processor:  processor,
		logger:     vlog.New().With(vlog.F("component", "FieldEventListener")),
	}, nil
}

// Start starts the field event listener
func (l *FieldEventListener) Start(ctx context.Context) error {
	l.logger.Info("starting field event listener")

	// Subscribe to events with the processor
	if err := l.subscriber.Subscribe(ctx, l.processor); err != nil {
		return fmt.Errorf("failed to subscribe to field events: %w", err)
	}

	// Start the subscriber
	if err := l.subscriber.Start(ctx); err != nil {
		return fmt.Errorf("failed to start field event subscriber: %w", err)
	}

	l.logger.Info("field event listener started successfully")
	return nil
}

// Stop stops the field event listener
func (l *FieldEventListener) Stop() error {
	l.logger.Info("stopping field event listener")

	if err := l.subscriber.Stop(); err != nil {
		l.logger.Error("failed to stop field event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to stop field event subscriber: %w", err)
	}

	l.logger.Info("field event listener stopped successfully")
	return nil
}

// IsRunning returns true if the field event listener is running
func (l *FieldEventListener) IsRunning() bool {
	return l.subscriber.IsRunning()
}
