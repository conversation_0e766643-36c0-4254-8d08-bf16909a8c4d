package event

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// SQSFieldEventSubscriber implements FieldEventSubscriber using AWS SQS
type SQSFieldEventSubscriber struct {
	sqsClient *awspkg.SQSClient
	cfg       config.Configuration
	processor interface {
		// ProcessMessage processes a field event message
		ProcessMessage(ctx context.Context, message *FieldEventMessage) error
	}
	logger    vlog.Logger
	isRunning bool
	mutex     sync.RWMutex
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewSQSFieldEventSubscriber creates a new SQS field event subscriber
func NewSQSFieldEventSubscriber(cfg config.Configuration, queueURL string) (*SQSFieldEventSubscriber, error) {
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        queueURL,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS client: %w", err)
	}

	return &SQSFieldEventSubscriber{
		sqsClient: sqsClient,
		cfg:       cfg,
		logger:    vlog.New().With(vlog.F("component", "SQSFieldEventSubscriber")),
		stopChan:  make(chan struct{}),
	}, nil
}

// Subscribe configures the event processor for the subscriber
func (s *SQSFieldEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *FieldEventMessage) error
}) error {
	s.processor = processor
	return nil
}

// Start starts the subscription service
func (s *SQSFieldEventSubscriber) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("field event subscriber is already running")
	}

	if s.processor == nil {
		return fmt.Errorf("event processor is required")
	}

	s.logger.Info("starting field event subscriber")

	s.isRunning = true
	s.wg.Add(1)

	go s.pollMessages(ctx)

	return nil
}

// Stop stops the subscription service
func (s *SQSFieldEventSubscriber) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("stopping field event subscriber")

	close(s.stopChan)
	s.wg.Wait()
	s.isRunning = false

	if s.sqsClient != nil {
		s.sqsClient.Close()
	}

	s.logger.Info("field event subscriber stopped")
	return nil
}

// IsRunning returns true if the subscriber is running
func (s *SQSFieldEventSubscriber) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// pollMessages continuously polls for messages from SQS
func (s *SQSFieldEventSubscriber) pollMessages(ctx context.Context) {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("field event subscriber polling stopped")
			return
		case <-ctx.Done():
			s.logger.Info("field event subscriber context cancelled")
			return
		case <-ticker.C:
			s.processMessages(ctx)
		}
	}
}

// processMessages processes messages from SQS
func (s *SQSFieldEventSubscriber) processMessages(ctx context.Context) {
	if s.processor == nil {
		s.logger.Warn("no processor set for field event subscriber")
		return
	}

	result, err := s.sqsClient.ReceiveMessages(ctx, 10, 20)
	if err != nil {
		s.logger.Error("failed to receive messages from SQS", vlog.F("error", err))
		return
	}

	if len(result.Messages) == 0 {
		return
	}

	for _, message := range result.Messages {
		if err := s.processMessage(ctx, message); err != nil {
			s.logger.Error("failed to process field message", vlog.F("error", err), vlog.F("messageId", *message.MessageId))
		}
	}
}

// processMessage processes a single SQS message
func (s *SQSFieldEventSubscriber) processMessage(ctx context.Context, sqsMessage types.Message) error {
	s.logger.Debug("processing field message", vlog.F("messageId", *sqsMessage.MessageId))

	// Parse the message body
	var fieldMessage FieldEventMessage
	if err := json.Unmarshal([]byte(*sqsMessage.Body), &fieldMessage); err != nil {
		s.logger.Error("failed to unmarshal field message", vlog.F("error", err), vlog.F("messageId", *sqsMessage.MessageId))
		return fmt.Errorf("failed to unmarshal field message: %w", err)
	}

	if err := s.processor.ProcessMessage(ctx, &fieldMessage); err != nil {
		s.logger.Error("failed to process field message", vlog.F("error", err), vlog.F("messageId", *sqsMessage.MessageId))
		return fmt.Errorf("failed to process field message: %w", err)
	}

	// Delete the message from SQS after successful processing
	if _, err := s.sqsClient.DeleteMessage(ctx, *sqsMessage.ReceiptHandle); err != nil {
		s.logger.Error("failed to delete field message from SQS", vlog.F("error", err), vlog.F("messageId", *sqsMessage.MessageId))
		return fmt.Errorf("failed to delete field message from SQS: %w", err)
	}

	s.logger.Debug("field message processed successfully", vlog.F("messageId", *sqsMessage.MessageId))
	return nil
}
