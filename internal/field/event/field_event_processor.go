package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertFieldUsecase defines the interface for field upsert operations
type UpsertFieldUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertFieldInput) (*usecase.UpsertFieldOutput, error)
}

// FieldUpsertEventProcessor processes field upsert events
type FieldUpsertEventProcessor struct {
	upsertUsecase UpsertFieldUsecase
}

// NewFieldUpsertEventProcessor creates a new field upsert event processor
func NewFieldUpsertEventProcessor(upsertUsecase UpsertFieldUsecase) *FieldUpsertEventProcessor {
	return &FieldUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// ProcessMessage processes a field event message
func (p *FieldUpsertEventProcessor) ProcessMessage(ctx context.Context, message *FieldEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "FieldUpsertEventProcessor"))
	logger.Debug("handling field event message", vlog.F("event_type", message.EventType))

	// Validate message data
	if message.Data.Name == "" {
		logger.Error("validation failed", vlog.F("error", "field name is required"))
		return core.NewBusinessError("field name is required in message data")
	}

	// Validate
	if message.Data.ID != nil && *message.Data.ID != "" {
		_, err := core.NewIDFromString(*message.Data.ID)
		if err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.Data.ID))
			return core.NewBusinessError("invalid UUID format in message data")
		}
	}

	if message.Data.AreaID != nil && *message.Data.AreaID != "" {
		_, err := core.NewIDFromString(*message.Data.AreaID)
		if err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid area UUID format"), vlog.F("area_id", *message.Data.AreaID))
			return core.NewBusinessError("invalid area UUID format in message data")
		}
	}

	upsertInput := &usecase.UpsertFieldInput{
		ID:          message.Data.ID,
		Name:        message.Data.Name,
		Description: message.Data.Description,
		Icon:        message.Data.Icon,
		Position:    message.Data.Position,
		Enabled:     message.Data.Enabled,
		AreaID:      message.Data.AreaID,
	}

	logger.Info("processing field upsert",
		vlog.F("name", message.Data.Name),
		vlog.F("enabled", message.Data.Enabled),
		vlog.F("has_id", message.Data.ID != nil),
		vlog.F("has_area_id", message.Data.AreaID != nil))

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		logger.Error("failed to process field upsert from message", vlog.F("error", err))
		return fmt.Errorf("failed to process field upsert from message: %w", err)
	}

	logger.Info("successfully processed field upsert", vlog.F("name", message.Data.Name))
	return nil
}
