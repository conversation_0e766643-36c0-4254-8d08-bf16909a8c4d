package event

import (
	"time"
)

// FieldEventData represents the data structure for field events
type FieldEventData struct {
	ID          *string `json:"id,omitempty"`
	Name        string  `json:"name"`
	Description *string `json:"description,omitempty"`
	Icon        *string `json:"icon,omitempty"`
	Position    *int    `json:"position,omitempty"`
	Enabled     bool    `json:"enabled"`
	AreaID      *string `json:"area_id,omitempty"`
}

// FieldEventMessage represents the event message structure for field operations
type FieldEventMessage struct {
	Data      FieldEventData `json:"data"`
	Timestamp time.Time      `json:"timestamp"`
	Source    string         `json:"source"`
	EventType string         `json:"event_type"`
}
