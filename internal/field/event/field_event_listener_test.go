package event_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/field/event"
	"gitlab.viswalslab.com/backend/price-list/internal/field/usecase"
)

// MockFieldEventSubscriber is a mock implementation of FieldEventSubscriber
type MockFieldEventSubscriber struct {
	mock.Mock
}

func (m *MockFieldEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *event.FieldEventMessage) error
}) error {
	args := m.Called(ctx, processor)
	return args.Error(0)
}

func (m *MockFieldEventSubscriber) Start(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockFieldEventSubscriber) Stop() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockFieldEventSubscriber) IsRunning() bool {
	args := m.Called()
	return args.Bool(0)
}

// MockUpsertFieldUsecaseForListener is a mock implementation of UpsertFieldUsecase
type MockUpsertFieldUsecaseForListener struct {
	mock.Mock
}

func (m *MockUpsertFieldUsecaseForListener) Execute(ctx context.Context, input *usecase.UpsertFieldInput) (*usecase.UpsertFieldOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.UpsertFieldOutput), args.Error(1)
}

func TestNewFieldEventListener_MissingQueueURL(t *testing.T) {
	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLField: "", // Missing queue URL
			Region:           "us-east-1",
			AccessKeyID:      "test-access-key",
			SecretAccessKey:  "test-secret-key",
		},
	}

	mockUsecase := new(MockUpsertFieldUsecaseForListener)

	listener, err := event.NewFieldEventListener(cfg, mockUsecase)

	assert.Error(t, err)
	assert.Nil(t, listener)
	assert.Contains(t, err.Error(), "AWS_SQS_QUEUE_URL_FIELD is required")
}

func TestNewFieldEventListener_ValidConfig(t *testing.T) {
	// Skip this test as it requires real AWS credentials for SQS initialization
	t.Skip("Skipping event listener test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLField: "https://sqs.us-east-1.amazonaws.com/123456789/field-queue",
			Region:           "us-east-1",
			AccessKeyID:      "test-access-key",
			SecretAccessKey:  "test-secret-key",
		},
	}

	mockUsecase := new(MockUpsertFieldUsecaseForListener)

	listener, err := event.NewFieldEventListener(cfg, mockUsecase)

	assert.NoError(t, err)
	assert.NotNil(t, listener)
}
