package usecase_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field/service"
	"gitlab.viswalslab.com/backend/price-list/internal/field/usecase"
)

// MockFieldUpsertRepository is a mock implementation of FieldUpsertRepository
type MockFieldUpsertRepository struct {
	mock.Mock
}

func (m *MockFieldUpsertRepository) UpsertField(ctx context.Context, field *entity.Field) error {
	args := m.Called(ctx, field)
	return args.Error(0)
}

// MockFieldValidationService is a mock implementation of FieldValidationService
type MockFieldValidationService struct {
	mock.Mock
}

func (m *MockFieldValidationService) ValidateUpsertInput(ctx context.Context, input *service.UpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func (m *MockFieldValidationService) ValidateBusinessRules(ctx context.Context, field *entity.Field) error {
	args := m.Called(ctx, field)
	return args.Error(0)
}

func TestNewUpsertFieldUsecase(t *testing.T) {
	mockRepo := new(MockFieldUpsertRepository)
	mockValidationService := new(MockFieldValidationService)

	uc := usecase.NewUpsertFieldUsecase(mockRepo, mockValidationService)

	assert.NotNil(t, uc)
}

func TestUpsertFieldUsecase_Execute(t *testing.T) {
	validID := core.NewID()
	validAreaID := core.NewID()

	tests := []struct {
		name           string
		input          *usecase.UpsertFieldInput
		setupMocks     func(*MockFieldUpsertRepository, *MockFieldValidationService)
		wantErr        bool
		wantErrMessage string
		expectedOutput *usecase.UpsertFieldOutput
	}{
		{
			name: "Successful upsert with ID",
			input: &usecase.UpsertFieldInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Cardiology",
				Description: core.ToPtr("Heart-related medical specialty"),
				Icon:        core.ToPtr("heart-icon"),
				Position:    core.ToPtr(1),
				Enabled:     true,
				AreaID:      core.ToPtr(validAreaID.Value()),
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
				mockRepo.On("UpsertField", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertFieldOutput{
				ID:          validID.Value(),
				Name:        "Cardiology",
				Description: core.ToPtr("Heart-related medical specialty"),
				Icon:        core.ToPtr("heart-icon"),
				Position:    core.ToPtr(1),
				Enabled:     true,
				AreaID:      core.ToPtr(validAreaID.Value()),
			},
		},
		{
			name: "Successful upsert without ID",
			input: &usecase.UpsertFieldInput{
				Name:    "General Practice",
				Enabled: true,
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
				mockRepo.On("UpsertField", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertFieldOutput{
				Name:    "General Practice",
				Enabled: true,
			},
		},
		{
			name: "Successful upsert with minimal fields",
			input: &usecase.UpsertFieldInput{
				Name:    "Emergency Medicine",
				Enabled: true,
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
				mockRepo.On("UpsertField", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertFieldOutput{
				Name:    "Emergency Medicine",
				Enabled: true,
			},
		},
		{
			name:           "Nil input",
			input:          nil,
			setupMocks:     func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {},
			wantErr:        true,
			wantErrMessage: "input is required",
		},
		{
			name: "Empty field name",
			input: &usecase.UpsertFieldInput{
				Name:    "",
				Enabled: true,
			},
			setupMocks:     func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {},
			wantErr:        true,
			wantErrMessage: "field name is required",
		},
		{
			name: "Invalid UUID format",
			input: &usecase.UpsertFieldInput{
				ID:      core.ToPtr("invalid-uuid"),
				Name:    "Test Field",
				Enabled: true,
			},
			setupMocks:     func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format",
		},
		{
			name: "Service validation error",
			input: &usecase.UpsertFieldInput{
				Name:    "Test Field",
				Enabled: true,
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(errors.New("service validation failed"))
			},
			wantErr:        true,
			wantErrMessage: "service validation failed",
		},
		{
			name: "Business rules validation error",
			input: &usecase.UpsertFieldInput{
				Name:    "Test Field",
				Enabled: true,
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(errors.New("business rules validation failed"))
			},
			wantErr:        true,
			wantErrMessage: "business rules validation failed",
		},
		{
			name: "Repository error",
			input: &usecase.UpsertFieldInput{
				Name:    "Test Field",
				Enabled: true,
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
				mockRepo.On("UpsertField", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(errors.New("database error"))
			},
			wantErr:        true,
			wantErrMessage: "database error occurred while upserting field",
		},
		{
			name: "Name constraint violation",
			input: &usecase.UpsertFieldInput{
				Name:    "duplicate-name",
				Enabled: true,
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
				nameExistsErr := &repository.FieldNameExistsError{Name: "duplicate-name"}
				mockRepo.On("UpsertField", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nameExistsErr)
			},
			wantErr:        true,
			wantErrMessage: "Field name 'duplicate-name' already exists",
		},
		{
			name: "Successful upsert with disabled field",
			input: &usecase.UpsertFieldInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "Obsolete Field",
				Enabled: false,
			},
			setupMocks: func(mockRepo *MockFieldUpsertRepository, mockValidation *MockFieldValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
				mockRepo.On("UpsertField", mock.Anything, mock.AnythingOfType("*entity.Field")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertFieldOutput{
				ID:      validID.Value(),
				Name:    "Obsolete Field",
				Enabled: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := new(MockFieldUpsertRepository)
			mockValidationService := new(MockFieldValidationService)

			tt.setupMocks(mockRepo, mockValidationService)

			uc := usecase.NewUpsertFieldUsecase(mockRepo, mockValidationService)
			output, err := uc.Execute(context.Background(), tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
				assert.Nil(t, output)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, output)
				if tt.expectedOutput != nil {
					assert.Equal(t, tt.expectedOutput.Name, output.Name)
					assert.Equal(t, tt.expectedOutput.Enabled, output.Enabled)
					assert.Equal(t, tt.expectedOutput.Description, output.Description)
					assert.Equal(t, tt.expectedOutput.Icon, output.Icon)
					assert.Equal(t, tt.expectedOutput.Position, output.Position)
					assert.Equal(t, tt.expectedOutput.AreaID, output.AreaID)
					if tt.expectedOutput.ID != "" {
						assert.Equal(t, tt.expectedOutput.ID, output.ID)
					} else {
						// For cases without ID, just verify it's a valid UUID
						_, err := core.NewIDFromString(output.ID)
						assert.NoError(t, err)
					}
				}
			}

			mockRepo.AssertExpectations(t)
			mockValidationService.AssertExpectations(t)
		})
	}
}
