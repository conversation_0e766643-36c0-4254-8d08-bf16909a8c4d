package field_test

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/field"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewFactory(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLField: "https://sqs.us-east-1.amazonaws.com/123456789/field-queue",
			Region:           "us-east-1",
			AccessKeyID:      "test-access-key",
			SecretAccessKey:  "test-secret-key",
		},
	}

	factory, err := field.NewFactory(db, cfg)

	assert.NoError(t, err)
	assert.NotNil(t, factory)
	assert.NotNil(t, factory.FieldEventListener)
}

func TestNewFactory_MissingQueueURL(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLField: "", // Missing queue URL
			Region:           "us-east-1",
			AccessKeyID:      "test-access-key",
			SecretAccessKey:  "test-secret-key",
		},
	}

	factory, err := field.NewFactory(db, cfg)

	assert.Error(t, err)
	assert.Nil(t, factory)
	assert.Contains(t, err.Error(), "AWS_SQS_QUEUE_URL_FIELD is required")
}

func TestFactory_Structure(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory structure test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLField: "https://sqs.us-east-1.amazonaws.com/123456789/field-queue",
			Region:           "us-east-1",
			AccessKeyID:      "test-access-key",
			SecretAccessKey:  "test-secret-key",
		},
	}

	factory, err := field.NewFactory(db, cfg)
	assert.NoError(t, err)

	// Verify factory structure
	assert.NotNil(t, factory)

	// Test that all required services are available
	assert.NotNil(t, factory.FieldEventListener)
}

func TestFactory_UsecaseAdapter(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping usecase adapter test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLField: "https://sqs.us-east-1.amazonaws.com/123456789/field-queue",
			Region:           "us-east-1",
			AccessKeyID:      "test-access-key",
			SecretAccessKey:  "test-secret-key",
		},
	}

	factory, err := field.NewFactory(db, cfg)
	assert.NoError(t, err)
	assert.NotNil(t, factory)

	assert.NotNil(t, factory.FieldEventListener)
}
