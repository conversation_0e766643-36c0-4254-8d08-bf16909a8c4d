package repository

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
)

// AccountBranchAreaUpsertRepository interface for SQS upsert functionality
type AccountBranchAreaUpsertRepository interface {
	UpsertAccountBranchArea(ctx context.Context, accountBranchArea *entity.AccountBranchArea) error
}

type accountBranchAreaRepository struct {
	db *sqlx.DB
}

// NewAccountBranchAreaUpsertRepository creates a new account branch area repository for upsert operations
func NewAccountBranchAreaUpsertRepository(db *sqlx.DB) AccountBranchAreaUpsertRepository {
	return &accountBranchAreaRepository{db: db}
}
