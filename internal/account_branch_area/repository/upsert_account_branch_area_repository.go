package repository

import (
	"context"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
)

// AccountBranchAreaMappingExistsError represents a domain error when account branch area mapping already exists
type AccountBranchAreaMappingExistsError struct {
	BranchID string
	AreaID   string
}

func (e *AccountBranchAreaMappingExistsError) Error() string {
	return fmt.Sprintf("account branch area mapping '%s-%s' already exists", e.BranchID, e.AreaID)
}

// UpsertAccountBranchArea upserts an account branch area mapping entity into the database
func (r *accountBranchAreaRepository) UpsertAccountBranchArea(ctx context.Context, accountBranchArea *entity.AccountBranchArea) error {
	query := `
		INSERT INTO public.account_branch_area (id, account_id, branch_id, area_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (branch_id, area_id) DO UPDATE SET
			account_id = EXCLUDED.account_id,
			updated_at = EXCLUDED.updated_at
	`

	_, err := r.db.ExecContext(ctx, query,
		accountBranchArea.ID(),
		accountBranchArea.AccountID(),
		accountBranchArea.BranchID(),
		accountBranchArea.AreaID(),
		accountBranchArea.CreatedAt(),
		accountBranchArea.UpdatedAt(),
	)

	if err != nil {
		// Check for unique constraint violation
		if strings.Contains(err.Error(), "account_branch_area_branch_id_area_id_key") {
			return &AccountBranchAreaMappingExistsError{
				BranchID: accountBranchArea.BranchID(),
				AreaID:   accountBranchArea.AreaID(),
			}
		}

		// Check for foreign key constraint violation on area
		if strings.Contains(err.Error(), "area_id_fk") || strings.Contains(err.Error(), "violates foreign key constraint") {
			return fmt.Errorf("area ID '%s' does not exist", accountBranchArea.AreaID())
		}

		return err
	}

	return nil
}
