package repository

import (
	"context"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
	sql_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/account_branch_area/sql"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	return sql_mocks.SetupTestDB(t)
}

func createTestAccountBranchArea(t *testing.T) *entity.AccountBranchArea {
	validID := core.NewID()
	input := &entity.NewAccountBranchAreaInput{
		AccountID: validID.Value(),
		BranchID:  validID.Value(),
		AreaID:    validID.Value(),
	}
	aba, err := entity.NewAccountBranchArea(input)
	assert.NoError(t, err)
	return aba
}

func TestNewAccountBranchAreaUpsertRepository(t *testing.T) {
	db, _ := setupMockDB(t)
	defer db.Close()

	repo := NewAccountBranchAreaUpsertRepository(db)
	assert.NotNil(t, repo)
	assert.Implements(t, (*AccountBranchAreaUpsertRepository)(nil), repo)
}

func TestUpsertAccountBranchArea_Success(t *testing.T) {
	db, mock := setupMockDB(t)
	defer db.Close()

	aba := createTestAccountBranchArea(t)

	// Expect the upsert query
	mock.ExpectExec(`INSERT INTO public.account_branch_area`).
		WithArgs(aba.ID(), aba.AccountID(), aba.BranchID(), aba.AreaID(), aba.CreatedAt(), aba.UpdatedAt()).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := NewAccountBranchAreaUpsertRepository(db)
	err := repo.UpsertAccountBranchArea(context.Background(), aba)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestUpsertAccountBranchArea_UniqueConstraintViolation(t *testing.T) {
	db, mock := setupMockDB(t)
	defer db.Close()

	aba := createTestAccountBranchArea(t)

	// Simulate unique constraint violation
	mock.ExpectExec(`INSERT INTO public.account_branch_area`).
		WithArgs(aba.ID(), aba.AccountID(), aba.BranchID(), aba.AreaID(), aba.CreatedAt(), aba.UpdatedAt()).
		WillReturnError(errors.New("ERROR: duplicate key value violates unique constraint \"account_branch_area_branch_id_area_id_key\""))

	repo := NewAccountBranchAreaUpsertRepository(db)
	err := repo.UpsertAccountBranchArea(context.Background(), aba)

	assert.Error(t, err)
	var mappingExistsErr *AccountBranchAreaMappingExistsError
	assert.ErrorAs(t, err, &mappingExistsErr)
	assert.Equal(t, aba.BranchID(), mappingExistsErr.BranchID)
	assert.Equal(t, aba.AreaID(), mappingExistsErr.AreaID)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestUpsertAccountBranchArea_ForeignKeyViolation(t *testing.T) {
	db, mock := setupMockDB(t)
	defer db.Close()

	aba := createTestAccountBranchArea(t)

	// Simulate foreign key constraint violation
	mock.ExpectExec(`INSERT INTO public.account_branch_area`).
		WithArgs(aba.ID(), aba.AccountID(), aba.BranchID(), aba.AreaID(), aba.CreatedAt(), aba.UpdatedAt()).
		WillReturnError(errors.New("ERROR: insert or update on table \"account_branch_area\" violates foreign key constraint \"area_id_fk\""))

	repo := NewAccountBranchAreaUpsertRepository(db)
	err := repo.UpsertAccountBranchArea(context.Background(), aba)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestUpsertAccountBranchArea_DatabaseError(t *testing.T) {
	db, mock := setupMockDB(t)
	defer db.Close()

	aba := createTestAccountBranchArea(t)

	// Simulate database error
	mock.ExpectExec(`INSERT INTO public.account_branch_area`).
		WithArgs(aba.ID(), aba.AccountID(), aba.BranchID(), aba.AreaID(), aba.CreatedAt(), aba.UpdatedAt()).
		WillReturnError(errors.New("database connection lost"))

	repo := NewAccountBranchAreaUpsertRepository(db)
	err := repo.UpsertAccountBranchArea(context.Background(), aba)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database connection lost")
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAccountBranchAreaMappingExistsError_Error(t *testing.T) {
	validID := core.NewID()
	err := &AccountBranchAreaMappingExistsError{
		BranchID: validID.Value(),
		AreaID:   validID.Value(),
	}

	expectedMsg := "account branch area mapping '" + validID.Value() + "-" + validID.Value() + "' already exists"
	assert.Equal(t, expectedMsg, err.Error())
}
