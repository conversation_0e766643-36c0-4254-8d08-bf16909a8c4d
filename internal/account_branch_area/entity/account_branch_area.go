package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// AccountBranchArea represents a mapping between an account, branch and area
type AccountBranchArea struct {
	id        core.Identifier
	accountID core.Identifier
	branchID  core.Identifier
	areaID    core.Identifier
	createdAt core.Timestamp
	updatedAt core.Timestamp
}

type NewAccountBranchAreaInput struct {
	AccountID string
	BranchID  string
	AreaID    string
	CreatedAt *time.Time
	UpdatedAt *time.Time
}

func NewAccountBranchArea(input *NewAccountBranchAreaInput) (*AccountBranchArea, error) {
	if input == nil {
		panic("NewAccountBranchAreaInput cannot be nil")
	}

	id := core.NewID()

	accountID, err := core.NewIDFromString(input.AccountID)
	if err != nil {
		return nil, err
	}

	branchID, err := core.NewIDFromString(input.BranchID)
	if err != nil {
		return nil, err
	}

	areaID, err := core.NewIDFromString(input.AreaID)
	if err != nil {
		return nil, err
	}

	// Use provided timestamps or default to current time using core timestamp
	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdAt, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, err
	}

	updatedAt, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, err
	}

	return &AccountBranchArea{
		id:        *id,
		accountID: *accountID,
		branchID:  *branchID,
		areaID:    *areaID,
		createdAt: *createdAt,
		updatedAt: *updatedAt,
	}, nil
}

// Getter methods following established patterns
func (aba AccountBranchArea) ID() string {
	return aba.id.Value()
}

func (aba AccountBranchArea) AccountID() string {
	return aba.accountID.Value()
}

func (aba AccountBranchArea) BranchID() string {
	return aba.branchID.Value()
}

func (aba AccountBranchArea) AreaID() string {
	return aba.areaID.Value()
}

func (aba AccountBranchArea) CreatedAt() time.Time {
	return aba.createdAt.Value()
}

func (aba AccountBranchArea) UpdatedAt() time.Time {
	return aba.updatedAt.Value()
}
