package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

var validID = core.NewID()

func TestNewAccountBranchArea(t *testing.T) {
	tests := []struct {
		name    string
		input   *NewAccountBranchAreaInput
		wantErr bool
	}{
		{
			name: "Valid input with all required fields",
			input: &NewAccountBranchAreaInput{
				AccountID: validID.Value(),
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
			},
			wantErr: false,
		},
		{
			name: "Valid input with timestamps",
			input: &NewAccountBranchAreaInput{
				AccountID: validID.Value(),
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				CreatedAt: &time.Time{},
				UpdatedAt: &time.Time{},
			},
			wantErr: true, // Zero time should fail validation
		},
		{
			name: "Invalid account ID",
			input: &NewAccountBranchAreaInput{
				AccountID: "invalid-uuid",
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
			},
			wantErr: true,
		},
		{
			name: "Invalid branch ID",
			input: &NewAccountBranchAreaInput{
				AccountID: validID.Value(),
				BranchID:  "invalid-uuid",
				AreaID:    validID.Value(),
			},
			wantErr: true,
		},
		{
			name: "Invalid area ID",
			input: &NewAccountBranchAreaInput{
				AccountID: validID.Value(),
				BranchID:  validID.Value(),
				AreaID:    "invalid-uuid",
			},
			wantErr: true,
		},
		{
			name: "Empty account ID",
			input: &NewAccountBranchAreaInput{
				AccountID: "",
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
			},
			wantErr: true,
		},
		{
			name: "Empty branch ID",
			input: &NewAccountBranchAreaInput{
				AccountID: validID.Value(),
				BranchID:  "",
				AreaID:    validID.Value(),
			},
			wantErr: true,
		},
		{
			name: "Empty area ID",
			input: &NewAccountBranchAreaInput{
				AccountID: validID.Value(),
				BranchID:  validID.Value(),
				AreaID:    "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			accountBranchArea, err := NewAccountBranchArea(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, accountBranchArea)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, accountBranchArea)

				// Verify all getter methods work correctly
				assert.Equal(t, tt.input.AccountID, accountBranchArea.AccountID())
				assert.Equal(t, tt.input.BranchID, accountBranchArea.BranchID())
				assert.Equal(t, tt.input.AreaID, accountBranchArea.AreaID())

				// Verify timestamps are set (either from input or default)
				assert.NotZero(t, accountBranchArea.CreatedAt())
				assert.NotZero(t, accountBranchArea.UpdatedAt())
				assert.NotEmpty(t, accountBranchArea.ID())

				if tt.input.CreatedAt != nil && !tt.input.CreatedAt.IsZero() {
					assert.Equal(t, tt.input.CreatedAt.UTC(), accountBranchArea.CreatedAt().UTC())
				}
				if tt.input.UpdatedAt != nil && !tt.input.UpdatedAt.IsZero() {
					assert.Equal(t, tt.input.UpdatedAt.UTC(), accountBranchArea.UpdatedAt().UTC())
				}
			}
		})
	}
}

func TestNewAccountBranchArea_PanicOnNilInput(t *testing.T) {
	assert.Panics(t, func() {
		NewAccountBranchArea(nil)
	})
}

func TestAccountBranchAreaGetters(t *testing.T) {
	now := time.Now()
	input := &NewAccountBranchAreaInput{
		AccountID: validID.Value(),
		BranchID:  validID.Value(),
		AreaID:    validID.Value(),
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	accountBranchArea, err := NewAccountBranchArea(input)
	assert.NoError(t, err)
	assert.NotNil(t, accountBranchArea)

	// Test all getter methods
	assert.Equal(t, input.AccountID, accountBranchArea.AccountID())
	assert.Equal(t, input.BranchID, accountBranchArea.BranchID())
	assert.Equal(t, input.AreaID, accountBranchArea.AreaID())
	assert.Equal(t, now.UTC(), accountBranchArea.CreatedAt().UTC())
	assert.Equal(t, now.UTC(), accountBranchArea.UpdatedAt().UTC())
	assert.NotEmpty(t, accountBranchArea.ID())
}

func TestAccountBranchAreaWithDifferentTimestamps(t *testing.T) {
	createdTime := time.Now().Add(-time.Hour)
	updatedTime := time.Now()

	input := &NewAccountBranchAreaInput{
		AccountID: validID.Value(),
		BranchID:  validID.Value(),
		AreaID:    validID.Value(),
		CreatedAt: &createdTime,
		UpdatedAt: &updatedTime,
	}

	accountBranchArea, err := NewAccountBranchArea(input)
	assert.NoError(t, err)
	assert.NotNil(t, accountBranchArea)

	// Verify timestamps are preserved correctly
	assert.Equal(t, createdTime.UTC(), accountBranchArea.CreatedAt().UTC())
	assert.Equal(t, updatedTime.UTC(), accountBranchArea.UpdatedAt().UTC())
	assert.True(t, accountBranchArea.CreatedAt().Before(accountBranchArea.UpdatedAt()))
}

func TestAccountBranchAreaWithNilTimestamps(t *testing.T) {
	input := &NewAccountBranchAreaInput{
		AccountID: validID.Value(),
		BranchID:  validID.Value(),
		AreaID:    validID.Value(),
		CreatedAt: nil,
		UpdatedAt: nil,
	}

	beforeCreate := time.Now()
	accountBranchArea, err := NewAccountBranchArea(input)
	afterCreate := time.Now()

	assert.NoError(t, err)
	assert.NotNil(t, accountBranchArea)

	// Verify default timestamps are set to current time
	assert.True(t, accountBranchArea.CreatedAt().After(beforeCreate) || accountBranchArea.CreatedAt().Equal(beforeCreate))
	assert.True(t, accountBranchArea.CreatedAt().Before(afterCreate) || accountBranchArea.CreatedAt().Equal(afterCreate))
	assert.True(t, accountBranchArea.UpdatedAt().After(beforeCreate) || accountBranchArea.UpdatedAt().Equal(beforeCreate))
	assert.True(t, accountBranchArea.UpdatedAt().Before(afterCreate) || accountBranchArea.UpdatedAt().Equal(afterCreate))
}
