package usecase_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/usecase"
	repository_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/account_branch_area/repository"
	service_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/account_branch_area/service"
)

func TestUpsertAccountBranchAreaUsecase_Execute(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name       string
		input      *usecase.UpsertAccountBranchAreaInput
		setupMocks func(*repository_mocks.MockAccountBranchAreaUpsertRepository, *service_mocks.MockAccountBranchAreaValidationService)
		wantErr    bool
		wantErrMsg string
	}{
		{
			name: "Successful upsert",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AccountBranchArea")).Return(nil)
				mockRepo.On("UpsertAccountBranchArea", mock.Anything, mock.AnythingOfType("*entity.AccountBranchArea")).Return(nil)
			},
			wantErr: false,
		},
		{
			name:  "Nil input",
			input: nil,
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				// No mocks needed for this test case
			},
			wantErr:    true,
			wantErrMsg: "input is required",
		},
		{
			name: "Empty account ID",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: "",
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				// No mocks needed for this test case
			},
			wantErr:    true,
			wantErrMsg: "account ID is required",
		},
		{
			name: "Empty branch ID",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  "",
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				// No mocks needed for this test case
			},
			wantErr:    true,
			wantErrMsg: "branch ID is required",
		},
		{
			name: "Empty area ID",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    "",
				AccountID: validID.Value(),
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				// No mocks needed for this test case
			},
			wantErr:    true,
			wantErrMsg: "area ID is required",
		},
		{
			name: "Invalid account ID format",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: "invalid-uuid",
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				// No mocks needed for this test case
			},
			wantErr:    true,
			wantErrMsg: "invalid account ID format",
		},
		{
			name: "Service validation failure",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(core.NewBusinessError("service validation failed"))
			},
			wantErr:    true,
			wantErrMsg: "service validation failed",
		},
		{
			name: "Business rules validation failure",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AccountBranchArea")).Return(core.NewBusinessError("business rules failed"))
			},
			wantErr:    true,
			wantErrMsg: "business rules failed",
		},
		{
			name: "Repository upsert failure",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AccountBranchArea")).Return(nil)
				mockRepo.On("UpsertAccountBranchArea", mock.Anything, mock.AnythingOfType("*entity.AccountBranchArea")).Return(errors.New("database error"))
			},
			wantErr:    true,
			wantErrMsg: "database error occurred while upserting account branch area",
		},
		{
			name: "Mapping exists error",
			input: &usecase.UpsertAccountBranchAreaInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			setupMocks: func(mockRepo *repository_mocks.MockAccountBranchAreaUpsertRepository, mockValidation *service_mocks.MockAccountBranchAreaValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.AccountBranchArea")).Return(nil)
				mappingErr := &repository.AccountBranchAreaMappingExistsError{
					BranchID: validID.Value(),
					AreaID:   validID.Value(),
				}
				mockRepo.On("UpsertAccountBranchArea", mock.Anything, mock.AnythingOfType("*entity.AccountBranchArea")).Return(mappingErr)
			},
			wantErr:    true,
			wantErrMsg: "already exists",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			mockRepo := new(repository_mocks.MockAccountBranchAreaUpsertRepository)
			mockValidation := new(service_mocks.MockAccountBranchAreaValidationService)

			// Setup mocks
			tt.setupMocks(mockRepo, mockValidation)

			// Create usecase
			uc := usecase.NewUpsertAccountBranchAreaUsecase(mockRepo, mockValidation)

			// Execute
			ctx := context.Background()
			err := uc.Execute(ctx, tt.input)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMsg != "" {
					assert.Contains(t, err.Error(), tt.wantErrMsg)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all expectations were met
			mockRepo.AssertExpectations(t)
			mockValidation.AssertExpectations(t)
		})
	}
}

func TestNewUpsertAccountBranchAreaUsecase(t *testing.T) {
	mockRepo := new(repository_mocks.MockAccountBranchAreaUpsertRepository)
	mockValidation := new(service_mocks.MockAccountBranchAreaValidationService)

	uc := usecase.NewUpsertAccountBranchAreaUsecase(mockRepo, mockValidation)

	assert.NotNil(t, uc)
}
