package usecase

import (
	"context"
	"errors"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var upsertAccountBranchAreaUsecaseName = "UpsertAccountBranchAreaUsecase"

type AccountBranchAreaUpsertRepository interface {
	UpsertAccountBranchArea(ctx context.Context, accountBranchArea *entity.AccountBranchArea) error
}

// UpsertAccountBranchAreaInput following Input naming pattern
type UpsertAccountBranchAreaInput struct {
	BranchID  string
	AreaID    string
	AccountID string // account ID for the mapping
}

type UpsertAccountBranchAreaUsecase struct {
	repo              AccountBranchAreaUpsertRepository
	validationService service.AccountBranchAreaValidationService
}

func NewUpsertAccountBranchAreaUsecase(repo AccountBranchAreaUpsertRepository, validationService service.AccountBranchAreaValidationService) *UpsertAccountBranchAreaUsecase {
	return &UpsertAccountBranchAreaUsecase{
		repo:              repo,
		validationService: validationService,
	}
}

func (uc *UpsertAccountBranchAreaUsecase) Execute(ctx context.Context, input *UpsertAccountBranchAreaInput) error {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", upsertAccountBranchAreaUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	// Validate input
	if err := uc.validateInput(input); err != nil {
		logger.Error("validation failed", vlog.F("error", err))
		return err
	}

	// Validate using service layer business rules
	serviceInput := &service.UpsertValidationInput{
		BranchID:  input.BranchID,
		AreaID:    input.AreaID,
		AccountID: input.AccountID,
	}
	if err := uc.validationService.ValidateUpsertInput(ctx, serviceInput); err != nil {
		logger.Error("service validation failed", vlog.F("error", err))
		return err
	}

	// Create account branch area entity using NewAccountBranchAreaInput pattern
	entityInput := &entity.NewAccountBranchAreaInput{
		BranchID:  input.BranchID,
		AreaID:    input.AreaID,
		AccountID: input.AccountID,
	}

	accountBranchArea, err := entity.NewAccountBranchArea(entityInput)
	if err != nil {
		logger.Error("failed to create account branch area entity", vlog.F("error", err))
		return core.NewBusinessError("failed to create account branch area entity: %v", err)
	}

	// Validate business rules using service layer
	if err := uc.validationService.ValidateBusinessRules(ctx, accountBranchArea); err != nil {
		logger.Error("business rules validation failed", vlog.F("error", err))
		return err
	}

	// Upsert account branch area entity
	if err := uc.repo.UpsertAccountBranchArea(ctx, accountBranchArea); err != nil {
		// Check if this is a mapping constraint violation
		var mappingExistsErr *repository.AccountBranchAreaMappingExistsError
		if errors.As(err, &mappingExistsErr) {
			logger.Error("Account branch area mapping constraint violation - message will go to DLQ",
				vlog.F("error", err),
				vlog.F("branch_id", accountBranchArea.BranchID()),
				vlog.F("area_id", accountBranchArea.AreaID()))
			return core.NewBusinessError("Account branch area mapping '%s-%s' already exists", mappingExistsErr.BranchID, mappingExistsErr.AreaID)
		}

		logger.Error("failed to upsert account branch area", vlog.F("error", err))
		return core.NewBusinessError("database error occurred while upserting account branch area: %v", err)
	}

	logger.Debug("execution finished",
		vlog.F("branch_id", accountBranchArea.BranchID()),
		vlog.F("area_id", accountBranchArea.AreaID()),
		vlog.F("account_id", accountBranchArea.AccountID()))
	return nil
}

// validateInput validates the input using core validation patterns
func (uc *UpsertAccountBranchAreaUsecase) validateInput(input *UpsertAccountBranchAreaInput) error {
	if input == nil {
		return core.NewBusinessError("input is required")
	}

	// Use core validation patterns
	if input.AccountID == "" {
		return core.NewBusinessError("account ID is required")
	}
	if input.BranchID == "" {
		return core.NewBusinessError("branch ID is required")
	}
	if input.AreaID == "" {
		return core.NewBusinessError("area ID is required")
	}

	// Validate ID formats using core
	if _, err := core.NewIDFromString(input.AccountID); err != nil {
		return core.NewBusinessError("invalid account ID format: %v", err)
	}
	if _, err := core.NewIDFromString(input.BranchID); err != nil {
		return core.NewBusinessError("invalid branch ID format: %v", err)
	}
	if _, err := core.NewIDFromString(input.AreaID); err != nil {
		return core.NewBusinessError("invalid area ID format: %v", err)
	}

	return nil
}
