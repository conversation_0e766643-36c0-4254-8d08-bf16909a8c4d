package event

import (
	"time"
)

// AccountBranchAreaMapping represents a single branch with its associated areas
type AccountBranchAreaMapping struct {
	Branch string   `json:"branch"`
	Areas  []string `json:"areas"`
}

// AccountBranchAreaEventData represents the data structure for account branch area mapping events
type AccountBranchAreaEventData struct {
	AccountID         string                     `json:"account_id"`  // branch manager userID
	AccountBranchArea []AccountBranchAreaMapping `json:"branch_area"` // array of branch-area mappings
}

// AccountBranchAreaEventMessage represents the event message structure for account branch area mapping operations
type AccountBranchAreaEventMessage struct {
	Data      AccountBranchAreaEventData `json:"data"`
	Timestamp time.Time                  `json:"timestamp"`
	Source    string                     `json:"source"`
	EventType string                     `json:"event_type"`
}
