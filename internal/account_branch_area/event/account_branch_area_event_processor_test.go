package event_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/event"
	usecase_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/account_branch_area/usecase"
)

func TestNewAccountBranchAreaUpsertEventProcessor(t *testing.T) {
	mockUsecase := new(usecase_mocks.MockUpsertAccountBranchAreaUsecase)
	processor := event.NewAccountBranchAreaUpsertEventProcessor(mockUsecase)

	assert.NotNil(t, processor)
}

func TestAccountBranchAreaUpsertEventProcessor_ProcessMessage(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name       string
		message    *event.AccountBranchAreaEventMessage
		setupMocks func(*usecase_mocks.MockUpsertAccountBranchAreaUsecase)
		wantErr    bool
		wantErrMsg string
	}{
		{
			name: "Successful processing with single branch and area",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {
				mockUsecase.On("Execute", mock.Anything, mock.AnythingOfType("*usecase.UpsertAccountBranchAreaInput")).Return(nil)
			},
			wantErr: false,
		},
		{
			name: "Successful processing with multiple areas",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{validID.Value(), validID.Value(), validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {
				mockUsecase.On("Execute", mock.Anything, mock.AnythingOfType("*usecase.UpsertAccountBranchAreaInput")).Return(nil).Times(3)
			},
			wantErr: false,
		},
		{
			name: "Successful processing with multiple branches",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{validID.Value()},
						},
						{
							Branch: validID.Value(),
							Areas:  []string{validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {
				mockUsecase.On("Execute", mock.Anything, mock.AnythingOfType("*usecase.UpsertAccountBranchAreaInput")).Return(nil).Times(2)
			},
			wantErr: false,
		},
		{
			name:       "Nil message",
			message:    nil,
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "message is required",
		},
		{
			name: "Empty account ID",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: "",
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "account ID is required",
		},
		{
			name: "Invalid account ID format",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: "invalid-uuid",
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "invalid account ID format",
		},
		{
			name: "Empty mappings",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID:         validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "account branch area mappings are required",
		},
		{
			name: "Empty branch ID",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: "",
							Areas:  []string{validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "branch UUID is required",
		},
		{
			name: "Invalid branch ID format",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: "invalid-uuid",
							Areas:  []string{validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "invalid branch UUID format",
		},
		{
			name: "Empty areas",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "areas are required",
		},
		{
			name: "Empty area ID",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{""},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "area UUID is required",
		},
		{
			name: "Invalid area ID format",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{"invalid-uuid"},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {},
			wantErr:    true,
			wantErrMsg: "invalid area UUID format",
		},
		{
			name: "Usecase execution failure",
			message: &event.AccountBranchAreaEventMessage{
				Data: event.AccountBranchAreaEventData{
					AccountID: validID.Value(),
					AccountBranchArea: []event.AccountBranchAreaMapping{
						{
							Branch: validID.Value(),
							Areas:  []string{validID.Value()},
						},
					},
				},
				Timestamp: time.Now(),
				Source:    "test",
				EventType: "account-branch-area.upsert",
			},
			setupMocks: func(mockUsecase *usecase_mocks.MockUpsertAccountBranchAreaUsecase) {
				mockUsecase.On("Execute", mock.Anything, mock.AnythingOfType("*usecase.UpsertAccountBranchAreaInput")).Return(errors.New("usecase error"))
			},
			wantErr:    true,
			wantErrMsg: "failed to process account branch area upsert",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock
			mockUsecase := new(usecase_mocks.MockUpsertAccountBranchAreaUsecase)

			// Setup mocks
			tt.setupMocks(mockUsecase)

			// Create processor
			processor := event.NewAccountBranchAreaUpsertEventProcessor(mockUsecase)

			// Execute
			ctx := context.Background()
			err := processor.ProcessMessage(ctx, tt.message)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMsg != "" {
					assert.Contains(t, err.Error(), tt.wantErrMsg)
				}
			} else {
				assert.NoError(t, err)
			}

			// Verify all expectations were met
			mockUsecase.AssertExpectations(t)
		})
	}
}
