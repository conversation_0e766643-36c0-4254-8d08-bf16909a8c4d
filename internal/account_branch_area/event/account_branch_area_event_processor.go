package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertAccountBranchAreaUsecase defines the interface for account branch area upsert operations
type UpsertAccountBranchAreaUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertAccountBranchAreaInput) error
}

// AccountBranchAreaUpsertEventProcessor processes account branch area upsert events
type AccountBranchAreaUpsertEventProcessor struct {
	upsertUsecase UpsertAccountBranchAreaUsecase
}

// NewAccountBranchAreaUpsertEventProcessor creates a new account branch area upsert event processor
func NewAccountBranchAreaUpsertEventProcessor(upsertUsecase UpsertAccountBranchAreaUsecase) *AccountBranchAreaUpsertEventProcessor {
	return &AccountBranchAreaUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// ProcessMessage processes an account branch area event message with batch operations
func (p *AccountBranchAreaUpsertEventProcessor) ProcessMessage(ctx context.Context, message *AccountBranchAreaEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "AccountBranchAreaUpsertEventProcessor"))
	logger.Debug("handling account branch area event message", vlog.F("event_type", message.EventType), vlog.F("account_id", message.Data.AccountID))

	// Validate message data
	if message.Data.AccountID == "" {
		logger.Error("validation failed", vlog.F("error", "account ID is required"))
		return core.NewBusinessError("account ID is required in message data")
	}

	if len(message.Data.AccountBranchArea) == 0 {
		logger.Error("validation failed", vlog.F("error", "account branch area mappings are required"))
		return core.NewBusinessError("account branch area mappings are required in message data")
	}

	// Validate account ID format
	if _, err := core.NewIDFromString(message.Data.AccountID); err != nil {
		logger.Error("validation failed", vlog.F("error", "invalid account ID format"), vlog.F("account_id", message.Data.AccountID))
		return core.NewBusinessError("invalid account ID format in message data: %v", err)
	}

	// Process each account-branch-area mapping
	for _, mapping := range message.Data.AccountBranchArea {
		if err := p.processAccountBranchAreaMapping(ctx, logger, mapping, message.Data.AccountID); err != nil {
			return err
		}
	}

	logger.Info("successfully processed all account branch area mappings",
		vlog.F("account_id", message.Data.AccountID),
		vlog.F("mappings_count", len(message.Data.AccountBranchArea)))

	return nil
}

// processAccountBranchAreaMapping processes a single account-branch-area mapping
func (p *AccountBranchAreaUpsertEventProcessor) processAccountBranchAreaMapping(ctx context.Context, logger vlog.Logger, mapping AccountBranchAreaMapping, accountID string) error {
	// Validate branch UUID
	if mapping.Branch == "" {
		logger.Error("validation failed", vlog.F("error", "branch UUID is required"))
		return core.NewBusinessError("branch UUID is required in mapping")
	}

	if _, err := core.NewIDFromString(mapping.Branch); err != nil {
		logger.Error("validation failed", vlog.F("error", "invalid branch UUID format"), vlog.F("branch_uuid", mapping.Branch))
		return core.NewBusinessError("invalid branch UUID format: %v", err)
	}

	// Validate areas
	if len(mapping.Areas) == 0 {
		logger.Error("validation failed", vlog.F("error", "areas are required"))
		return core.NewBusinessError("areas are required for branch %s", mapping.Branch)
	}

	// Process each area for this branch
	for _, areaUUID := range mapping.Areas {
		if areaUUID == "" {
			logger.Error("validation failed", vlog.F("error", "area UUID is required"))
			return core.NewBusinessError("area UUID is required")
		}

		if _, err := core.NewIDFromString(areaUUID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid area UUID format"), vlog.F("area_uuid", areaUUID))
			return core.NewBusinessError("invalid area UUID format: %v", err)
		}

		// Convert to usecase input
		upsertInput := &usecase.UpsertAccountBranchAreaInput{
			BranchID:  mapping.Branch,
			AreaID:    areaUUID,
			AccountID: accountID, // Include account ID for the mapping
		}

		// Execute the upsert operation
		ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
		err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
		if err != nil {
			logger.Error("failed to process account branch area upsert",
				vlog.F("error", err),
				vlog.F("branch_id", mapping.Branch),
				vlog.F("area_id", areaUUID))
			return fmt.Errorf("failed to process account branch area upsert for branch %s, area %s: %w", mapping.Branch, areaUUID, err)
		}

		logger.Debug("processed account branch area mapping",
			vlog.F("branch_id", mapping.Branch),
			vlog.F("area_id", areaUUID),
			vlog.F("account_id", accountID))
	}

	return nil
}
