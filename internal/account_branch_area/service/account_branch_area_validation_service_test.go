package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
)

func TestNewAccountBranchAreaValidationService(t *testing.T) {
	service := NewAccountBranchAreaValidationService()
	assert.NotNil(t, service)
	assert.Implements(t, (*AccountBranchAreaValidationService)(nil), service)
}

func TestValidateBusinessRules(t *testing.T) {
	service := NewAccountBranchAreaValidationService()
	ctx := context.Background()

	validID := core.NewID()

	tests := []struct {
		name               string
		setupEntity        func() *entity.AccountBranchArea
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid entity",
			setupEntity: func() *entity.AccountBranchArea {
				input := &entity.NewAccountBranchAreaInput{
					AccountID: validID.Value(),
					BranchID:  validID.Value(),
					AreaID:    validID.Value(),
				}
				aba, _ := entity.NewAccountBranchArea(input)
				return aba
			},
			wantErr: false,
		},
		{
			name: "Nil entity",
			setupEntity: func() *entity.AccountBranchArea {
				return nil
			},
			wantErr:            true,
			expectedErrMessage: "account branch area entity is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := tt.setupEntity()
			err := service.ValidateBusinessRules(ctx, entity)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpsertInput(t *testing.T) {
	service := NewAccountBranchAreaValidationService()
	ctx := context.Background()

	validID := core.NewID()

	tests := []struct {
		name               string
		input              *UpsertValidationInput
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid input",
			input: &UpsertValidationInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			wantErr: false,
		},
		{
			name:               "Nil input",
			input:              nil,
			wantErr:            true,
			expectedErrMessage: "upsert input is required",
		},
		{
			name: "Empty account ID",
			input: &UpsertValidationInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: "",
			},
			wantErr:            true,
			expectedErrMessage: "account ID is required",
		},
		{
			name: "Empty branch ID",
			input: &UpsertValidationInput{
				BranchID:  "",
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			wantErr:            true,
			expectedErrMessage: "branch ID is required",
		},
		{
			name: "Empty area ID",
			input: &UpsertValidationInput{
				BranchID:  validID.Value(),
				AreaID:    "",
				AccountID: validID.Value(),
			},
			wantErr:            true,
			expectedErrMessage: "area ID is required",
		},
		{
			name: "Invalid account ID format",
			input: &UpsertValidationInput{
				BranchID:  validID.Value(),
				AreaID:    validID.Value(),
				AccountID: "invalid-uuid",
			},
			wantErr:            true,
			expectedErrMessage: "invalid account ID format",
		},
		{
			name: "Invalid branch ID format",
			input: &UpsertValidationInput{
				BranchID:  "invalid-uuid",
				AreaID:    validID.Value(),
				AccountID: validID.Value(),
			},
			wantErr:            true,
			expectedErrMessage: "invalid branch ID format",
		},
		{
			name: "Invalid area ID format",
			input: &UpsertValidationInput{
				BranchID:  validID.Value(),
				AreaID:    "invalid-uuid",
				AccountID: validID.Value(),
			},
			wantErr:            true,
			expectedErrMessage: "invalid area ID format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
