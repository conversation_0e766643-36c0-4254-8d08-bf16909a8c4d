package service

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/entity"
)

// AccountBranchAreaValidationService provides validation operations for account branch area mapping domain
type AccountBranchAreaValidationService interface {
	ValidateBusinessRules(ctx context.Context, accountBranchArea *entity.AccountBranchArea) error
	ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error
}

// UpsertValidationInput contains data for upsert validation
type UpsertValidationInput struct {
	BranchID  string
	AreaID    string
	AccountID string
}

type validationService struct{}

// NewAccountBranchAreaValidationService creates a new validation service
func NewAccountBranchAreaValidationService() AccountBranchAreaValidationService {
	return &validationService{}
}

// ValidateBusinessRules validates business-specific rules for account branch area entity
func (s *validationService) ValidateBusinessRules(ctx context.Context, accountBranchArea *entity.AccountBranchArea) error {
	if accountBranchArea == nil {
		return core.NewBusinessError("account branch area entity is required")
	}

	if accountBranchArea.AccountID() == "" {
		return core.NewBusinessError("account ID is required")
	}

	if accountBranchArea.BranchID() == "" {
		return core.NewBusinessError("branch ID is required")
	}

	if accountBranchArea.AreaID() == "" {
		return core.NewBusinessError("area ID is required")
	}

	return nil
}

// ValidateUpsertInput validates input for upsert operations
func (s *validationService) ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error {
	if input == nil {
		return core.NewBusinessError("upsert input is required")
	}

	// Validate account ID is not empty
	if input.AccountID == "" {
		return core.NewBusinessError("account ID is required")
	}

	// Validate branch ID is not empty
	if input.BranchID == "" {
		return core.NewBusinessError("branch ID is required")
	}

	// Validate area ID is not empty
	if input.AreaID == "" {
		return core.NewBusinessError("area ID is required")
	}

	// Validate ID formats using core validation
	if _, err := core.NewIDFromString(input.AccountID); err != nil {
		return core.NewBusinessError("invalid account ID format: %v", err)
	}

	if _, err := core.NewIDFromString(input.BranchID); err != nil {
		return core.NewBusinessError("invalid branch ID format: %v", err)
	}

	if _, err := core.NewIDFromString(input.AreaID); err != nil {
		return core.NewBusinessError("invalid area ID format: %v", err)
	}

	return nil
}
