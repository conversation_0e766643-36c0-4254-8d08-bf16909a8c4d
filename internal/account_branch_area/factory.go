package account_branch_area

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/event"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/service"
	"gitlab.viswalslab.com/backend/price-list/internal/account_branch_area/usecase"
)

type Factory struct {
	AccountBranchAreaEventListener *event.AccountBranchAreaEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase *usecase.UpsertAccountBranchAreaUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertAccountBranchAreaInput) error {
	// Direct delegation to usecase - no conversion needed since types are the same
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewAccountBranchAreaValidationService()

	// Initialize repository for upsert operations
	accountBranchAreaUpsertRepo := repository.NewAccountBranchAreaUpsertRepository(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertAccountBranchAreaUsecase(accountBranchAreaUpsertRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	// Initialize event listener
	eventListener, err := event.NewAccountBranchAreaEventListener(cfg, adapter)
	if err != nil {
		return nil, err
	}

	return &Factory{
		AccountBranchAreaEventListener: eventListener,
	}, nil
}
