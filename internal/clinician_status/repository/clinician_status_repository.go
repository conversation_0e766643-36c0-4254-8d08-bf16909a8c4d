package repository

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
)

// ClinicianStatusUpsertRepository defines the interface for clinician status upsert operations
type ClinicianStatusUpsertRepository interface {
	UpsertClinicianStatus(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error
}

// clinicianStatusRepository implements the upsert repository interface
type clinicianStatusRepository struct {
	db *sqlx.DB
}

// NewClinicianStatusUpsertRepository creates a new clinician status upsert repository
func NewClinicianStatusUpsertRepository(db *sqlx.DB) ClinicianStatusUpsertRepository {
	return &clinicianStatusRepository{db: db}
}

// Compile-time interface check
var _ ClinicianStatusUpsertRepository = (*clinicianStatusRepository)(nil)
