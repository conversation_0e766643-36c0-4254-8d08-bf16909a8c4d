package repository

import (
	"context"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
)

// ClinicianStatusNameExistsError represents a domain error when clinician status name already exists
type ClinicianStatusNameExistsError struct {
	Name string
}

func (e *ClinicianStatusNameExistsError) Error() string {
	return fmt.Sprintf("clinician status name '%s' already exists", e.Name)
}

// UpsertClinicianStatus upserts a clinician status entity into the database
func (r *clinicianStatusRepository) UpsertClinicianStatus(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error {
	query := `
		INSERT INTO public.clinician_status (uuid, clinician_status, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, NOW(), NOW())
		ON CONFLICT (uuid) DO UPDATE SET
			clinician_status = EXCLUDED.clinician_status,
			enabled = EXCLUDED.enabled,
			updated_at = NOW()
	`

	_, err := r.db.ExecContext(ctx, query,
		clinicianStatus.ID(),
		clinicianStatus.Name(),
		clinicianStatus.Enabled(),
	)

	if err != nil {
		// Check for name constraint violation
		if strings.Contains(err.Error(), "clinician_status_name_key") {
			return &ClinicianStatusNameExistsError{Name: clinicianStatus.Name()}
		}
		return err
	}

	return nil
}
