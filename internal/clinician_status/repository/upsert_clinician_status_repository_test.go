package repository_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/repository"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewClinicianStatusUpsertRepository(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewClinicianStatusUpsertRepository(db)

	assert.NotNil(t, repo)
	assert.Implements(t, (*repository.ClinicianStatusUpsertRepository)(nil), repo)
}

func TestClinicianStatusRepository_UpsertClinicianStatus(t *testing.T) {
	validID := core.NewID()
	now := time.Now()

	// Create a test clinician status entity
	clinicianStatus, err := entity.NewClinicianStatus(&entity.NewClinicianStatusInput{
		ID:        validID.Value(),
		Name:      "senior",
		Enabled:   true,
		CreatedAt: &now,
		UpdatedAt: &now,
	})
	require.NoError(t, err)

	tests := []struct {
		name            string
		clinicianStatus *entity.ClinicianStatus
		setupMock       func(sqlmock.Sqlmock)
		wantErr         bool
		wantErrMessage  string
	}{
		{
			name:            "Successful upsert",
			clinicianStatus: clinicianStatus,
			setupMock: func(mock sqlmock.Sqlmock) {
				expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "senior", true).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:            "Database error",
			clinicianStatus: clinicianStatus,
			setupMock: func(mock sqlmock.Sqlmock) {
				expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "senior", true).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
		{
			name:            "Name constraint violation",
			clinicianStatus: clinicianStatus,
			setupMock: func(mock sqlmock.Sqlmock) {
				expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
				constraintErr := errors.New(`pq: duplicate key value violates unique constraint "clinician_status_name_key"`)
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "senior", true).
					WillReturnError(constraintErr)
			},
			wantErr:        true,
			wantErrMessage: "clinician status name 'senior' already exists",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			tt.setupMock(mock)

			repo := repository.NewClinicianStatusUpsertRepository(db)
			err := repo.UpsertClinicianStatus(context.Background(), tt.clinicianStatus)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestClinicianStatusRepository_UpsertClinicianStatus_DisabledStatus(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now()

	// Create a disabled clinician status entity
	clinicianStatus, err := entity.NewClinicianStatus(&entity.NewClinicianStatusInput{
		ID:        validID.Value(),
		Name:      "retired",
		Enabled:   false,
		CreatedAt: &now,
		UpdatedAt: &now,
	})
	require.NoError(t, err)

	expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "retired", false).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewClinicianStatusUpsertRepository(db)
	err = repo.UpsertClinicianStatus(context.Background(), clinicianStatus)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestClinicianStatusRepository_UpsertClinicianStatus_UpdateExisting(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now()

	// Create a clinician status entity for update
	clinicianStatus, err := entity.NewClinicianStatus(&entity.NewClinicianStatusInput{
		ID:        validID.Value(),
		Name:      "updated-senior",
		Enabled:   false,
		CreatedAt: &now,
		UpdatedAt: &now,
	})
	require.NoError(t, err)

	// Mock expects the upsert query with ON CONFLICT DO UPDATE
	expectedQuery := `INSERT INTO public\.clinician_status.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "updated-senior", false).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewClinicianStatusUpsertRepository(db)
	err = repo.UpsertClinicianStatus(context.Background(), clinicianStatus)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}
