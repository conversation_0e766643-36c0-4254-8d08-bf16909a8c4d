package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

var validID = core.NewID()

func TestNewClinicianStatus(t *testing.T) {
	tests := []struct {
		name    string
		input   *NewClinicianStatusInput
		wantErr bool
	}{
		{
			name: "Valid input with all required fields",
			input: &NewClinicianStatusInput{
				ID:      validID.Value(),
				Name:    "senior",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with disabled status",
			input: &NewClinicianStatusInput{
				ID:      validID.Value(),
				Name:    "retired",
				Enabled: false,
			},
			wantErr: false,
		},
		{
			name: "Valid input with timestamps",
			input: &NewClinicianStatusInput{
				ID:        validID.Value(),
				Name:      "intern",
				Enabled:   true,
				CreatedAt: &time.Time{},
				UpdatedAt: &time.Time{},
			},
			wantErr: true,
		},
		{
			name: "Invalid ID format",
			input: &NewClinicianStatusInput{
				ID:      "invalid-uuid",
				Name:    "senior",
				Enabled: true,
			},
			wantErr: true,
		},
		{
			name: "Empty ID",
			input: &NewClinicianStatusInput{
				ID:      "",
				Name:    "senior",
				Enabled: true,
			},
			wantErr: true,
		},
		{
			name: "Empty name",
			input: &NewClinicianStatusInput{
				ID:      validID.Value(),
				Name:    "",
				Enabled: true,
			},
			wantErr: true,
		},
		{
			name: "Valid input with long name",
			input: &NewClinicianStatusInput{
				ID:      validID.Value(),
				Name:    "very-long-clinician-status-name-that-should-still-be-valid",
				Enabled: true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clinicianStatus, err := NewClinicianStatus(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, clinicianStatus)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, clinicianStatus)

				// Verify all getter methods work correctly
				assert.Equal(t, tt.input.ID, clinicianStatus.ID())
				assert.Equal(t, tt.input.Name, clinicianStatus.Name())
				assert.Equal(t, tt.input.Enabled, clinicianStatus.Enabled())

				assert.NotZero(t, clinicianStatus.CreatedAt())
				assert.NotZero(t, clinicianStatus.UpdatedAt())

				if tt.input.CreatedAt != nil && !tt.input.CreatedAt.IsZero() {
					assert.Equal(t, tt.input.CreatedAt.UTC(), clinicianStatus.CreatedAt().UTC())
				}
				if tt.input.UpdatedAt != nil && !tt.input.UpdatedAt.IsZero() {
					assert.Equal(t, tt.input.UpdatedAt.UTC(), clinicianStatus.UpdatedAt().UTC())
				}
			}
		})
	}
}

func TestNewClinicianStatus_PanicOnNilInput(t *testing.T) {
	assert.Panics(t, func() {
		NewClinicianStatus(nil)
	})
}

func TestClinicianStatusGetters(t *testing.T) {
	now := time.Now()
	input := &NewClinicianStatusInput{
		ID:        validID.Value(),
		Name:      "specialist",
		Enabled:   true,
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	clinicianStatus, err := NewClinicianStatus(input)
	assert.NoError(t, err)
	assert.NotNil(t, clinicianStatus)

	// Test all getter methods
	assert.Equal(t, input.ID, clinicianStatus.ID())
	assert.Equal(t, input.Name, clinicianStatus.Name())
	assert.Equal(t, input.Enabled, clinicianStatus.Enabled())
	assert.Equal(t, now.UTC(), clinicianStatus.CreatedAt().UTC())
	assert.Equal(t, now.UTC(), clinicianStatus.UpdatedAt().UTC())
}

func TestClinicianStatusWithDifferentTimestamps(t *testing.T) {
	createdTime := time.Now().Add(-time.Hour)
	updatedTime := time.Now()

	input := &NewClinicianStatusInput{
		ID:        validID.Value(),
		Name:      "consultant",
		Enabled:   true,
		CreatedAt: &createdTime,
		UpdatedAt: &updatedTime,
	}

	clinicianStatus, err := NewClinicianStatus(input)
	assert.NoError(t, err)
	assert.NotNil(t, clinicianStatus)

	// Verify timestamps are preserved correctly
	assert.Equal(t, createdTime.UTC(), clinicianStatus.CreatedAt().UTC())
	assert.Equal(t, updatedTime.UTC(), clinicianStatus.UpdatedAt().UTC())
	assert.True(t, clinicianStatus.CreatedAt().Before(clinicianStatus.UpdatedAt()))
}

func TestClinicianStatusWithNilTimestamps(t *testing.T) {
	input := &NewClinicianStatusInput{
		ID:        validID.Value(),
		Name:      "resident",
		Enabled:   true,
		CreatedAt: nil,
		UpdatedAt: nil,
	}

	beforeCreate := time.Now()
	clinicianStatus, err := NewClinicianStatus(input)
	afterCreate := time.Now()

	assert.NoError(t, err)
	assert.NotNil(t, clinicianStatus)

	// Verify default timestamps are set to current time
	assert.True(t, clinicianStatus.CreatedAt().After(beforeCreate) || clinicianStatus.CreatedAt().Equal(beforeCreate))
	assert.True(t, clinicianStatus.CreatedAt().Before(afterCreate) || clinicianStatus.CreatedAt().Equal(afterCreate))
	assert.True(t, clinicianStatus.UpdatedAt().After(beforeCreate) || clinicianStatus.UpdatedAt().Equal(beforeCreate))
	assert.True(t, clinicianStatus.UpdatedAt().Before(afterCreate) || clinicianStatus.UpdatedAt().Equal(afterCreate))
}

func TestClinicianStatusEnabledDisabledStates(t *testing.T) {
	tests := []struct {
		name    string
		enabled bool
	}{
		{
			name:    "Enabled clinician status",
			enabled: true,
		},
		{
			name:    "Disabled clinician status",
			enabled: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := &NewClinicianStatusInput{
				ID:      validID.Value(),
				Name:    "test-status",
				Enabled: tt.enabled,
			}

			clinicianStatus, err := NewClinicianStatus(input)
			assert.NoError(t, err)
			assert.NotNil(t, clinicianStatus)
			assert.Equal(t, tt.enabled, clinicianStatus.Enabled())
		})
	}
}

func TestClinicianStatusWithSpecialCharacters(t *testing.T) {
	tests := []struct {
		name        string
		statusName  string
		expectError bool
	}{
		{
			name:        "Name with hyphens",
			statusName:  "senior-consultant",
			expectError: false,
		},
		{
			name:        "Name with underscores",
			statusName:  "junior_resident",
			expectError: false,
		},
		{
			name:        "Name with spaces",
			statusName:  "chief medical officer",
			expectError: false,
		},
		{
			name:        "Name with numbers",
			statusName:  "level-1-intern",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := &NewClinicianStatusInput{
				ID:      validID.Value(),
				Name:    tt.statusName,
				Enabled: true,
			}

			clinicianStatus, err := NewClinicianStatus(input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, clinicianStatus)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, clinicianStatus)
				assert.Equal(t, tt.statusName, clinicianStatus.Name())
			}
		})
	}
}
