package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// ClinicianStatus represents a clinician status entity
type ClinicianStatus struct {
	id        core.Identifier
	name      string
	enabled   bool
	createdAt core.Timestamp
	updatedAt core.Timestamp
}

// NewClinicianStatusInput represents the input for creating a new clinician status
type NewClinicianStatusInput struct {
	ID        string
	Name      string
	Enabled   bool
	CreatedAt *time.Time
	UpdatedAt *time.Time
}

// NewClinicianStatus creates a new clinician status entity
func NewClinicianStatus(input *NewClinicianStatusInput) (*ClinicianStatus, error) {
	if input == nil {
		panic("NewClinicianStatusInput cannot be nil")
	}

	// Parse and validate ID
	id, err := core.NewIDFromString(input.ID)
	if err != nil {
		return nil, err
	}

	// Validate required fields
	if input.Name == "" {
		return nil, core.NewBusinessError("clinician status name is required")
	}

	// Use provided timestamps or default to current time using core timestamp
	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdAt, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, err
	}

	updatedAt, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, err
	}

	return &ClinicianStatus{
		id:        *id,
		name:      input.Name,
		enabled:   input.Enabled,
		createdAt: *createdAt,
		updatedAt: *updatedAt,
	}, nil
}

// ID returns the clinician status ID
func (cs *ClinicianStatus) ID() string {
	return cs.id.Value()
}

// Name returns the clinician status name
func (cs *ClinicianStatus) Name() string {
	return cs.name
}

// Enabled returns whether the clinician status is enabled
func (cs *ClinicianStatus) Enabled() bool {
	return cs.enabled
}

// CreatedAt returns the creation timestamp
func (cs *ClinicianStatus) CreatedAt() time.Time {
	return cs.createdAt.Value()
}

// UpdatedAt returns the last update timestamp
func (cs *ClinicianStatus) UpdatedAt() time.Time {
	return cs.updatedAt.Value()
}
