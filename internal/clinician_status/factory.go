package clinician_status

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/event"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/service"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/usecase"
)

type Factory struct {
	ClinicianStatusEventListener *event.ClinicianStatusEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase *usecase.UpsertClinicianStatusUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertClinicianStatusInput) (*usecase.UpsertClinicianStatusOutput, error) {
	// Direct delegation to usecase - no conversion needed since types are the same
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewClinicianStatusValidationService()

	// Initialize repository for upsert operations
	clinicianStatusUpsertRepo := repository.NewClinicianStatusUpsertRepository(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertClinicianStatusUsecase(clinicianStatusUpsertRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	// Initialize event listener
	eventListener, err := event.NewClinicianStatusEventListener(cfg, adapter)
	if err != nil {
		return nil, err
	}

	return &Factory{
		ClinicianStatusEventListener: eventListener,
	}, nil
}
