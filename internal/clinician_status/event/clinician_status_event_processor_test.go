package event_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/event"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/usecase"
	usecase_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/clinician_status/usecase"
)

func TestNewClinicianStatusUpsertEventProcessor(t *testing.T) {
	mockUsecase := new(usecase_mocks.MockUpsertClinicianStatusUsecase)
	processor := event.NewClinicianStatusUpsertEventProcessor(mockUsecase)

	assert.NotNil(t, processor)
	assert.IsType(t, &event.ClinicianStatusUpsertEventProcessor{}, processor)
}

func TestClinicianStatusUpsertEventProcessor_ProcessMessage(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name           string
		message        *event.ClinicianStatusEventMessage
		setupMock      func(*usecase_mocks.MockUpsertClinicianStatusUsecase)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Successful processing with ID",
			message: &event.ClinicianStatusEventMessage{
				Data: event.ClinicianStatusEventData{
					ID:      core.ToPtr(validID.Value()),
					Name:    "senior",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "clinician-status.upsert",
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {
				expectedInput := &usecase.UpsertClinicianStatusInput{
					ID:      core.ToPtr(validID.Value()),
					Name:    "senior",
					Enabled: true,
				}
				expectedOutput := &usecase.UpsertClinicianStatusOutput{
					ID:      validID.Value(),
					Name:    "senior",
					Enabled: true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name: "Successful processing without ID",
			message: &event.ClinicianStatusEventMessage{
				Data: event.ClinicianStatusEventData{
					Name:    "intern",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "clinician-status.upsert",
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {
				expectedInput := &usecase.UpsertClinicianStatusInput{
					ID:      nil,
					Name:    "intern",
					Enabled: true,
				}
				expectedOutput := &usecase.UpsertClinicianStatusOutput{
					ID:      "new-generated-id",
					Name:    "intern",
					Enabled: true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name:           "Nil message",
			message:        nil,
			setupMock:      func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {},
			wantErr:        true,
			wantErrMessage: "message is required",
		},
		{
			name: "Empty name",
			message: &event.ClinicianStatusEventMessage{
				Data: event.ClinicianStatusEventData{
					Name:    "",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "clinician-status.upsert",
			},
			setupMock:      func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {},
			wantErr:        true,
			wantErrMessage: "clinician status name is required in message data",
		},
		{
			name: "Invalid UUID format",
			message: &event.ClinicianStatusEventMessage{
				Data: event.ClinicianStatusEventData{
					ID:      core.ToPtr("invalid-uuid"),
					Name:    "senior",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "clinician-status.upsert",
			},
			setupMock:      func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format in message data",
		},
		{
			name: "Usecase execution error",
			message: &event.ClinicianStatusEventMessage{
				Data: event.ClinicianStatusEventData{
					Name:    "resident",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "clinician-status.upsert",
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {
				expectedInput := &usecase.UpsertClinicianStatusInput{
					ID:      nil,
					Name:    "resident",
					Enabled: true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil, errors.New("database error"))
			},
			wantErr:        true,
			wantErrMessage: "failed to process clinician status upsert from message",
		},
		{
			name: "Business error from usecase",
			message: &event.ClinicianStatusEventMessage{
				Data: event.ClinicianStatusEventData{
					Name:    "duplicate-name",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "clinician-status.upsert",
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {
				expectedInput := &usecase.UpsertClinicianStatusInput{
					ID:      nil,
					Name:    "duplicate-name",
					Enabled: true,
				}
				businessErr := core.NewBusinessError("Clinician status name 'duplicate-name' already exists")
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil, businessErr)
			},
			wantErr:        true,
			wantErrMessage: "failed to process clinician status upsert from message",
		},
		{
			name: "Successful processing with disabled status",
			message: &event.ClinicianStatusEventMessage{
				Data: event.ClinicianStatusEventData{
					ID:      core.ToPtr(validID.Value()),
					Name:    "retired",
					Enabled: false,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "clinician-status.upsert",
			},
			setupMock: func(mockUsecase *usecase_mocks.MockUpsertClinicianStatusUsecase) {
				expectedInput := &usecase.UpsertClinicianStatusInput{
					ID:      core.ToPtr(validID.Value()),
					Name:    "retired",
					Enabled: false,
				}
				expectedOutput := &usecase.UpsertClinicianStatusOutput{
					ID:      validID.Value(),
					Name:    "retired",
					Enabled: false,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUsecase := new(usecase_mocks.MockUpsertClinicianStatusUsecase)
			tt.setupMock(mockUsecase)

			processor := event.NewClinicianStatusUpsertEventProcessor(mockUsecase)
			err := processor.ProcessMessage(context.Background(), tt.message)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			mockUsecase.AssertExpectations(t)
		})
	}
}
