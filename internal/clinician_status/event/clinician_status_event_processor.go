package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertClinicianStatusUsecase defines the interface for clinician status upsert operations
type UpsertClinicianStatusUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertClinicianStatusInput) (*usecase.UpsertClinicianStatusOutput, error)
}

// ClinicianStatusUpsertEventProcessor processes clinician status upsert events
type ClinicianStatusUpsertEventProcessor struct {
	upsertUsecase UpsertClinicianStatusUsecase
}

// NewClinicianStatusUpsertEventProcessor creates a new clinician status upsert event processor
func NewClinicianStatusUpsertEventProcessor(upsertUsecase UpsertClinicianStatusUsecase) *ClinicianStatusUpsertEventProcessor {
	return &ClinicianStatusUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// ProcessMessage processes a clinician status event message
func (p *ClinicianStatusUpsertEventProcessor) ProcessMessage(ctx context.Context, message *ClinicianStatusEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "ClinicianStatusUpsertEventProcessor"))
	logger.Debug("handling clinician status event message", vlog.F("event_type", message.EventType))

	// Validate message data
	if message.Data.Name == "" {
		logger.Error("validation failed", vlog.F("error", "clinician status name is required"))
		return core.NewBusinessError("clinician status name is required in message data")
	}

	// Validate ID format if provided
	if message.Data.ID != nil && *message.Data.ID != "" {
		if _, err := core.NewIDFromString(*message.Data.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.Data.ID))
			return core.NewBusinessError("invalid UUID format in message data: %v", err)
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertClinicianStatusInput{
		Name:    message.Data.Name,
		Enabled: message.Data.Enabled,
	}

	if message.Data.ID != nil {
		upsertInput.ID = message.Data.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		logger.Error("failed to process clinician status upsert from message", vlog.F("error", err))
		return fmt.Errorf("failed to process clinician status upsert from message: %w", err)
	}

	logger.Info("successfully processed clinician status upsert",
		vlog.F("name", message.Data.Name))
	return nil
}
