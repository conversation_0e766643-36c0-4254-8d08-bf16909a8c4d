package event

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// SQSClinicianStatusEventSubscriber implements ClinicianStatusEventSubscriber using AWS SQS
type SQSClinicianStatusEventSubscriber struct {
	sqsClient *awspkg.SQSClient
	cfg       config.Configuration
	processor interface {
		// ProcessMessage processes a clinician status event message
		ProcessMessage(ctx context.Context, message *ClinicianStatusEventMessage) error
	}
	logger    vlog.Logger
	isRunning bool
	mutex     sync.RWMutex
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewSQSClinicianStatusEventSubscriber creates a new SQS-based clinician status event subscriber
func NewSQSClinicianStatusEventSubscriber(cfg config.Configuration, queueURL string) (*SQSClinicianStatusEventSubscriber, error) {
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        queueURL,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS client: %w", err)
	}

	return &SQSClinicianStatusEventSubscriber{
		sqsClient: sqsClient,
		cfg:       cfg,
		logger:    vlog.New().With(vlog.F("subscriber", "ClinicianStatusEventSubscriber")),
		stopChan:  make(chan struct{}),
	}, nil
}

// Subscribe configures the event processor for the subscriber
func (s *SQSClinicianStatusEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *ClinicianStatusEventMessage) error
}) error {
	s.processor = processor
	return nil
}

// Start starts the subscription service
func (s *SQSClinicianStatusEventSubscriber) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("clinician status event subscriber is already running")
	}

	if s.processor == nil {
		return fmt.Errorf("event processor is required")
	}

	s.logger.Info("starting clinician status event subscriber")

	s.isRunning = true
	s.wg.Add(1)

	go s.pollMessages(ctx)

	return nil
}

// Stop stops the subscription service
func (s *SQSClinicianStatusEventSubscriber) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("stopping clinician status event subscriber")

	close(s.stopChan)
	s.wg.Wait()

	s.isRunning = false

	if err := s.sqsClient.Close(); err != nil {
		s.logger.Error("failed to close clinician status event subscriber client", vlog.F("error", err))
		return fmt.Errorf("failed to close clinician status event subscriber client: %w", err)
	}

	s.logger.Info("clinician status event subscriber stopped successfully")
	return nil
}

// IsRunning returns true if the subscriber is running
func (s *SQSClinicianStatusEventSubscriber) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// pollMessages continuously polls for messages from SQS
func (s *SQSClinicianStatusEventSubscriber) pollMessages(ctx context.Context) {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("context cancelled, stopping clinician status event polling")
			return
		case <-s.stopChan:
			s.logger.Info("stop signal received, stopping clinician status event polling")
			return
		case <-ticker.C:
			s.processMessages(ctx)
		}
	}
}

// processMessages processes incoming SQS messages
func (s *SQSClinicianStatusEventSubscriber) processMessages(ctx context.Context) {
	result, err := s.sqsClient.ReceiveMessages(ctx, 10, 20)
	if err != nil {
		s.logger.Error("error receiving clinician status event messages", vlog.F("error", err))
		return
	}

	if len(result.Messages) == 0 {
		return
	}

	for _, msg := range result.Messages {
		processErr := s.processMessage(ctx, &msg)
		if processErr != nil {
			s.logger.Error("error processing clinician status event message", vlog.F("error", processErr))
		}

		// Always delete the message from SQS after processing attempt
		// This prevents infinite reprocessing of failed messages
		if msg.ReceiptHandle != nil {
			if _, deleteErr := s.sqsClient.DeleteMessage(ctx, *msg.ReceiptHandle); deleteErr != nil {
				s.logger.Error("error deleting clinician status event message", vlog.F("error", deleteErr))
			} else {
				if processErr != nil {
					s.logger.Info("deleted failed message from queue to prevent reprocessing",
						vlog.F("processing_error", processErr.Error()))
				}
			}
		}
	}
}

// processMessage processes a single SQS message
func (s *SQSClinicianStatusEventSubscriber) processMessage(ctx context.Context, msg *types.Message) error {
	if msg.Body == nil {
		return fmt.Errorf("message body is nil")
	}

	var eventMessage ClinicianStatusEventMessage
	if err := json.Unmarshal([]byte(*msg.Body), &eventMessage); err != nil {
		return fmt.Errorf("failed to unmarshal message: %w", err)
	}

	if err := s.processor.ProcessMessage(ctx, &eventMessage); err != nil {
		return fmt.Errorf("failed to handle event message: %w", err)
	}

	return nil
}
