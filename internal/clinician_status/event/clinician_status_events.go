package event

import (
	"time"
)

// ClinicianStatusEventData represents the data structure for clinician status events
type ClinicianStatusEventData struct {
	ID      *string `json:"id,omitempty"`
	Name    string  `json:"name"`
	Enabled bool    `json:"enabled"`
}

// ClinicianStatusEventMessage represents the event message structure for clinician status operations
type ClinicianStatusEventMessage struct {
	Data      ClinicianStatusEventData `json:"data"`
	Timestamp time.Time                `json:"timestamp"`
	Source    string                   `json:"source"`
	EventType string                   `json:"event_type"`
}
