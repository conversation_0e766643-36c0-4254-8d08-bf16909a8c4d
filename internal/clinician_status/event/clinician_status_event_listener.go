package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// ClinicianStatusEventSubscriber defines the interface for subscribing to clinician status events
type ClinicianStatusEventSubscriber interface {
	// Subscribe starts listening to clinician status events
	Subscribe(ctx context.Context, processor interface {
		ProcessMessage(ctx context.Context, message *ClinicianStatusEventMessage) error
	}) error

	// Start starts the subscription service
	Start(ctx context.Context) error

	// Stop stops the subscription service
	Stop() error

	// IsRunning returns true if the subscriber is running
	IsRunning() bool
}

// ClinicianStatusEventListener manages the clinician status event subscription service
type ClinicianStatusEventListener struct {
	subscriber ClinicianStatusEventSubscriber
	cfg        config.Configuration
}

// NewClinicianStatusEventListener creates a new clinician status event listener instance
func NewClinicianStatusEventListener(cfg config.Configuration, upsertUsecase UpsertClinicianStatusUsecase) (*ClinicianStatusEventListener, error) {
	// Validate that clinician status queue URL is configured
	if cfg.AWS.SQSQueueURLClinicianStatus == "" {
		return nil, fmt.Errorf("AWS_SQS_QUEUE_URL_CLINICIAN_STATUS is required for clinician status event operations")
	}

	// Initialize SQS event subscriber with clinician status queue URL
	subscriber, err := NewSQSClinicianStatusEventSubscriber(cfg, cfg.AWS.SQSQueueURLClinicianStatus)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS event subscriber: %w", err)
	}

	// Create event handler
	eventProcessor := NewClinicianStatusUpsertEventProcessor(upsertUsecase)

	// Set up subscriber with handler
	if err := subscriber.Subscribe(context.Background(), eventProcessor); err != nil {
		return nil, fmt.Errorf("failed to set up event subscription: %w", err)
	}

	return &ClinicianStatusEventListener{
		subscriber: subscriber,
		cfg:        cfg,
	}, nil
}

// Start starts the clinician status event listener
func (s *ClinicianStatusEventListener) Start(ctx context.Context) error {
	logger := vlog.New().With(vlog.F("service", "ClinicianStatusEventListener"))
	logger.Info("starting clinician status event listener")

	if err := s.subscriber.Start(ctx); err != nil {
		logger.Error("failed to start event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to start event subscriber: %w", err)
	}

	logger.Info("clinician status event listener started successfully")
	return nil
}

// Stop stops the clinician status event listener
func (s *ClinicianStatusEventListener) Stop() error {
	logger := vlog.New().With(vlog.F("service", "ClinicianStatusEventListener"))
	logger.Info("stopping clinician status event listener")

	if err := s.subscriber.Stop(); err != nil {
		logger.Error("failed to stop event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to stop event subscriber: %w", err)
	}

	logger.Info("clinician status event listener stopped successfully")
	return nil
}

// IsRunning returns true if the service is currently running
func (s *ClinicianStatusEventListener) IsRunning() bool {
	return s.subscriber.IsRunning()
}
