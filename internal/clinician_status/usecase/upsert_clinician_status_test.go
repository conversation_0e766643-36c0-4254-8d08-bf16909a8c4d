package usecase_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/service"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/usecase"
	repository_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/clinician_status/repository"
)

// MockClinicianStatusValidationService is a mock implementation of ClinicianStatusValidationService
type MockClinicianStatusValidationService struct {
	mock.Mock
}

func (m *MockClinicianStatusValidationService) ValidateUpsertInput(ctx context.Context, input *service.UpsertValidationInput) error {
	args := m.Called(ctx, input)
	return args.Error(0)
}

func (m *MockClinicianStatusValidationService) ValidateBusinessRules(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error {
	args := m.Called(ctx, clinicianStatus)
	return args.Error(0)
}

func TestNewUpsertClinicianStatusUsecase(t *testing.T) {
	mockRepo := new(repository_mocks.MockClinicianStatusUpsertRepository)
	mockValidationService := new(MockClinicianStatusValidationService)

	uc := usecase.NewUpsertClinicianStatusUsecase(mockRepo, mockValidationService)

	assert.NotNil(t, uc)
}

func TestUpsertClinicianStatusUsecase_Execute(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name           string
		input          *usecase.UpsertClinicianStatusInput
		setupMocks     func(*repository_mocks.MockClinicianStatusUpsertRepository, *MockClinicianStatusValidationService)
		wantErr        bool
		wantErrMessage string
		expectedOutput *usecase.UpsertClinicianStatusOutput
	}{
		{
			name: "Successful upsert with ID",
			input: &usecase.UpsertClinicianStatusInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "senior",
				Enabled: true,
			},
			setupMocks: func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(nil)
				mockRepo.On("UpsertClinicianStatus", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertClinicianStatusOutput{
				ID:      validID.Value(),
				Name:    "senior",
				Enabled: true,
			},
		},
		{
			name: "Successful upsert without ID",
			input: &usecase.UpsertClinicianStatusInput{
				Name:    "intern",
				Enabled: true,
			},
			setupMocks: func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(nil)
				mockRepo.On("UpsertClinicianStatus", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(nil)
			},
			wantErr: false,
			expectedOutput: &usecase.UpsertClinicianStatusOutput{
				Name:    "intern",
				Enabled: true,
			},
		},
		{
			name:           "Nil input",
			input:          nil,
			setupMocks:     func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {},
			wantErr:        true,
			wantErrMessage: "input is required",
		},
		{
			name: "Empty name",
			input: &usecase.UpsertClinicianStatusInput{
				Name:    "",
				Enabled: true,
			},
			setupMocks:     func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {},
			wantErr:        true,
			wantErrMessage: "name is required",
		},
		{
			name: "Invalid UUID format",
			input: &usecase.UpsertClinicianStatusInput{
				ID:      core.ToPtr("invalid-uuid"),
				Name:    "senior",
				Enabled: true,
			},
			setupMocks:     func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format",
		},
		{
			name: "Service validation error",
			input: &usecase.UpsertClinicianStatusInput{
				Name:    "test",
				Enabled: true,
			},
			setupMocks: func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(errors.New("service validation failed"))
			},
			wantErr:        true,
			wantErrMessage: "service validation failed",
		},
		{
			name: "Business rules validation error",
			input: &usecase.UpsertClinicianStatusInput{
				Name:    "test",
				Enabled: true,
			},
			setupMocks: func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(errors.New("business rules validation failed"))
			},
			wantErr:        true,
			wantErrMessage: "business rules validation failed",
		},
		{
			name: "Repository error",
			input: &usecase.UpsertClinicianStatusInput{
				Name:    "test",
				Enabled: true,
			},
			setupMocks: func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(nil)
				mockRepo.On("UpsertClinicianStatus", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(errors.New("database error"))
			},
			wantErr:        true,
			wantErrMessage: "database error occurred while upserting clinician status",
		},
		{
			name: "Name constraint violation",
			input: &usecase.UpsertClinicianStatusInput{
				Name:    "duplicate-name",
				Enabled: true,
			},
			setupMocks: func(mockRepo *repository_mocks.MockClinicianStatusUpsertRepository, mockValidation *MockClinicianStatusValidationService) {
				mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(nil)
				nameExistsErr := &repository.ClinicianStatusNameExistsError{Name: "duplicate-name"}
				mockRepo.On("UpsertClinicianStatus", mock.Anything, mock.AnythingOfType("*entity.ClinicianStatus")).Return(nameExistsErr)
			},
			wantErr:        true,
			wantErrMessage: "Clinician status name 'duplicate-name' already exists",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := new(repository_mocks.MockClinicianStatusUpsertRepository)
			mockValidationService := new(MockClinicianStatusValidationService)

			tt.setupMocks(mockRepo, mockValidationService)

			uc := usecase.NewUpsertClinicianStatusUsecase(mockRepo, mockValidationService)
			output, err := uc.Execute(context.Background(), tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
				assert.Nil(t, output)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, output)
				if tt.expectedOutput != nil {
					assert.Equal(t, tt.expectedOutput.Name, output.Name)
					assert.Equal(t, tt.expectedOutput.Enabled, output.Enabled)
					if tt.expectedOutput.ID != "" {
						assert.Equal(t, tt.expectedOutput.ID, output.ID)
					} else {
						// For cases without ID, just verify it's a valid UUID
						_, err := core.NewIDFromString(output.ID)
						assert.NoError(t, err)
					}
				}
			}

			mockRepo.AssertExpectations(t)
			mockValidationService.AssertExpectations(t)
		})
	}
}
