package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
)

func TestNewClinicianStatusValidationService(t *testing.T) {
	service := NewClinicianStatusValidationService()
	assert.NotNil(t, service)
	assert.Implements(t, (*ClinicianStatusValidationService)(nil), service)
}

func TestValidateBusinessRules(t *testing.T) {
	service := NewClinicianStatusValidationService()
	ctx := context.Background()

	validID := core.NewID()

	tests := []struct {
		name               string
		setupEntity        func() *entity.ClinicianStatus
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid entity",
			setupEntity: func() *entity.ClinicianStatus {
				input := &entity.NewClinicianStatusInput{
					ID:      validID.Value(),
					Name:    "senior",
					Enabled: true,
				}
				cs, _ := entity.NewClinicianStatus(input)
				return cs
			},
			wantErr: false,
		},
		{
			name: "Valid disabled entity",
			setupEntity: func() *entity.ClinicianStatus {
				input := &entity.NewClinicianStatusInput{
					ID:      validID.Value(),
					Name:    "retired",
					Enabled: false,
				}
				cs, _ := entity.NewClinicianStatus(input)
				return cs
			},
			wantErr: false,
		},
		{
			name: "Nil entity",
			setupEntity: func() *entity.ClinicianStatus {
				return nil
			},
			wantErr:            true,
			expectedErrMessage: "clinician status entity is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := tt.setupEntity()
			err := service.ValidateBusinessRules(ctx, entity)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpsertInput(t *testing.T) {
	service := NewClinicianStatusValidationService()
	ctx := context.Background()

	validID := core.NewID()

	tests := []struct {
		name               string
		input              *UpsertValidationInput
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid input with ID",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "senior",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input without ID",
			input: &UpsertValidationInput{
				Name:    "intern",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid disabled status",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "retired",
				Enabled: false,
			},
			wantErr: false,
		},
		{
			name:               "Nil input",
			input:              nil,
			wantErr:            true,
			expectedErrMessage: "validation input is required",
		},
		{
			name: "Empty name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "",
				Enabled: true,
			},
			wantErr:            true,
			expectedErrMessage: "clinician status name is required",
		},
		{
			name: "Invalid ID format",
			input: &UpsertValidationInput{
				ID:      core.ToPtr("invalid-uuid"),
				Name:    "senior",
				Enabled: true,
			},
			wantErr:            true,
			expectedErrMessage: "invalid UUID format",
		},
		{
			name: "Empty ID string",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(""),
				Name:    "senior",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with special characters in name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "senior-consultant",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with spaces in name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "chief medical officer",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with numbers in name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "level-1-resident",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid long name",
			input: &UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Name:    "very-long-clinician-status-name-that-should-still-be-valid-for-testing-purposes",
				Enabled: true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpsertInput_EdgeCases(t *testing.T) {
	service := NewClinicianStatusValidationService()
	ctx := context.Background()

	tests := []struct {
		name               string
		input              *UpsertValidationInput
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Whitespace only name - allowed by current validation",
			input: &UpsertValidationInput{
				Name:    "   ",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Tab and newline in name - allowed by current validation",
			input: &UpsertValidationInput{
				Name:    "\t\n",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Single space name - allowed by current validation",
			input: &UpsertValidationInput{
				Name:    " ",
				Enabled: true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateBusinessRules_EntityValidation(t *testing.T) {
	service := NewClinicianStatusValidationService()
	ctx := context.Background()

	validID := core.NewID()

	// Test with valid entity
	input := &entity.NewClinicianStatusInput{
		ID:      validID.Value(),
		Name:    "consultant",
		Enabled: true,
	}
	clinicianStatus, err := entity.NewClinicianStatus(input)
	assert.NoError(t, err)

	err = service.ValidateBusinessRules(ctx, clinicianStatus)
	assert.NoError(t, err)

	// Test with disabled entity
	disabledInput := &entity.NewClinicianStatusInput{
		ID:      validID.Value(),
		Name:    "retired",
		Enabled: false,
	}
	disabledStatus, err := entity.NewClinicianStatus(disabledInput)
	assert.NoError(t, err)

	err = service.ValidateBusinessRules(ctx, disabledStatus)
	assert.NoError(t, err)
}
