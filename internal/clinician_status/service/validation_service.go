package service

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/clinician_status/entity"
)

// UpsertValidationInput represents input for upsert validation
type UpsertValidationInput struct {
	ID      *string
	Name    string
	Enabled bool
}

// ClinicianStatusValidationService defines the interface for clinician status validation
type ClinicianStatusValidationService interface {
	ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error
	ValidateBusinessRules(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error
}

// clinicianStatusValidationService implements the validation service
type clinicianStatusValidationService struct{}

// NewClinicianStatusValidationService creates a new validation service
func NewClinicianStatusValidationService() ClinicianStatusValidationService {
	return &clinicianStatusValidationService{}
}

// ValidateUpsertInput validates the upsert input
func (s *clinicianStatusValidationService) ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error {
	if input == nil {
		return core.NewBusinessError("validation input is required")
	}

	// Validate required fields
	if input.Name == "" {
		return core.NewBusinessError("clinician status name is required")
	}

	// Validate UUID format if provided
	if input.ID != nil && *input.ID != "" {
		if _, err := core.NewIDFromString(*input.ID); err != nil {
			return core.NewBusinessError("invalid UUID format: %v", err)
		}
	}

	return nil
}

// ValidateBusinessRules validates business rules for the clinician status entity
func (s *clinicianStatusValidationService) ValidateBusinessRules(ctx context.Context, clinicianStatus *entity.ClinicianStatus) error {
	if clinicianStatus == nil {
		return core.NewBusinessError("clinician status entity is required")
	}

	// Validate entity fields
	if clinicianStatus.ID() == "" {
		return core.NewBusinessError("clinician status ID is required")
	}
	if clinicianStatus.Name() == "" {
		return core.NewBusinessError("clinician status name is required")
	}

	return nil
}
