package branchfieldnature

import (
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func NewBranchFieldNatureHandlerFactory(repo repository.BranchFieldNatureRepository, logger vlog.Logger) *transport.BranchFieldNatureHandler {
	uc := usecase.NewBranchFieldNatureUsecase(repo)
	return transport.NewBranchFieldNatureHandler(uc, logger)
}
