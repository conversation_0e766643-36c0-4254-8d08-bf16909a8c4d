package repository

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type BranchFieldNatureRepository interface {
	Create(ctx context.Context, bfn *entity.BranchFieldNature) error
}

type branchFieldNatureRepository struct {
	db     *sqlx.DB
	logger vlog.Logger
}

func NewBranchFieldNatureRepository(db *sqlx.DB, logger vlog.Logger) *branchFieldNatureRepository {
	return &branchFieldNatureRepository{db: db, logger: logger}
}
