package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type testBranchFieldNature struct {
	ID              string
	AccountUUID     string
	BranchID        string
	FieldID         string
	NatureID        string
	ClinicianStatus string
	Prices          int
	Duration        int
	AppointmentType *string
	CreatedDate     time.Time
	UpdatedDate     time.Time
}

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewBranchFieldNatureRepository(t *testing.T) {
	db, _ := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewBranchFieldNatureRepository(db, logger)
	assert.NotNil(t, repo)
	assert.Implements(t, (*BranchFieldNatureRepository)(nil), repo)
}

func TestBranchFieldNatureRepository_Create(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewBranchFieldNatureRepository(db, logger)
	ctx := context.Background()

	id := core.NewID()
	accountUUID := core.NewID()
	branchID := core.NewID()
	fieldID := core.NewID()
	natureID := core.NewID()
	createdAt, _ := core.NewTimestampFromTime(time.Now())
	updatedAt, _ := core.NewTimestampFromTime(time.Now())
	apptType := "Consultation"
	bfn := &entity.BranchFieldNature{
		ID:              *id,
		AccountUUID:     *accountUUID,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
		AppointmentType: &apptType,
		CreatedDate:     *createdAt,
		UpdatedDate:     *updatedAt,
	}

	mock.ExpectQuery(`INSERT INTO branch_price_configuration`).
		WithArgs(
			bfn.AccountUUID.Value(),
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_date", "updated_date"}).
			AddRow(id.Value(), createdAt.String(), updatedAt.String()))

	err := repo.Create(ctx, bfn)
	assert.NoError(t, err)
	assert.Equal(t, *id, bfn.ID)
}

func TestBranchFieldNatureRepository_Create_ErrorOnInsert(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewBranchFieldNatureRepository(db, logger)
	ctx := context.Background()

	id := core.NewID()
	accountUUID := core.NewID()
	branchID := core.NewID()
	fieldID := core.NewID()
	natureID := core.NewID()
	apptType := "Consultation"
	bfn := &entity.BranchFieldNature{
		ID:              *id,
		AccountUUID:     *accountUUID,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
		AppointmentType: &apptType,
	}

	mock.ExpectQuery(`INSERT INTO branch_price_configuration`).
		WithArgs(
			bfn.AccountUUID.Value(),
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		).
		WillReturnError(assert.AnError)

	err := repo.Create(ctx, bfn)
	assert.Error(t, err)
}

func TestBranchFieldNatureRepository_Create_InvalidID(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewBranchFieldNatureRepository(db, logger)
	ctx := context.Background()

	id := core.NewID()
	accountUUID := core.NewID()
	branchID := core.NewID()
	fieldID := core.NewID()
	natureID := core.NewID()
	apptType := "Consultation"
	bfn := &entity.BranchFieldNature{
		ID:              *id,
		AccountUUID:     *accountUUID,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
		AppointmentType: &apptType,
	}

	// Simulate DB returning an invalid UUID string
	mock.ExpectQuery(`INSERT INTO branch_price_configuration`).
		WithArgs(
			bfn.AccountUUID.Value(),
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_date", "updated_date"}).
			AddRow("not-a-uuid", "2023-01-01T00:00:00Z", "2023-01-01T00:00:00Z"))

	err := repo.Create(ctx, bfn)
	assert.Error(t, err)
}

func TestBranchFieldNatureRepository_Create_TreatmentPayload(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewBranchFieldNatureRepository(db, logger)
	ctx := context.Background()

	id := core.NewID()
	accountUUID := core.NewID()
	branchID := core.NewID()
	fieldID := core.NewID()
	natureID := core.NewID()
	createdAt, _ := core.NewTimestampFromTime(time.Now())
	updatedAt, _ := core.NewTimestampFromTime(time.Now())
	apptType := "in_clinic"
	bfn := &entity.BranchFieldNature{
		ID:              *id,
		AccountUUID:     *accountUUID,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: "associate",
		Prices:          35,
		Duration:        0, // Should be ignored for treatment
		AppointmentType: &apptType,
		PriceListType:   "treatment",
		PriceType:       "from",
		CreatedDate:     *createdAt,
		UpdatedDate:     *updatedAt,
	}

	mock.ExpectQuery(`INSERT INTO branch_price_configuration`).
		WithArgs(
			bfn.AccountUUID.Value(),
			bfn.BranchID.Value(),
			bfn.FieldID.Value(),
			bfn.NatureID.Value(),
			bfn.ClinicianStatus,
			bfn.Prices,
			bfn.Duration,
			bfn.AppointmentType,
			bfn.PriceListType,
			bfn.PriceType,
		).
		WillReturnRows(sqlmock.NewRows([]string{"id", "created_date", "updated_date"}).
			AddRow(id.Value(), createdAt.String(), updatedAt.String()))

	err := repo.Create(ctx, bfn)
	assert.NoError(t, err)
	assert.Equal(t, *id, bfn.ID)
}
