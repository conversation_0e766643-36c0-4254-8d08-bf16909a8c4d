package repository

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
)

func (r *branchFieldNatureRepository) Create(ctx context.Context, bfn *entity.BranchFieldNature) error {
	query := `INSERT INTO branch_price_configuration (
    account_uuid, branch_id, field_id, nature_id, clinician_status, prices, duration, appointment_type, price_list_type, price_type, created_date, updated_date
	) VALUES (
		$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, now(), now()
	) RETURNING id, created_date, updated_date`

	row := r.db.QueryRowxContext(ctx, query,
		bfn.AccountUUID.Value(),
		bfn.BranchID.Value(),
		bfn.FieldID.Value(),
		bfn.NatureID.Value(),
		bfn.ClinicianStatus,
		bfn.Prices,
		bfn.Duration,
		bfn.AppointmentType,
		bfn.PriceListType,
		bfn.PriceType,
	)

	var idStr string
	var createdDate, updatedDate interface{}
	if err := row.Scan(&idStr, &createdDate, &updatedDate); err != nil {
		return err
	}
	id, err := core.NewIDFromString(idStr)
	if err != nil {
		return err
	}
	bfn.ID = *id
	return nil
}
