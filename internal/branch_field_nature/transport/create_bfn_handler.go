package transport

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
)

// CreateBranchFieldNature handles the creation of a branch field nature
func (h *BranchFieldNatureHandler) Create(c echo.Context) error {
	var req BranchFieldNatureRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid request", "data": nil})
	}

	// Extract account_uuid from context (set by auth middleware)
	accountUUIDVal := c.Get("account_uuid")
	if accountUUIDVal == nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{"status": false, "message": "Missing account context", "data": nil})
	}
	accountUUIDStr, ok := accountUUIDVal.(string)
	if !ok || accountUUIDStr == "" {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{"status": false, "message": "Invalid user context", "data": nil})
	}
	accountUUID, err := core.NewIDFromString(accountUUIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid user UUID", "data": nil})
	}

	// Validate price_list_type
	validPriceListTypes := map[string]bool{"appointment": true, "treatment": true}
	if !validPriceListTypes[req.PriceListType] {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid price_list_type", "data": nil})
	}

	// Validate duration only for appointment type
	if req.PriceListType == "appointment" {
		if req.Duration >= 120 || req.Duration <= 0 || req.Duration%15 != 0 {
			return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Duration must be >0, <120, and a multiple of 15", "data": nil})
		}
	}

	// Validate price_type
	validPriceTypes := map[string]bool{"fixed": true, "from": true}
	if !validPriceTypes[req.PriceType] {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid price_type", "data": nil})
	}

	// Validate appointment_type for treatment
	if req.PriceListType == "treatment" && (req.AppointmentType == nil || *req.AppointmentType != "in_clinic") {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid appointment type. Treatment price list supports only In-Clinic appointments.", "data": nil})
	}

	branchID, err := core.NewIDFromString(req.BranchID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid branch_id", "data": nil})
	}
	fieldID, err := core.NewIDFromString(req.FieldID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid field_id", "data": nil})
	}
	natureID, err := core.NewIDFromString(req.NatureID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{"status": false, "message": "Invalid nature_id", "data": nil})
	}

	bfn := &entity.BranchFieldNature{
		AccountUUID:     *accountUUID,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: req.ClinicianStatus,
		Prices:          req.Prices,
		Duration:        req.Duration,
		AppointmentType: req.AppointmentType,
		PriceListType:   req.PriceListType,
		PriceType:       req.PriceType,
	}

	if err := h.Usecase.Create(c.Request().Context(), bfn); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{"status": false, "message": "Failed to create: " + err.Error(), "data": nil})
	}
	response := map[string]interface{}{
		"id":               bfn.ID.Value(),
		"account_uuid":     bfn.AccountUUID.Value(),
		"branch_id":        bfn.BranchID.Value(),
		"field_id":         bfn.FieldID.Value(),
		"nature_id":        bfn.NatureID.Value(),
		"clinician_status": bfn.ClinicianStatus,
		"prices":           bfn.Prices,
		"duration":         bfn.Duration,
		"appointment_type": bfn.AppointmentType,
		"price_list_type":  bfn.PriceListType,
		"price_type":       bfn.PriceType,
		"created_date":     bfn.CreatedDate.String(),
		"updated_date":     bfn.UpdatedDate.String(),
	}
	return c.JSON(http.StatusCreated, map[string]interface{}{"status": true, "message": "Created successfully", "data": response})
}
