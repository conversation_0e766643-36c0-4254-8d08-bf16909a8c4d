package transport

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
)

func TestToBranchFieldNatureResponse(t *testing.T) {
	id, err := core.NewIDFromString("84ce4d70-a442-412b-bf27-06f4544a8661")
	if err != nil || id == nil {
		t.Fatalf("failed to create id: %v", err)
	}
	accountUUID, err := core.NewIDFromString("e7b8c1a2-4b2a-4e2a-9c1a-2b2a4e2a9c1a")
	if err != nil || accountUUID == nil {
		t.Fatalf("failed to create accountUUID: %v", err)
	}
	branchID, err := core.NewIDFromString("b1b2c3d4-e5f6-7890-abcd-ef1234567890")
	if err != nil || branchID == nil {
		t.Fatalf("failed to create branchID: %v", err)
	}
	fieldID, err := core.NewIDFromString("f1e2d3c4-b5a6-7890-abcd-ef1234567890")
	if err != nil || fieldID == nil {
		t.Fatalf("failed to create fieldID: %v", err)
	}
	natureID, err := core.NewIDFromString("a1e2d3c4-b5a6-7890-abcd-ef1234567890")
	if err != nil || natureID == nil {
		t.Fatalf("failed to create natureID: %v", err)
	}
	apptType := "in_clinic"
	created, err := core.NewTimestampFromTime(core.NewTimestamp().Value())
	if err != nil || created == nil {
		t.Fatalf("failed to create created timestamp: %v", err)
	}
	updated, err := core.NewTimestampFromTime(core.NewTimestamp().Value())
	if err != nil || updated == nil {
		t.Fatalf("failed to create updated timestamp: %v", err)
	}
	bfn := entity.BranchFieldNature{
		ID:              *id,
		AccountUUID:        *accountUUID,
		BranchID:        *branchID,
		FieldID:         *fieldID,
		NatureID:        *natureID,
		ClinicianStatus: "master",
		Prices:          1000,
		Duration:        30,
		AppointmentType: &apptType,
		PriceListType:   "treatment",
		PriceType:       "fixed",
		CreatedDate:     *created,
		UpdatedDate:     *updated,
	}
	resp := ToBranchFieldNatureResponse(bfn)
	assert.Equal(t, "84ce4d70-a442-412b-bf27-06f4544a8661", resp.ID)
	assert.Equal(t, "e7b8c1a2-4b2a-4e2a-9c1a-2b2a4e2a9c1a", resp.AccountUUID)
	assert.Equal(t, "b1b2c3d4-e5f6-7890-abcd-ef1234567890", resp.BranchID)
	assert.Equal(t, "f1e2d3c4-b5a6-7890-abcd-ef1234567890", resp.FieldID)
	assert.Equal(t, "a1e2d3c4-b5a6-7890-abcd-ef1234567890", resp.NatureID)
	assert.Equal(t, "master", resp.ClinicianStatus)
	assert.Equal(t, 1000, resp.Prices)
	assert.Equal(t, 30, resp.Duration)
	assert.NotNil(t, resp.AppointmentType)
	assert.Equal(t, apptType, *resp.AppointmentType)
	assert.Equal(t, created.String(), resp.CreatedDate)
	assert.Equal(t, updated.String(), resp.UpdatedDate)
	assert.Equal(t, "treatment", resp.PriceListType)
	assert.Equal(t, "fixed", resp.PriceType)
}