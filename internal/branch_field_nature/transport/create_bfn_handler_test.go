package transport

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// Inline mock repository for tests

type mockBranchFieldNatureRepository struct {
	CreateFunc func(ctx context.Context, bfn *entity.BranchFieldNature) error
}

func (m *mockBranchFieldNatureRepository) Create(ctx context.Context, bfn *entity.BranchFieldNature) error {
	if m.CreateFunc != nil {
		return m.CreateFunc(ctx, bfn)
	}
	return nil
}

func TestCreateBranchFieldNatureHandler_Success(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	accountUUID := core.NewID().Value()
	branchID := core.NewID().Value()
	fieldID := core.NewID().Value()
	natureID := core.NewID().Value()
	apptType := "in_clinic"
	request := BranchFieldNatureRequest{
		BranchID:        branchID,
		FieldID:         fieldID,
		NatureID:        natureID,
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
		AppointmentType: &apptType,
		PriceListType:   "appointment",
		PriceType:       "fixed",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	c.Set("account_uuid", accountUUID)

	mockRepo.CreateFunc = func(ctx context.Context, bfn *entity.BranchFieldNature) error {
		return nil
	}

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusCreated, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.True(t, response["status"].(bool))
	assert.Equal(t, "Created successfully", response["message"])
	assert.NotNil(t, response["data"])
	assert.Equal(t, "appointment", response["data"].(map[string]interface{})["price_list_type"])
	assert.Equal(t, "fixed", response["data"].(map[string]interface{})["price_type"])
}

func TestCreateBranchFieldNatureHandler_InvalidRequest(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	c.Set("account_uuid", core.NewID().Value())

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
}

func TestCreateBranchFieldNatureHandler_MissingAccountUUIDUUID(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	request := BranchFieldNatureRequest{
		BranchID:        core.NewID().Value(),
		FieldID:         core.NewID().Value(),
		NatureID:        core.NewID().Value(),
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	// No account_uuid set

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, resp.Code)
}

func TestCreateBranchFieldNatureHandler_InvalidAccountUUID(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	request := BranchFieldNatureRequest{
		BranchID:        core.NewID().Value(),
		FieldID:         core.NewID().Value(),
		NatureID:        core.NewID().Value(),
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	c.Set("account_uuid", 12345) // Invalid type

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, resp.Code)
}

func TestCreateBranchFieldNatureHandler_InvalidDuration(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	request := BranchFieldNatureRequest{
		BranchID:        core.NewID().Value(),
		FieldID:         core.NewID().Value(),
		NatureID:        core.NewID().Value(),
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        17, // Not a multiple of 15
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	c.Set("account_uuid", core.NewID().Value())

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
}

func TestCreateBranchFieldNatureHandler_InvalidBranchID(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	request := BranchFieldNatureRequest{
		BranchID:        "invalid-uuid",
		FieldID:         core.NewID().Value(),
		NatureID:        core.NewID().Value(),
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	c.Set("account_uuid", core.NewID().Value())

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
}

func TestCreateBranchFieldNatureHandler_UsecaseError(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	accountUUID := core.NewID().Value()
	branchID := core.NewID().Value()
	fieldID := core.NewID().Value()
	natureID := core.NewID().Value()
	apptType := "in_clinic"
	request := BranchFieldNatureRequest{
		BranchID:        branchID,
		FieldID:         fieldID,
		NatureID:        natureID,
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        30,
		AppointmentType: &apptType,
		PriceListType:   "appointment",
		PriceType:       "fixed",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	c.Set("account_uuid", accountUUID)

	mockRepo.CreateFunc = func(ctx context.Context, bfn *entity.BranchFieldNature) error {
		return errors.New("db error")
	}

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusInternalServerError, resp.Code)
}

func TestCreateBranchFieldNatureHandler_TreatmentInvalidAppointmentType(t *testing.T) {
	e := echo.New()
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := usecase.NewBranchFieldNatureUsecase(mockRepo)
	h := &BranchFieldNatureHandler{Usecase: uc, Logger: vlog.NewWithLevel("error")}

	accountUUID := core.NewID().Value()
	branchID := core.NewID().Value()
	fieldID := core.NewID().Value()
	natureID := core.NewID().Value()
	apptType := "live_latter" // Invalid for treatment
	request := BranchFieldNatureRequest{
		BranchID:        branchID,
		FieldID:         fieldID,
		NatureID:        natureID,
		ClinicianStatus: "active",
		Prices:          1000,
		Duration:        0,
		AppointmentType: &apptType,
		PriceListType:   "treatment",
		PriceType:       "fixed",
	}
	jsonReq, _ := json.Marshal(request)
	req := httptest.NewRequest(http.MethodPost, "/branch-field-nature", bytes.NewReader(jsonReq))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	resp := httptest.NewRecorder()
	c := e.NewContext(req, resp)
	c.Set("account_uuid", accountUUID)

	err := h.Create(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
	var response map[string]interface{}
	json.Unmarshal(resp.Body.Bytes(), &response)
	assert.False(t, response["status"].(bool))
	assert.Equal(t, "Invalid appointment type. Treatment price list supports only In-Clinic appointments.", response["message"])
}
