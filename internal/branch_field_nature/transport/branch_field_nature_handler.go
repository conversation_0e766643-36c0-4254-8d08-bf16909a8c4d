package transport

import (
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type BranchFieldNatureRequest struct {
	BranchID        string  `json:"branch_id"`
	FieldID         string  `json:"field_id"`
	NatureID        string  `json:"nature_id"`
	ClinicianStatus string  `json:"clinician_status"`
	Prices          int     `json:"prices"`
	Duration        int     `json:"duration"`
	AppointmentType *string `json:"appointment_type,omitempty"`
	PriceListType   string  `json:"price_list_type"`
	PriceType       string  `json:"price_type"`
}

type BranchFieldNatureResponse struct {
	ID              string  `json:"id"`
	AccountUUID     string  `json:"account_uuid"`
	BranchID        string  `json:"branch_id"`
	FieldID         string  `json:"field_id"`
	NatureID        string  `json:"nature_id"`
	ClinicianStatus string  `json:"clinician_status"`
	Prices          int     `json:"prices"`
	Duration        int     `json:"duration"`
	AppointmentType *string `json:"appointment_type,omitempty"`
	PriceListType   string  `json:"price_list_type"`
	PriceType       string  `json:"price_type"`
	CreatedDate     string  `json:"created_date"`
	UpdatedDate     string  `json:"updated_date"`
}

type BranchFieldNatureHandler struct {
	Usecase *usecase.BranchFieldNatureUsecase
	Logger  vlog.Logger
}

func NewBranchFieldNatureHandler(uc *usecase.BranchFieldNatureUsecase, logger vlog.Logger) *BranchFieldNatureHandler {
	return &BranchFieldNatureHandler{Usecase: uc, Logger: logger}
}

func ToBranchFieldNatureResponse(bfn entity.BranchFieldNature) BranchFieldNatureResponse {
	return BranchFieldNatureResponse{
		ID:              bfn.ID.Value(),
		AccountUUID:     bfn.AccountUUID.Value(),
		BranchID:        bfn.BranchID.Value(),
		FieldID:         bfn.FieldID.Value(),
		NatureID:        bfn.NatureID.Value(),
		ClinicianStatus: bfn.ClinicianStatus,
		Prices:          bfn.Prices,
		Duration:        bfn.Duration,
		AppointmentType: bfn.AppointmentType,
		PriceListType:   bfn.PriceListType,
		PriceType:       bfn.PriceType,
		CreatedDate:     bfn.CreatedDate.String(),
		UpdatedDate:     bfn.UpdatedDate.String(),
	}
}
