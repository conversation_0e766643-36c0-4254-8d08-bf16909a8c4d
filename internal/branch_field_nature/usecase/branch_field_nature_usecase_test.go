package usecase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/branch_field_nature/entity"
)

type mockBranchFieldNatureRepository struct {
	CreateFunc func(ctx context.Context, bfn *entity.BranchFieldNature) error
}

func (m *mockBranchFieldNatureRepository) Create(ctx context.Context, bfn *entity.BranchFieldNature) error {
	if m.CreateFunc != nil {
		return m.CreateFunc(ctx, bfn)
	}
	return nil
}

func TestNewBranchFieldNatureUsecase(t *testing.T) {
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := NewBranchFieldNatureUsecase(mockRepo)
	assert.NotNil(t, uc)
	assert.Equal(t, mockRepo, uc.repo)
}

func TestBranchFieldNatureUsecase_Create_Success(t *testing.T) {
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := NewBranchFieldNatureUsecase(mockRepo)
	bfn := &entity.BranchFieldNature{ClinicianStatus: "active", PriceListType: "appointment", PriceType: "fixed"}
	mockRepo.CreateFunc = func(ctx context.Context, b *entity.BranchFieldNature) error {
		assert.Equal(t, bfn, b)
		return nil
	}
	err := uc.Create(context.Background(), bfn)
	assert.NoError(t, err)
}

func TestBranchFieldNatureUsecase_Create_Error(t *testing.T) {
	mockRepo := &mockBranchFieldNatureRepository{}
	uc := NewBranchFieldNatureUsecase(mockRepo)
	mockRepo.CreateFunc = func(ctx context.Context, b *entity.BranchFieldNature) error {
		return assert.AnError
	}
	err := uc.Create(context.Background(), &entity.BranchFieldNature{})
	assert.Error(t, err)
}
