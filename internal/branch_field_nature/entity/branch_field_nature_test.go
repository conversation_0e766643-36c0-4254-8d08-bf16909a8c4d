package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

func TestBranchFieldNature_Creation(t *testing.T) {
	t.Run("valid branch field nature creation", func(t *testing.T) {
		id := core.NewID()
		accountUUID := core.NewID()
		branchID := core.NewID()
		fieldID := core.NewID()
		natureID := core.NewID()
		createdAt, _ := core.NewTimestampFromTime(time.Now())
		updatedAt, _ := core.NewTimestampFromTime(time.Now())
		apptType := "in_clinic"
		bfn := BranchFieldNature{
			ID:              *id,
			AccountUUID:     *accountUUID,
			BranchID:        *branchID,
			FieldID:         *fieldID,
			NatureID:        *natureID,
			ClinicianStatus: "active",
			Prices:          1000,
			Duration:        30,
			AppointmentType: &apptType,
			CreatedDate:     *createdAt,
			UpdatedDate:     *updatedAt,
			PriceListType:   "treatment",
			PriceType:       "fixed",
		}
		assert.Equal(t, *id, bfn.ID)
		assert.Equal(t, *accountUUID, bfn.AccountUUID)
		assert.Equal(t, *branchID, bfn.BranchID)
		assert.Equal(t, *fieldID, bfn.FieldID)
		assert.Equal(t, *natureID, bfn.NatureID)
		assert.Equal(t, "active", bfn.ClinicianStatus)
		assert.Equal(t, 1000, bfn.Prices)
		assert.Equal(t, 30, bfn.Duration)
		assert.Equal(t, &apptType, bfn.AppointmentType)
		assert.Equal(t, *createdAt, bfn.CreatedDate)
		assert.Equal(t, *updatedAt, bfn.UpdatedDate)
		assert.Equal(t, "treatment", bfn.PriceListType)
		assert.Equal(t, "fixed", bfn.PriceType)
	})

	t.Run("branch field nature with nil appointment type", func(t *testing.T) {
		id := core.NewID()
		bfn := BranchFieldNature{
			ID:              *id,
			AppointmentType: nil,
		}
		assert.Equal(t, *id, bfn.ID)
		assert.Nil(t, bfn.AppointmentType)
	})
}
