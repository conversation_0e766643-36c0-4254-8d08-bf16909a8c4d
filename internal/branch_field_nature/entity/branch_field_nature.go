package entity

import (
	"gitlab.viswalslab.com/backend/price-list/core"
)

type BranchFieldNature struct {
	ID              core.Identifier
	AccountUUID     core.Identifier
	BranchID        core.Identifier
	FieldID         core.Identifier
	NatureID        core.Identifier
	ClinicianStatus string
	Prices          int
	Duration        int
	AppointmentType *string
	PriceListType   string // "appointment" or "treatment"
	PriceType       string // "fixed" or "from"
	CreatedDate     core.Timestamp
	UpdatedDate     core.Timestamp
}
