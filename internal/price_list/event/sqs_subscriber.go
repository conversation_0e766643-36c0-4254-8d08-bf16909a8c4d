package event

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// SQSPriceListEventSubscriber implements PriceListEventSubscriber using AWS SQS
type SQSPriceListEventSubscriber struct {
	sqsClient *awspkg.SQSClient
	cfg       config.Configuration
	processor interface {
		// ProcessMessage processes a price list event message
		ProcessMessage(ctx context.Context, message *PriceListEventMessage) error
	}
	logger    vlog.Logger
	isRunning bool
	mutex     sync.RWMutex
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewSQSPriceListEventSubscriber creates a new SQS-based price list event subscriber
func NewSQSPriceListEventSubscriber(cfg config.Configuration, queueURL string) (*SQSPriceListEventSubscriber, error) {
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        queueURL,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS client: %w", err)
	}

	return &SQSPriceListEventSubscriber{
		sqsClient: sqsClient,
		cfg:       cfg,
		logger:    vlog.New().With(vlog.F("subscriber", "PriceListEventSubscriber")),
		stopChan:  make(chan struct{}),
	}, nil
}

// Subscribe configures the event processor for the subscriber
func (s *SQSPriceListEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *PriceListEventMessage) error
}) error {
	s.processor = processor
	return nil
}

// Start starts the subscription service
func (s *SQSPriceListEventSubscriber) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("price list event subscriber is already running")
	}

	if s.processor == nil {
		return fmt.Errorf("event processor is required")
	}

	s.logger.Info("starting price list event subscriber")

	s.isRunning = true
	s.wg.Add(1)

	go s.pollMessages(ctx)

	return nil
}

// Stop stops the subscription service
func (s *SQSPriceListEventSubscriber) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("stopping price list event subscriber")

	close(s.stopChan)
	s.isRunning = false

	// Wait for polling goroutine to finish
	s.wg.Wait()

	s.logger.Info("price list event subscriber stopped")
	return nil
}

// IsRunning returns true if the subscriber is running
func (s *SQSPriceListEventSubscriber) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// pollMessages continuously polls for messages from SQS
func (s *SQSPriceListEventSubscriber) pollMessages(ctx context.Context) {
	defer s.wg.Done()

	s.logger.Info("started polling for price list events")

	ticker := time.NewTicker(5 * time.Second) // Poll every 5 seconds
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			s.logger.Info("received stop signal, stopping message polling")
			return
		case <-ctx.Done():
			s.logger.Info("context cancelled, stopping message polling")
			return
		case <-ticker.C:
			s.pollAndProcessMessages(ctx)
		}
	}
}

// pollAndProcessMessages polls for messages and processes them
func (s *SQSPriceListEventSubscriber) pollAndProcessMessages(ctx context.Context) {
	messages, err := s.sqsClient.ReceiveMessages(ctx, 10, 20) // Max 10 messages, 20 seconds wait
	if err != nil {
		s.logger.Error("failed to receive messages from SQS", vlog.F("error", err))
		return
	}

	if len(messages) == 0 {
		s.logger.Debug("no messages received from SQS")
		return
	}

	s.logger.Info("received messages from SQS", vlog.F("count", len(messages)))

	for _, msg := range messages {
		if err := s.processMessage(ctx, &msg); err != nil {
			s.logger.Error("failed to process message", 
				vlog.F("error", err),
				vlog.F("message_id", *msg.MessageId))
			continue
		}

		// Delete message after successful processing
		if err := s.sqsClient.DeleteMessage(ctx, *msg.ReceiptHandle); err != nil {
			s.logger.Error("failed to delete message from SQS", 
				vlog.F("error", err),
				vlog.F("message_id", *msg.MessageId))
		} else {
			s.logger.Debug("message deleted from SQS", vlog.F("message_id", *msg.MessageId))
		}
	}
}

// processMessage processes a single SQS message
func (s *SQSPriceListEventSubscriber) processMessage(ctx context.Context, msg *types.Message) error {
	if msg.Body == nil {
		return fmt.Errorf("message body is nil")
	}

	var eventMessage PriceListEventMessage
	if err := json.Unmarshal([]byte(*msg.Body), &eventMessage); err != nil {
		return fmt.Errorf("failed to unmarshal message: %w", err)
	}

	if err := s.processor.ProcessMessage(ctx, &eventMessage); err != nil {
		return fmt.Errorf("failed to handle event message: %w", err)
	}

	return nil
}
