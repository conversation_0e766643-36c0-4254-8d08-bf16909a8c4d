package event

import (
	"time"
)

// PriceListFieldData represents a single field with its status in the price list event
type PriceListFieldData struct {
	ID       string `json:"id"`
	StatusID string `json:"status_id"`
}

// PriceListRoleData represents the role information in the price list event
type PriceListRoleData struct {
	ID      string `json:"id"`
	Acronym string `json:"acronym"`
}

// PriceListEventData represents the data structure for price list events
type PriceListEventData struct {
	BranchID  string                   `json:"branch_id"`
	AccountID string                   `json:"account_id"`
	Role      PriceListRoleData        `json:"role"`
	Fields    []PriceListFieldData     `json:"fields"`
}

// PriceListEventMessage represents the event message structure for price list operations
type PriceListEventMessage struct {
	Data      PriceListEventData `json:"data"`
	Timestamp time.Time          `json:"timestamp"`
	Source    string             `json:"source"`
	EventType string             `json:"event_type"`
}
