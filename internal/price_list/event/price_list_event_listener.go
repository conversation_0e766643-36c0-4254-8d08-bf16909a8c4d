package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// PriceListEventSubscriber defines the interface for subscribing to price list events
type PriceListEventSubscriber interface {
	// Subscribe starts listening to price list events
	Subscribe(ctx context.Context, processor interface {
		ProcessMessage(ctx context.Context, message *PriceListEventMessage) error
	}) error

	// Start starts the subscription service
	Start(ctx context.Context) error

	// Stop stops the subscription service
	Stop() error

	// IsRunning returns true if the subscriber is running
	IsRunning() bool
}

// UpsertPriceListsUsecase defines the interface for batch price list upsert operations
type UpsertPriceListsUsecase interface {
	Execute(ctx context.Context, input interface{}) (interface{}, error)
}

// PriceListEventListener manages the price list event subscription service
type PriceListEventListener struct {
	subscriber PriceListEventSubscriber
	cfg        config.Configuration
}

// NewPriceListEventListener creates a new price list event listener instance
func NewPriceListEventListener(cfg config.Configuration, upsertUsecase UpsertPriceListsUsecase) (*PriceListEventListener, error) {
	// Validate that price list queue URL is configured
	if cfg.AWS.SQSQueueURLPriceList == "" {
		return nil, fmt.Errorf("AWS_SQS_QUEUE_URL_PRICE_LIST is required for price list event operations")
	}

	// Initialize SQS event subscriber with price list queue URL
	subscriber, err := NewSQSPriceListEventSubscriber(cfg, cfg.AWS.SQSQueueURLPriceList)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS event subscriber: %w", err)
	}

	// Create event processor
	eventProcessor := NewPriceListUpsertEventProcessor(upsertUsecase)

	// Set up subscriber with processor
	if err := subscriber.Subscribe(context.Background(), eventProcessor); err != nil {
		return nil, fmt.Errorf("failed to set up event subscription: %w", err)
	}

	return &PriceListEventListener{
		subscriber: subscriber,
		cfg:        cfg,
	}, nil
}

// Start starts the price list event listener
func (l *PriceListEventListener) Start(ctx context.Context) error {
	logger := vlog.New().With(vlog.F("listener", "PriceListEventListener"))
	
	if l.subscriber == nil {
		logger.Error("subscriber is not initialized")
		return fmt.Errorf("subscriber is not initialized")
	}

	if l.subscriber.IsRunning() {
		logger.Warn("price list event listener is already running")
		return nil
	}

	logger.Info("starting price list event listener")

	if err := l.subscriber.Start(ctx); err != nil {
		logger.Error("failed to start price list event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to start price list event subscriber: %w", err)
	}

	logger.Info("price list event listener started successfully")
	return nil
}

// Stop stops the price list event listener
func (l *PriceListEventListener) Stop() error {
	logger := vlog.New().With(vlog.F("listener", "PriceListEventListener"))
	
	if l.subscriber == nil {
		logger.Error("subscriber is not initialized")
		return fmt.Errorf("subscriber is not initialized")
	}

	if !l.subscriber.IsRunning() {
		logger.Info("price list event listener is not running")
		return nil
	}

	logger.Info("stopping price list event listener")

	if err := l.subscriber.Stop(); err != nil {
		logger.Error("failed to stop price list event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to stop price list event subscriber: %w", err)
	}

	logger.Info("price list event listener stopped successfully")
	return nil
}

// IsRunning returns true if the price list event listener is running
func (l *PriceListEventListener) IsRunning() bool {
	if l.subscriber == nil {
		return false
	}
	return l.subscriber.IsRunning()
}
