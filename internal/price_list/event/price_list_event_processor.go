package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertPriceListsUsecase defines the interface for batch price list upsert operations
type UpsertPriceListsUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertPriceListsInput) (*usecase.UpsertPriceListsOutput, error)
}

// PriceListUpsertEventProcessor processes price list upsert events
type PriceListUpsertEventProcessor struct {
	upsertUsecase UpsertPriceListsUsecase
}

// NewPriceListUpsertEventProcessor creates a new price list upsert event processor
func NewPriceListUpsertEventProcessor(upsertUsecase UpsertPriceListsUsecase) *PriceListUpsertEventProcessor {
	return &PriceListUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// ProcessMessage processes a price list event message
func (p *PriceListUpsertEventProcessor) ProcessMessage(ctx context.Context, message *PriceListEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "PriceListUpsertEventProcessor"))
	logger.Debug("handling price list event message", vlog.F("event_type", message.EventType))

	// Validate message data
	if message.Data.BranchID == "" {
		logger.Error("validation failed", vlog.F("error", "branch ID is required"))
		return core.NewBusinessError("branch ID is required in message data")
	}

	if message.Data.AccountID == "" {
		logger.Error("validation failed", vlog.F("error", "account ID is required"))
		return core.NewBusinessError("account ID is required in message data")
	}

	if message.Data.Role.ID == "" {
		logger.Error("validation failed", vlog.F("error", "role ID is required"))
		return core.NewBusinessError("role ID is required in message data")
	}

	if message.Data.Role.Acronym == "" {
		logger.Error("validation failed", vlog.F("error", "role acronym is required"))
		return core.NewBusinessError("role acronym is required in message data")
	}

	if len(message.Data.Fields) == 0 {
		logger.Error("validation failed", vlog.F("error", "at least one field is required"))
		return core.NewBusinessError("at least one field is required in message data")
	}

	// Validate each field
	for i, field := range message.Data.Fields {
		if field.ID == "" {
			logger.Error("validation failed", 
				vlog.F("error", "field ID is required"),
				vlog.F("field_index", i))
			return core.NewBusinessError(fmt.Sprintf("field ID is required for field at index %d", i))
		}

		if field.StatusID == "" {
			logger.Error("validation failed", 
				vlog.F("error", "status ID is required"),
				vlog.F("field_index", i),
				vlog.F("field_id", field.ID))
			return core.NewBusinessError(fmt.Sprintf("status ID is required for field %s", field.ID))
		}
	}

	logger.Info("processing price list upsert",
		vlog.F("branch_id", message.Data.BranchID),
		vlog.F("account_id", message.Data.AccountID),
		vlog.F("role_id", message.Data.Role.ID),
		vlog.F("role_acronym", message.Data.Role.Acronym),
		vlog.F("fields_count", len(message.Data.Fields)))

	// Convert event fields to usecase input fields
	fields := make([]usecase.UpsertPriceListFieldInput, 0, len(message.Data.Fields))
	for _, field := range message.Data.Fields {
		fields = append(fields, usecase.UpsertPriceListFieldInput{
			FieldID:  field.ID,
			StatusID: field.StatusID,
		})
	}

	// Create upsert input
	upsertInput := &usecase.UpsertPriceListsInput{
		BranchID:  message.Data.BranchID,
		AccountID: message.Data.AccountID,
		RoleID:    message.Data.Role.ID,
		Acronym:   message.Data.Role.Acronym,
		Fields:    fields,
	}

	// Execute the batch upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	output, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		logger.Error("failed to process price list upsert from message", vlog.F("error", err))
		return fmt.Errorf("failed to process price list upsert from message: %w", err)
	}

	logger.Info("successfully processed price list upsert",
		vlog.F("branch_id", message.Data.BranchID),
		vlog.F("processed_count", output.ProcessedCount),
		vlog.F("success_count", output.SuccessCount),
		vlog.F("failure_count", output.FailureCount))

	// Log any failures for individual fields
	if output.FailureCount > 0 {
		logger.Warn("some field updates failed",
			vlog.F("failure_count", output.FailureCount),
			vlog.F("success_count", output.SuccessCount))

		for _, result := range output.Results {
			if !result.Success {
				logger.Error("field update failed",
					vlog.F("field_id", result.FieldID),
					vlog.F("status_id", result.StatusID),
					vlog.F("error", result.Error))
			}
		}
	}

	return nil
}
