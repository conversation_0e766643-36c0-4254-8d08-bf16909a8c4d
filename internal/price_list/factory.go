package price_list

import (
	"fmt"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/event"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/service"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/usecase"
)

// Factory encapsulates the creation and initialization of price list components
type Factory struct {
	// Event components
	PriceListEventListener *event.PriceListEventListener

	// Repository components
	PriceListRepository       repository.PriceListRepository
	UpsertPriceListRepository repository.UpsertPriceListRepository

	// Service components
	PriceListValidationService service.PriceListValidationService

	// UseCase components
	UpsertPriceListUsecase  usecase.UpsertPriceListUsecase
	UpsertPriceListsUsecase usecase.UpsertPriceListsUsecase
}

// NewFactory creates and initializes all price list components
func NewFactory(cfg config.Configuration, db *sqlx.DB) (*Factory, error) {
	if db == nil {
		return nil, fmt.Errorf("database connection is required")
	}

	// Initialize repositories
	priceListRepository := repository.NewPriceListRepository(db)
	upsertPriceListRepository := repository.NewUpsertPriceListRepository(db)

	// Initialize services
	priceListValidationService := service.NewPriceListValidationService()

	// Initialize use cases
	upsertPriceListUsecase := usecase.NewUpsertPriceListUsecase(
		upsertPriceListRepository,
		priceListValidationService,
	)

	upsertPriceListsUsecase := usecase.NewUpsertPriceListsUsecase(
		upsertPriceListUsecase,
	)

	// Initialize event listener
	priceListEventListener, err := event.NewPriceListEventListener(cfg, upsertPriceListsUsecase)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize price list event listener: %w", err)
	}

	return &Factory{
		// Event components
		PriceListEventListener: priceListEventListener,

		// Repository components
		PriceListRepository:       priceListRepository,
		UpsertPriceListRepository: upsertPriceListRepository,

		// Service components
		PriceListValidationService: priceListValidationService,

		// UseCase components
		UpsertPriceListUsecase:  upsertPriceListUsecase,
		UpsertPriceListsUsecase: upsertPriceListsUsecase,
	}, nil
}
