package repository

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/price_list/entity"
)

// PriceListRepository defines the interface for price list data operations
type PriceListRepository interface {
	// GetByID retrieves a price list entry by its ID
	GetByID(ctx context.Context, id string) (*entity.PriceList, error)

	// GetByBranchAndField retrieves price list entries by branch and field
	GetByBranchAndField(ctx context.Context, branchID, fieldID string) ([]*entity.PriceList, error)

	// List retrieves price list entries with optional filtering
	List(ctx context.Context, filter *GetPriceListsFilter) (*GetPriceListsResult, error)
}

// GetPriceListsFilter represents filter criteria for listing price lists
type GetPriceListsFilter struct {
	BranchID  *string
	AccountID *string
	FieldID   *string
	StatusID  *string
	RoleID    *string
	Limit     *int
	Offset    *int
}

// GetPriceListsResult represents the result of listing price lists
type GetPriceListsResult struct {
	PriceLists []*entity.PriceList
	Total      int
}
