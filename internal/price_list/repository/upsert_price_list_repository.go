package repository

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertPriceListRepository defines the interface for price list upsert operations
type UpsertPriceListRepository interface {
	// Upsert creates or updates a price list entry
	Upsert(ctx context.Context, priceList *entity.PriceList) error
}

// PriceListModel represents the database model for price list
type PriceListModel struct {
	ID        string         `db:"id"`
	BranchID  string         `db:"branch_id"`
	AccountID string         `db:"account_id"`
	FieldID   string         `db:"field_id"`
	StatusID  string         `db:"status_id"`
	RoleID    string         `db:"role_id"`
	Acronym   string         `db:"acronym"`
	CreatedAt sql.NullTime   `db:"created_at"`
	UpdatedAt sql.NullTime   `db:"updated_at"`
}

// priceListRepository implements both PriceListRepository and UpsertPriceListRepository
type priceListRepository struct {
	db *sqlx.DB
}

// NewPriceListRepository creates a new price list repository instance
func NewPriceListRepository(db *sqlx.DB) PriceListRepository {
	return &priceListRepository{db: db}
}

// NewUpsertPriceListRepository creates a new upsert price list repository instance
func NewUpsertPriceListRepository(db *sqlx.DB) UpsertPriceListRepository {
	return &priceListRepository{db: db}
}

// Upsert creates or updates a price list entry
func (r *priceListRepository) Upsert(ctx context.Context, priceList *entity.PriceList) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "PriceListRepository"))

	query := `
		INSERT INTO price_list_field_status (
			id, branch_id, account_id, field_id, status_id, role_id, acronym, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9
		)
		ON CONFLICT (branch_id, field_id, account_id, role_id) 
		DO UPDATE SET
			status_id = EXCLUDED.status_id,
			acronym = EXCLUDED.acronym,
			updated_at = EXCLUDED.updated_at
		RETURNING id, created_at, updated_at`

	var model PriceListModel
	err := r.db.QueryRowxContext(ctx, query,
		priceList.ID().Value(),
		priceList.BranchID().Value(),
		priceList.AccountID().Value(),
		priceList.FieldID().Value(),
		priceList.StatusID().Value(),
		priceList.RoleID().Value(),
		priceList.Acronym(),
		priceList.CreatedAt().Value(),
		priceList.UpdatedAt().Value(),
	).StructScan(&model)

	if err != nil {
		logger.Error("failed to upsert price list", vlog.F("error", err))
		return fmt.Errorf("failed to upsert price list: %w", err)
	}

	logger.Info("price list upserted successfully", 
		vlog.F("id", model.ID),
		vlog.F("branch_id", priceList.BranchID().Value()),
		vlog.F("field_id", priceList.FieldID().Value()))

	return nil
}

// GetByID retrieves a price list entry by its ID
func (r *priceListRepository) GetByID(ctx context.Context, id string) (*entity.PriceList, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "PriceListRepository"))

	query := `
		SELECT id, branch_id, account_id, field_id, status_id, role_id, acronym, created_at, updated_at
		FROM price_list_field_status 
		WHERE id = $1`

	var model PriceListModel
	err := r.db.GetContext(ctx, &model, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			logger.Debug("price list not found", vlog.F("id", id))
			return nil, nil
		}
		logger.Error("failed to get price list by ID", vlog.F("error", err), vlog.F("id", id))
		return nil, fmt.Errorf("failed to get price list by ID: %w", err)
	}

	priceList, err := r.modelToEntity(&model)
	if err != nil {
		logger.Error("failed to convert model to entity", vlog.F("error", err))
		return nil, fmt.Errorf("failed to convert model to entity: %w", err)
	}

	return priceList, nil
}

// GetByBranchAndField retrieves price list entries by branch and field
func (r *priceListRepository) GetByBranchAndField(ctx context.Context, branchID, fieldID string) ([]*entity.PriceList, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "PriceListRepository"))

	query := `
		SELECT id, branch_id, account_id, field_id, status_id, role_id, acronym, created_at, updated_at
		FROM price_list_field_status 
		WHERE branch_id = $1 AND field_id = $2
		ORDER BY created_at DESC`

	var models []PriceListModel
	err := r.db.SelectContext(ctx, &models, query, branchID, fieldID)
	if err != nil {
		logger.Error("failed to get price lists by branch and field", 
			vlog.F("error", err), 
			vlog.F("branch_id", branchID), 
			vlog.F("field_id", fieldID))
		return nil, fmt.Errorf("failed to get price lists by branch and field: %w", err)
	}

	priceLists := make([]*entity.PriceList, 0, len(models))
	for _, model := range models {
		priceList, err := r.modelToEntity(&model)
		if err != nil {
			logger.Error("failed to convert model to entity", vlog.F("error", err))
			return nil, fmt.Errorf("failed to convert model to entity: %w", err)
		}
		priceLists = append(priceLists, priceList)
	}

	return priceLists, nil
}

// List retrieves price list entries with optional filtering
func (r *priceListRepository) List(ctx context.Context, filter *GetPriceListsFilter) (*GetPriceListsResult, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "PriceListRepository"))

	baseQuery := `
		SELECT id, branch_id, account_id, field_id, status_id, role_id, acronym, created_at, updated_at
		FROM price_list_field_status`
	
	countQuery := `SELECT COUNT(*) FROM price_list_field_status`
	
	whereClause := ""
	args := []interface{}{}
	argIndex := 1

	if filter != nil {
		conditions := []string{}
		
		if filter.BranchID != nil {
			conditions = append(conditions, fmt.Sprintf("branch_id = $%d", argIndex))
			args = append(args, *filter.BranchID)
			argIndex++
		}
		
		if filter.AccountID != nil {
			conditions = append(conditions, fmt.Sprintf("account_id = $%d", argIndex))
			args = append(args, *filter.AccountID)
			argIndex++
		}
		
		if filter.FieldID != nil {
			conditions = append(conditions, fmt.Sprintf("field_id = $%d", argIndex))
			args = append(args, *filter.FieldID)
			argIndex++
		}
		
		if filter.StatusID != nil {
			conditions = append(conditions, fmt.Sprintf("status_id = $%d", argIndex))
			args = append(args, *filter.StatusID)
			argIndex++
		}
		
		if filter.RoleID != nil {
			conditions = append(conditions, fmt.Sprintf("role_id = $%d", argIndex))
			args = append(args, *filter.RoleID)
			argIndex++
		}

		if len(conditions) > 0 {
			whereClause = " WHERE " + fmt.Sprintf("%s", conditions[0])
			for i := 1; i < len(conditions); i++ {
				whereClause += " AND " + conditions[i]
			}
		}
	}

	// Get total count
	var total int
	err := r.db.GetContext(ctx, &total, countQuery+whereClause, args...)
	if err != nil {
		logger.Error("failed to get price lists count", vlog.F("error", err))
		return nil, fmt.Errorf("failed to get price lists count: %w", err)
	}

	// Build final query with pagination
	finalQuery := baseQuery + whereClause + " ORDER BY created_at DESC"
	
	if filter != nil {
		if filter.Limit != nil {
			finalQuery += fmt.Sprintf(" LIMIT $%d", argIndex)
			args = append(args, *filter.Limit)
			argIndex++
		}
		
		if filter.Offset != nil {
			finalQuery += fmt.Sprintf(" OFFSET $%d", argIndex)
			args = append(args, *filter.Offset)
		}
	}

	var models []PriceListModel
	err = r.db.SelectContext(ctx, &models, finalQuery, args...)
	if err != nil {
		logger.Error("failed to list price lists", vlog.F("error", err))
		return nil, fmt.Errorf("failed to list price lists: %w", err)
	}

	priceLists := make([]*entity.PriceList, 0, len(models))
	for _, model := range models {
		priceList, err := r.modelToEntity(&model)
		if err != nil {
			logger.Error("failed to convert model to entity", vlog.F("error", err))
			return nil, fmt.Errorf("failed to convert model to entity: %w", err)
		}
		priceLists = append(priceLists, priceList)
	}

	return &GetPriceListsResult{
		PriceLists: priceLists,
		Total:      total,
	}, nil
}

// modelToEntity converts a database model to an entity
func (r *priceListRepository) modelToEntity(model *PriceListModel) (*entity.PriceList, error) {
	input := &entity.NewPriceListInput{
		ID:        model.ID,
		BranchID:  model.BranchID,
		AccountID: model.AccountID,
		FieldID:   model.FieldID,
		StatusID:  model.StatusID,
		RoleID:    model.RoleID,
		Acronym:   model.Acronym,
	}

	if model.CreatedAt.Valid {
		input.CreatedAt = &model.CreatedAt.Time
	}

	if model.UpdatedAt.Valid {
		input.UpdatedAt = &model.UpdatedAt.Time
	}

	return entity.HydratePriceList(input)
}
