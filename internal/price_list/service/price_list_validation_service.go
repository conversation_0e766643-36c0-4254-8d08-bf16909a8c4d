package service

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// PriceListValidationService defines the interface for price list validation operations
type PriceListValidationService interface {
	// ValidatePriceListData validates price list data for business rules
	ValidatePriceListData(ctx context.Context, input *ValidatePriceListInput) error

	// ValidateFieldStatusUpdate validates field status update operations
	ValidateFieldStatusUpdate(ctx context.Context, input *ValidateFieldStatusUpdateInput) error
}

// ValidatePriceListInput represents input for price list validation
type ValidatePriceListInput struct {
	BranchID  string
	AccountID string
	FieldID   string
	StatusID  string
	RoleID    string
	Acronym   string
}

// ValidateFieldStatusUpdateInput represents input for field status update validation
type ValidateFieldStatusUpdateInput struct {
	BranchID  string
	AccountID string
	FieldID   string
	StatusID  string
	RoleID    string
	Acronym   string
}

// priceListValidationService implements PriceListValidationService
type priceListValidationService struct {
	logger vlog.Logger
}

// NewPriceListValidationService creates a new price list validation service
func NewPriceListValidationService() PriceListValidationService {
	return &priceListValidationService{
		logger: vlog.New().With(vlog.F("service", "PriceListValidationService")),
	}
}

// ValidatePriceListData validates price list data for business rules
func (s *priceListValidationService) ValidatePriceListData(ctx context.Context, input *ValidatePriceListInput) error {
	logger := vlog.FromContext(ctx).With(vlog.F("service", "PriceListValidationService"))

	if input == nil {
		logger.Error("validation input is required")
		return core.NewBusinessError("validation input is required")
	}

	// Validate required fields
	if input.BranchID == "" {
		logger.Error("branch ID is required")
		return core.NewBusinessError("branch ID is required")
	}

	if input.AccountID == "" {
		logger.Error("account ID is required")
		return core.NewBusinessError("account ID is required")
	}

	if input.FieldID == "" {
		logger.Error("field ID is required")
		return core.NewBusinessError("field ID is required")
	}

	if input.StatusID == "" {
		logger.Error("status ID is required")
		return core.NewBusinessError("status ID is required")
	}

	if input.RoleID == "" {
		logger.Error("role ID is required")
		return core.NewBusinessError("role ID is required")
	}

	if input.Acronym == "" {
		logger.Error("acronym is required")
		return core.NewBusinessError("acronym is required")
	}

	// Validate UUID formats
	if _, err := core.ParseID(input.BranchID); err != nil {
		logger.Error("invalid branch ID format", vlog.F("branch_id", input.BranchID))
		return core.NewBusinessError("invalid branch ID format")
	}

	if _, err := core.ParseID(input.AccountID); err != nil {
		logger.Error("invalid account ID format", vlog.F("account_id", input.AccountID))
		return core.NewBusinessError("invalid account ID format")
	}

	if _, err := core.ParseID(input.FieldID); err != nil {
		logger.Error("invalid field ID format", vlog.F("field_id", input.FieldID))
		return core.NewBusinessError("invalid field ID format")
	}

	if _, err := core.ParseID(input.StatusID); err != nil {
		logger.Error("invalid status ID format", vlog.F("status_id", input.StatusID))
		return core.NewBusinessError("invalid status ID format")
	}

	if _, err := core.ParseID(input.RoleID); err != nil {
		logger.Error("invalid role ID format", vlog.F("role_id", input.RoleID))
		return core.NewBusinessError("invalid role ID format")
	}

	// Validate acronym length and format
	if len(input.Acronym) < 2 || len(input.Acronym) > 50 {
		logger.Error("acronym must be between 2 and 50 characters", vlog.F("acronym", input.Acronym))
		return core.NewBusinessError("acronym must be between 2 and 50 characters")
	}

	logger.Debug("price list data validation passed", 
		vlog.F("branch_id", input.BranchID),
		vlog.F("field_id", input.FieldID),
		vlog.F("status_id", input.StatusID))

	return nil
}

// ValidateFieldStatusUpdate validates field status update operations
func (s *priceListValidationService) ValidateFieldStatusUpdate(ctx context.Context, input *ValidateFieldStatusUpdateInput) error {
	logger := vlog.FromContext(ctx).With(vlog.F("service", "PriceListValidationService"))

	if input == nil {
		logger.Error("validation input is required")
		return core.NewBusinessError("validation input is required")
	}

	// Use the same validation logic as ValidatePriceListData
	validateInput := &ValidatePriceListInput{
		BranchID:  input.BranchID,
		AccountID: input.AccountID,
		FieldID:   input.FieldID,
		StatusID:  input.StatusID,
		RoleID:    input.RoleID,
		Acronym:   input.Acronym,
	}

	if err := s.ValidatePriceListData(ctx, validateInput); err != nil {
		logger.Error("field status update validation failed", vlog.F("error", err))
		return fmt.Errorf("field status update validation failed: %w", err)
	}

	// Additional validation specific to field status updates can be added here
	logger.Debug("field status update validation passed", 
		vlog.F("branch_id", input.BranchID),
		vlog.F("field_id", input.FieldID),
		vlog.F("status_id", input.StatusID))

	return nil
}

// ValidateEntityData validates a price list entity
func (s *priceListValidationService) ValidateEntityData(ctx context.Context, priceList *entity.PriceList) error {
	logger := vlog.FromContext(ctx).With(vlog.F("service", "PriceListValidationService"))

	if priceList == nil {
		logger.Error("price list entity is required")
		return core.NewBusinessError("price list entity is required")
	}

	input := &ValidatePriceListInput{
		BranchID:  priceList.BranchID().Value(),
		AccountID: priceList.AccountID().Value(),
		FieldID:   priceList.FieldID().Value(),
		StatusID:  priceList.StatusID().Value(),
		RoleID:    priceList.RoleID().Value(),
		Acronym:   priceList.Acronym(),
	}

	return s.ValidatePriceListData(ctx, input)
}
