package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// PriceList represents a price list entity for field status updates
type PriceList struct {
	id        core.Identifier
	branchID  core.Identifier
	accountID core.Identifier
	fieldID   core.Identifier
	statusID  core.Identifier
	roleID    core.Identifier
	acronym   string
	createdAt core.Timestamp
	updatedAt core.Timestamp
}

// NewPriceListInput represents the input for creating a new price list entry
type NewPriceListInput struct {
	ID        string
	BranchID  string
	AccountID string
	FieldID   string
	StatusID  string
	RoleID    string
	Acronym   string
	CreatedAt *time.Time
	UpdatedAt *time.Time
}

// NewPriceList creates a new price list entity with validation
func NewPriceList(input *NewPriceListInput) (*PriceList, error) {
	if input == nil {
		return nil, core.NewBusinessError("input is required")
	}

	if input.BranchID == "" {
		return nil, core.NewBusinessError("branch ID is required")
	}

	if input.AccountID == "" {
		return nil, core.NewBusinessError("account ID is required")
	}

	if input.FieldID == "" {
		return nil, core.NewBusinessError("field ID is required")
	}

	if input.StatusID == "" {
		return nil, core.NewBusinessError("status ID is required")
	}

	if input.RoleID == "" {
		return nil, core.NewBusinessError("role ID is required")
	}

	if input.Acronym == "" {
		return nil, core.NewBusinessError("acronym is required")
	}

	var id core.Identifier
	if input.ID != "" {
		parsedID, err := core.ParseID(input.ID)
		if err != nil {
			return nil, core.NewBusinessError("invalid ID format")
		}
		id = parsedID
	} else {
		id = core.NewID()
	}

	branchID, err := core.ParseID(input.BranchID)
	if err != nil {
		return nil, core.NewBusinessError("invalid branch ID format")
	}

	accountID, err := core.ParseID(input.AccountID)
	if err != nil {
		return nil, core.NewBusinessError("invalid account ID format")
	}

	fieldID, err := core.ParseID(input.FieldID)
	if err != nil {
		return nil, core.NewBusinessError("invalid field ID format")
	}

	statusID, err := core.ParseID(input.StatusID)
	if err != nil {
		return nil, core.NewBusinessError("invalid status ID format")
	}

	roleID, err := core.ParseID(input.RoleID)
	if err != nil {
		return nil, core.NewBusinessError("invalid role ID format")
	}

	now := time.Now()
	createdAt := core.NewTimestamp(now)
	updatedAt := core.NewTimestamp(now)

	if input.CreatedAt != nil {
		createdAt = core.NewTimestamp(*input.CreatedAt)
	}

	if input.UpdatedAt != nil {
		updatedAt = core.NewTimestamp(*input.UpdatedAt)
	}

	return &PriceList{
		id:        id,
		branchID:  branchID,
		accountID: accountID,
		fieldID:   fieldID,
		statusID:  statusID,
		roleID:    roleID,
		acronym:   input.Acronym,
		createdAt: createdAt,
		updatedAt: updatedAt,
	}, nil
}

// HydratePriceList creates a price list entity from existing data
func HydratePriceList(input *NewPriceListInput) (*PriceList, error) {
	return NewPriceList(input)
}

// Getters
func (p *PriceList) ID() core.Identifier {
	return p.id
}

func (p *PriceList) BranchID() core.Identifier {
	return p.branchID
}

func (p *PriceList) AccountID() core.Identifier {
	return p.accountID
}

func (p *PriceList) FieldID() core.Identifier {
	return p.fieldID
}

func (p *PriceList) StatusID() core.Identifier {
	return p.statusID
}

func (p *PriceList) RoleID() core.Identifier {
	return p.roleID
}

func (p *PriceList) Acronym() string {
	return p.acronym
}

func (p *PriceList) CreatedAt() core.Timestamp {
	return p.createdAt
}

func (p *PriceList) UpdatedAt() core.Timestamp {
	return p.updatedAt
}

// SetUpdatedAt updates the updated timestamp
func (p *PriceList) SetUpdatedAt(timestamp core.Timestamp) {
	p.updatedAt = timestamp
}
