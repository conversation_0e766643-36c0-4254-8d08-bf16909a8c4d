package usecase

import (
	"context"
	"fmt"
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/price_list/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertPriceListInput represents the input for upserting price list entries
type UpsertPriceListInput struct {
	ID        *string
	BranchID  string
	AccountID string
	FieldID   string
	StatusID  string
	RoleID    string
	Acronym   string
}

// UpsertPriceListOutput represents the output of upserting a price list entry
type UpsertPriceListOutput struct {
	ID        string
	BranchID  string
	AccountID string
	FieldID   string
	StatusID  string
	RoleID    string
	Acronym   string
	CreatedAt time.Time
	UpdatedAt time.Time
}

// UpsertPriceListUsecase defines the interface for price list upsert operations
type UpsertPriceListUsecase interface {
	Execute(ctx context.Context, input *UpsertPriceListInput) (*UpsertPriceListOutput, error)
}

// upsertPriceListUsecase implements UpsertPriceListUsecase
type upsertPriceListUsecase struct {
	repository        repository.UpsertPriceListRepository
	validationService service.PriceListValidationService
}

// NewUpsertPriceListUsecase creates a new upsert price list use case
func NewUpsertPriceListUsecase(
	repository repository.UpsertPriceListRepository,
	validationService service.PriceListValidationService,
) UpsertPriceListUsecase {
	return &upsertPriceListUsecase{
		repository:        repository,
		validationService: validationService,
	}
}

// Execute performs the upsert operation for a price list entry
func (u *upsertPriceListUsecase) Execute(ctx context.Context, input *UpsertPriceListInput) (*UpsertPriceListOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", "UpsertPriceListUsecase"))

	if input == nil {
		logger.Error("input is required")
		return nil, core.NewBusinessError("input is required")
	}

	logger.Info("starting price list upsert operation",
		vlog.F("branch_id", input.BranchID),
		vlog.F("field_id", input.FieldID),
		vlog.F("status_id", input.StatusID),
		vlog.F("role_id", input.RoleID))

	// Validate input data
	validationInput := &service.ValidatePriceListInput{
		BranchID:  input.BranchID,
		AccountID: input.AccountID,
		FieldID:   input.FieldID,
		StatusID:  input.StatusID,
		RoleID:    input.RoleID,
		Acronym:   input.Acronym,
	}

	if err := u.validationService.ValidatePriceListData(ctx, validationInput); err != nil {
		logger.Error("validation failed", vlog.F("error", err))
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Create entity input
	entityInput := &entity.NewPriceListInput{
		BranchID:  input.BranchID,
		AccountID: input.AccountID,
		FieldID:   input.FieldID,
		StatusID:  input.StatusID,
		RoleID:    input.RoleID,
		Acronym:   input.Acronym,
	}

	// Set ID if provided
	if input.ID != nil {
		entityInput.ID = *input.ID
	}

	// Create price list entity
	priceList, err := entity.NewPriceList(entityInput)
	if err != nil {
		logger.Error("failed to create price list entity", vlog.F("error", err))
		return nil, fmt.Errorf("failed to create price list entity: %w", err)
	}

	// Validate entity
	if err := u.validationService.ValidateEntityData(ctx, priceList); err != nil {
		logger.Error("entity validation failed", vlog.F("error", err))
		return nil, fmt.Errorf("entity validation failed: %w", err)
	}

	// Perform upsert operation
	if err := u.repository.Upsert(ctx, priceList); err != nil {
		logger.Error("failed to upsert price list", vlog.F("error", err))
		return nil, fmt.Errorf("failed to upsert price list: %w", err)
	}

	logger.Info("price list upserted successfully",
		vlog.F("id", priceList.ID().Value()),
		vlog.F("branch_id", priceList.BranchID().Value()),
		vlog.F("field_id", priceList.FieldID().Value()))

	// Create output
	output := &UpsertPriceListOutput{
		ID:        priceList.ID().Value(),
		BranchID:  priceList.BranchID().Value(),
		AccountID: priceList.AccountID().Value(),
		FieldID:   priceList.FieldID().Value(),
		StatusID:  priceList.StatusID().Value(),
		RoleID:    priceList.RoleID().Value(),
		Acronym:   priceList.Acronym(),
		CreatedAt: priceList.CreatedAt().Value(),
		UpdatedAt: priceList.UpdatedAt().Value(),
	}

	return output, nil
}

// UpsertPriceListsInput represents the input for batch upserting price list entries
type UpsertPriceListsInput struct {
	BranchID  string
	AccountID string
	RoleID    string
	Acronym   string
	Fields    []UpsertPriceListFieldInput
}

// UpsertPriceListFieldInput represents a field with its status for batch operations
type UpsertPriceListFieldInput struct {
	FieldID  string
	StatusID string
}

// UpsertPriceListsOutput represents the output of batch upserting price list entries
type UpsertPriceListsOutput struct {
	ProcessedCount int
	SuccessCount   int
	FailureCount   int
	Results        []UpsertPriceListResult
}

// UpsertPriceListResult represents the result of a single upsert operation
type UpsertPriceListResult struct {
	FieldID   string
	StatusID  string
	Success   bool
	Error     string
	PriceList *UpsertPriceListOutput
}

// UpsertPriceListsUsecase defines the interface for batch price list upsert operations
type UpsertPriceListsUsecase interface {
	Execute(ctx context.Context, input *UpsertPriceListsInput) (*UpsertPriceListsOutput, error)
}

// upsertPriceListsUsecase implements UpsertPriceListsUsecase
type upsertPriceListsUsecase struct {
	upsertUsecase UpsertPriceListUsecase
}

// NewUpsertPriceListsUsecase creates a new batch upsert price lists use case
func NewUpsertPriceListsUsecase(upsertUsecase UpsertPriceListUsecase) UpsertPriceListsUsecase {
	return &upsertPriceListsUsecase{
		upsertUsecase: upsertUsecase,
	}
}

// Execute performs batch upsert operations for multiple price list entries
func (u *upsertPriceListsUsecase) Execute(ctx context.Context, input *UpsertPriceListsInput) (*UpsertPriceListsOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", "UpsertPriceListsUsecase"))

	if input == nil {
		logger.Error("input is required")
		return nil, core.NewBusinessError("input is required")
	}

	if len(input.Fields) == 0 {
		logger.Error("at least one field is required")
		return nil, core.NewBusinessError("at least one field is required")
	}

	logger.Info("starting batch price list upsert operation",
		vlog.F("branch_id", input.BranchID),
		vlog.F("account_id", input.AccountID),
		vlog.F("role_id", input.RoleID),
		vlog.F("fields_count", len(input.Fields)))

	output := &UpsertPriceListsOutput{
		ProcessedCount: len(input.Fields),
		Results:        make([]UpsertPriceListResult, 0, len(input.Fields)),
	}

	// Process each field
	for _, field := range input.Fields {
		result := UpsertPriceListResult{
			FieldID:  field.FieldID,
			StatusID: field.StatusID,
		}

		// Create upsert input for individual field
		upsertInput := &UpsertPriceListInput{
			BranchID:  input.BranchID,
			AccountID: input.AccountID,
			FieldID:   field.FieldID,
			StatusID:  field.StatusID,
			RoleID:    input.RoleID,
			Acronym:   input.Acronym,
		}

		// Execute upsert for this field
		priceListOutput, err := u.upsertUsecase.Execute(ctx, upsertInput)
		if err != nil {
			logger.Error("failed to upsert price list for field",
				vlog.F("field_id", field.FieldID),
				vlog.F("error", err))
			
			result.Success = false
			result.Error = err.Error()
			output.FailureCount++
		} else {
			logger.Debug("successfully upserted price list for field",
				vlog.F("field_id", field.FieldID),
				vlog.F("price_list_id", priceListOutput.ID))
			
			result.Success = true
			result.PriceList = priceListOutput
			output.SuccessCount++
		}

		output.Results = append(output.Results, result)
	}

	logger.Info("batch price list upsert operation completed",
		vlog.F("processed_count", output.ProcessedCount),
		vlog.F("success_count", output.SuccessCount),
		vlog.F("failure_count", output.FailureCount))

	return output, nil
}
