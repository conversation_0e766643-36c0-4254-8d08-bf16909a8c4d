package event

import (
	"time"
)

// NatureEventData represents the data structure for nature events
type NatureEventData struct {
	ID          *string `json:"id,omitempty"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Icon        string  `json:"icon"`
	Enabled     bool    `json:"enabled"`
}

// NatureEventMessage represents the event message structure for nature operations
type NatureEventMessage struct {
	Data      NatureEventData `json:"data"`
	Timestamp time.Time       `json:"timestamp"`
	Source    string          `json:"source"`
	EventType string          `json:"event_type"`
}
