package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertNatureUsecase defines the interface for nature upsert operations
type UpsertNatureUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertNatureInput) (*usecase.UpsertNatureOutput, error)
}

// NatureUpsertEventProcessor processes nature upsert events
type NatureUpsertEventProcessor struct {
	upsertUsecase UpsertNatureUsecase
}

// NewNatureUpsertEventProcessor creates a new nature upsert event processor
func NewNatureUpsertEventProcessor(upsertUsecase UpsertNatureUsecase) *NatureUpsertEventProcessor {
	return &NatureUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// ProcessMessage processes a nature event message
func (p *NatureUpsertEventProcessor) ProcessMessage(ctx context.Context, message *NatureEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "NatureUpsertEventProcessor"))
	logger.Debug("handling nature event message", vlog.F("event_type", message.EventType))

	// Validate message data using core functions
	if message.Data.Name == "" {
		logger.Error("validation failed", vlog.F("error", "nature name is required"))
		return core.NewBusinessError("nature name is required in message data")
	}
	if message.Data.Description == "" {
		logger.Error("validation failed", vlog.F("error", "nature description is required"))
		return core.NewBusinessError("nature description is required in message data")
	}
	if message.Data.Icon == "" {
		logger.Error("validation failed", vlog.F("error", "nature icon is required"))
		return core.NewBusinessError("nature icon is required in message data")
	}

	// Validate ID format if provided
	if message.Data.ID != nil && *message.Data.ID != "" {
		if _, err := core.NewIDFromString(*message.Data.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.Data.ID))
			return core.NewBusinessError("invalid UUID format in message data: %v", err)
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertNatureInput{
		Name:        message.Data.Name,
		Description: message.Data.Description,
		Icon:        message.Data.Icon,
		Enabled:     message.Data.Enabled,
	}

	if message.Data.ID != nil {
		upsertInput.ID = message.Data.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		logger.Error("failed to process nature upsert from message", vlog.F("error", err))
		return fmt.Errorf("failed to process nature upsert from message: %w", err)
	}

	logger.Info("successfully processed nature upsert", vlog.F("name", message.Data.Name))
	return nil
}
