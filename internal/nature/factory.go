package nature

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/service"

	"gitlab.viswalslab.com/backend/price-list/internal/nature/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
)

type Factory struct {
	NatureEventListener *transport.NatureEventListener
}

func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewNatureValidationService()

	// Initialize repository
	natureRepo := repository.NewNatureRepo(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertNatureUsecase(natureRepo, validationService)

	// Initialize event listener
	eventListener, err := transport.NewNatureEventListener(cfg, upsertUsecase)
	if err != nil {
		return nil, err
	}

	return &Factory{
		NatureEventListener: eventListener,
	}, nil
}
