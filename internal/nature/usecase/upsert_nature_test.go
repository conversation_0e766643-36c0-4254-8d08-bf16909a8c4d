package usecase_test

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/usecase"
	repository_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/nature/repository"
	service_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/nature/service"
)

func TestUpsertNatureUsecase_Execute(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name           string
		input          *usecase.UpsertNatureInput
		setupMocks     func(*repository_mocks.MockNatureRepository, *service_mocks.MockNatureValidationService)
		wantErr        bool
		wantErrMessage string
		checkResult    func(*testing.T, *usecase.UpsertNatureOutput)
	}{
		{
			name: "Successful upsert with existing ID",
			input: &usecase.UpsertNatureInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			setupMocks: func(repo *repository_mocks.MockNatureRepository, validation *service_mocks.MockNatureValidationService) {
				// Validation service succeeds
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)

				// Repository upsert succeeds
				repo.On("UpsertNature", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)
			},
			wantErr: false,
			checkResult: func(t *testing.T, output *usecase.UpsertNatureOutput) {
				assert.Equal(t, validID.Value(), output.ID)
				assert.Equal(t, "Cardiology", output.Name)
				assert.Equal(t, "Heart-related medical procedures", output.Description)
				assert.Equal(t, "heart-icon.png", output.Icon)
				assert.Equal(t, true, output.Enabled)
			},
		},
		{
			name: "Successful upsert without ID (new record)",
			input: &usecase.UpsertNatureInput{
				ID:          nil,
				Name:        "Dermatology",
				Description: "Skin-related medical procedures",
				Icon:        "skin-icon.png",
				Enabled:     true,
			},
			setupMocks: func(repo *repository_mocks.MockNatureRepository, validation *service_mocks.MockNatureValidationService) {
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)
				repo.On("UpsertNature", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)
			},
			wantErr: false,
			checkResult: func(t *testing.T, output *usecase.UpsertNatureOutput) {
				assert.NotEmpty(t, output.ID) // New ID should be generated
				assert.Equal(t, "Dermatology", output.Name)
				assert.Equal(t, "Skin-related medical procedures", output.Description)
				assert.Equal(t, "skin-icon.png", output.Icon)
				assert.Equal(t, true, output.Enabled)
			},
		},
		{
			name: "Validation error - empty name",
			input: &usecase.UpsertNatureInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "",
				Description: "Description",
				Icon:        "icon.png",
				Enabled:     true,
			},
			setupMocks: func(repo *repository_mocks.MockNatureRepository, validation *service_mocks.MockNatureValidationService) {
				// No mocks needed - validateInput is called internally and will return error
			},
			wantErr:        true,
			wantErrMessage: "name is required",
		},
		{
			name: "Validation error - invalid ID format",
			input: &usecase.UpsertNatureInput{
				ID:          core.ToPtr("invalid-id"),
				Name:        "Valid Name",
				Description: "Valid Description",
				Icon:        "valid-icon.png",
				Enabled:     true,
			},
			setupMocks: func(repo *repository_mocks.MockNatureRepository, validation *service_mocks.MockNatureValidationService) {
				// No mocks needed - validateInput is called internally and will return error
			},
			wantErr:        true,
			wantErrMessage: "invalid UUID format",
		},
		{
			name: "Repository error",
			input: &usecase.UpsertNatureInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			setupMocks: func(repo *repository_mocks.MockNatureRepository, validation *service_mocks.MockNatureValidationService) {
				validation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
				validation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)
				repo.On("UpsertNature", mock.Anything, mock.AnythingOfType("*entity.Nature")).
					Return(errors.New("database connection failed"))
			},
			wantErr:        true,
			wantErrMessage: "database connection failed",
		},
		{
			name:  "Nil input",
			input: nil,
			setupMocks: func(repo *repository_mocks.MockNatureRepository, validation *service_mocks.MockNatureValidationService) {
				// No mocks should be called
			},
			wantErr:        true,
			wantErrMessage: "input is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			mockRepo := new(repository_mocks.MockNatureRepository)
			mockValidation := new(service_mocks.MockNatureValidationService)

			// Setup mocks
			tt.setupMocks(mockRepo, mockValidation)

			// Create usecase
			uc := usecase.NewUpsertNatureUsecase(mockRepo, mockValidation)

			// Execute
			ctx := context.Background()
			result, err := uc.Execute(ctx, tt.input)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.checkResult != nil {
					tt.checkResult(t, result)
				}
			}

			// Verify all expectations were met
			mockRepo.AssertExpectations(t)
			mockValidation.AssertExpectations(t)
		})
	}
}

func TestUpsertNatureUsecase_Execute_DisabledNature(t *testing.T) {
	validID := core.NewID()

	input := &usecase.UpsertNatureInput{
		ID:          core.ToPtr(validID.Value()),
		Name:        "Disabled Nature",
		Description: "This nature is disabled",
		Icon:        "disabled-icon.png",
		Enabled:     false,
	}

	mockRepo := new(repository_mocks.MockNatureRepository)
	mockValidation := new(service_mocks.MockNatureValidationService)

	// Setup mocks
	mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
	mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)
	mockRepo.On("UpsertNature", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)

	// Create usecase
	uc := usecase.NewUpsertNatureUsecase(mockRepo, mockValidation)

	// Execute
	ctx := context.Background()
	result, err := uc.Execute(ctx, input)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, false, result.Enabled)

	// Verify expectations
	mockRepo.AssertExpectations(t)
	mockValidation.AssertExpectations(t)
}

func TestUpsertNatureUsecase_Execute_EmptyStringID(t *testing.T) {
	emptyID := ""

	input := &usecase.UpsertNatureInput{
		ID:          &emptyID,
		Name:        "Nature with Empty ID",
		Description: "This should generate a new ID",
		Icon:        "new-icon.png",
		Enabled:     true,
	}

	mockRepo := new(repository_mocks.MockNatureRepository)
	mockValidation := new(service_mocks.MockNatureValidationService)

	// Setup mocks
	mockValidation.On("ValidateUpsertInput", mock.Anything, mock.AnythingOfType("*service.UpsertValidationInput")).Return(nil)
	mockValidation.On("ValidateBusinessRules", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)
	mockRepo.On("UpsertNature", mock.Anything, mock.AnythingOfType("*entity.Nature")).Return(nil)

	// Create usecase
	uc := usecase.NewUpsertNatureUsecase(mockRepo, mockValidation)

	// Execute
	ctx := context.Background()
	result, err := uc.Execute(ctx, input)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.NotEmpty(t, result.ID)     // Should generate new ID
	assert.NotEqual(t, "", result.ID) // Should not be empty string

	// Verify expectations
	mockRepo.AssertExpectations(t)
	mockValidation.AssertExpectations(t)
}
