package repository

import (
	"context"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
)

// NatureNameExistsError represents a domain error when nature name already exists
type NatureNameExistsError struct {
	Name string
}

func (e *NatureNameExistsError) Error() string {
	return fmt.Sprintf("nature name '%s' already exists", e.Name)
}

// UpsertNature upserts a nature entity into the database
func (r *NatureRepo) UpsertNature(ctx context.Context, nature *entity.Nature) error {
	query := `
		INSERT INTO public.nature (uuid, name, description, icon, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
		ON CONFLICT (uuid) DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			enabled = EXCLUDED.enabled,
			updated_at = NOW()
	`

	_, err := r.db.ExecContext(ctx, query,
		nature.ID(),
		nature.Name(),
		nature.Description(),
		nature.Icon(),
		nature.Enabled(),
	)

	if err != nil {
		// Check for name constraint violation
		if strings.Contains(err.Error(), "nature_name_key") {
			return &NatureNameExistsError{Name: nature.Name()}
		}
		return err
	}

	return nil
}
