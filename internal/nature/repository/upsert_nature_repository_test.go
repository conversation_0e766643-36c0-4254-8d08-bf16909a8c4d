package repository_test

import (
	"context"
	"database/sql"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/repository"
	sql_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/nature/sql"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	return sql_mocks.SetupTestDB(t)
}

func createTestNature(t *testing.T) *entity.Nature {
	validID := core.NewID()
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Cardiology",
		Description: "Heart-related medical procedures",
		Icon:        "heart-icon.png",
		Enabled:     true,
	})
	assert.NoError(t, err)
	return nature
}

func TestNatureRepo_UpsertNature_Insert(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)
	nature := createTestNature(t)

	// Expect the upsert query to be executed
	expectedQuery := `
		INSERT INTO public.nature \(uuid, name, description, icon, enabled, created_at, updated_at\)
		VALUES \(\$1, \$2, \$3, \$4, \$5, NOW\(\), NOW\(\)\)
		ON CONFLICT \(uuid\) DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			enabled = EXCLUDED.enabled,
			updated_at = NOW\(\)`

	mock.ExpectExec(expectedQuery).
		WithArgs(
			nature.ID(),
			nature.Name(),
			nature.Description(),
			nature.Icon(),
			nature.Enabled(),
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Execute
	ctx := context.Background()
	err := repo.UpsertNature(ctx, nature)

	// Assertions
	assert.NoError(t, err)

	// Verify all expectations were met
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestNatureRepo_UpsertNature_Update(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)
	nature := createTestNature(t)

	// Expect the upsert query to be executed (simulating an update)
	expectedQuery := `
		INSERT INTO public.nature \(uuid, name, description, icon, enabled, created_at, updated_at\)
		VALUES \(\$1, \$2, \$3, \$4, \$5, NOW\(\), NOW\(\)\)
		ON CONFLICT \(uuid\) DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			enabled = EXCLUDED.enabled,
			updated_at = NOW\(\)`

	mock.ExpectExec(expectedQuery).
		WithArgs(
			nature.ID(),
			nature.Name(),
			nature.Description(),
			nature.Icon(),
			nature.Enabled(),
		).
		WillReturnResult(sqlmock.NewResult(0, 1)) // 0 for last insert id (update), 1 row affected

	// Execute
	ctx := context.Background()
	err := repo.UpsertNature(ctx, nature)

	// Assertions
	assert.NoError(t, err)

	// Verify all expectations were met
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestNatureRepo_UpsertNature_DatabaseError(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)
	nature := createTestNature(t)

	// Expect the query to fail
	expectedQuery := `
		INSERT INTO public.nature \(uuid, name, description, icon, enabled, created_at, updated_at\)
		VALUES \(\$1, \$2, \$3, \$4, \$5, NOW\(\), NOW\(\)\)
		ON CONFLICT \(uuid\) DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			enabled = EXCLUDED.enabled,
			updated_at = NOW\(\)`

	mock.ExpectExec(expectedQuery).
		WithArgs(
			nature.ID(),
			nature.Name(),
			nature.Description(),
			nature.Icon(),
			nature.Enabled(),
		).
		WillReturnError(sql.ErrConnDone)

	// Execute
	ctx := context.Background()
	err := repo.UpsertNature(ctx, nature)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, sql.ErrConnDone, err)

	// Verify all expectations were met
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestNatureRepo_UpsertNature_DisabledNature(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)

	// Create disabled nature
	validID := core.NewID()
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Disabled Nature",
		Description: "This nature is disabled",
		Icon:        "disabled-icon.png",
		Enabled:     false,
	})
	assert.NoError(t, err)

	// Expect the upsert query with enabled=false
	expectedQuery := `
		INSERT INTO public.nature \(uuid, name, description, icon, enabled, created_at, updated_at\)
		VALUES \(\$1, \$2, \$3, \$4, \$5, NOW\(\), NOW\(\)\)
		ON CONFLICT \(uuid\) DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			enabled = EXCLUDED.enabled,
			updated_at = NOW\(\)`

	mock.ExpectExec(expectedQuery).
		WithArgs(
			nature.ID(),
			nature.Name(),
			nature.Description(),
			nature.Icon(),
			false, // enabled=false
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Execute
	ctx := context.Background()
	err = repo.UpsertNature(ctx, nature)

	// Assertions
	assert.NoError(t, err)

	// Verify all expectations were met
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestNatureRepo_UpsertNature_ContextCancellation(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)
	nature := createTestNature(t)

	// Create a cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel immediately

	// Execute
	err := repo.UpsertNature(ctx, nature)

	// Assertions - context.Canceled should be returned
	assert.Error(t, err)
	// Note: The specific error might vary based on how sqlx handles cancelled contexts
}

func TestNatureRepo_UpsertNature_SpecialCharacters(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)

	// Create nature with special characters
	validID := core.NewID()
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Cardiology & Surgery",
		Description: "Heart-related procedures with 'advanced' techniques",
		Icon:        "heart-icon_v2.png",
		Enabled:     true,
	})
	assert.NoError(t, err)

	// Expect the upsert query
	expectedQuery := `
		INSERT INTO public.nature \(uuid, name, description, icon, enabled, created_at, updated_at\)
		VALUES \(\$1, \$2, \$3, \$4, \$5, NOW\(\), NOW\(\)\)
		ON CONFLICT \(uuid\) DO UPDATE SET
			name = EXCLUDED.name,
			description = EXCLUDED.description,
			icon = EXCLUDED.icon,
			enabled = EXCLUDED.enabled,
			updated_at = NOW\(\)`

	mock.ExpectExec(expectedQuery).
		WithArgs(
			nature.ID(),
			"Cardiology & Surgery",
			"Heart-related procedures with 'advanced' techniques",
			"heart-icon_v2.png",
			true,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// Execute
	ctx := context.Background()
	err = repo.UpsertNature(ctx, nature)

	// Assertions
	assert.NoError(t, err)

	// Verify all expectations were met
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestNatureRepo_UpsertNature_NameConstraintViolation(t *testing.T) {
	// Setup
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)

	validID := core.NewID()
	nature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Existing Name",
		Description: "Test Description",
		Icon:        "test-icon.png",
		Enabled:     true,
	})
	assert.NoError(t, err)

	// Mock the constraint violation error
	mock.ExpectExec("INSERT INTO public.nature").
		WithArgs(
			validID.Value(),
			"Existing Name",
			"Test Description",
			"test-icon.png",
			true,
		).
		WillReturnError(errors.New(`duplicate key value violates unique constraint "nature_name_key"`))

	// Execute
	ctx := context.Background()
	err = repo.UpsertNature(ctx, nature)

	// Assertions
	assert.Error(t, err)

	var nameExistsErr *repository.NatureNameExistsError
	assert.True(t, errors.As(err, &nameExistsErr))
	assert.Equal(t, "Existing Name", nameExistsErr.Name)
	assert.Contains(t, err.Error(), "nature name 'Existing Name' already exists")
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestNewNatureRepo(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewNatureRepo(db)

	assert.NotNil(t, repo)
	// Test that the repository can be used as the interface type
	var _ interface {
		UpsertNature(ctx context.Context, nature *entity.Nature) error
	} = repo
}
