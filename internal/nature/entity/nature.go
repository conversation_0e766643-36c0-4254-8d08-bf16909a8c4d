package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// Nature represents the nature of appointment entity
type Nature struct {
	id          core.Identifier
	name        string
	description string
	icon        string
	enabled     bool
	createdAt   core.Timestamp
	updatedAt   core.Timestamp
}

type NewNatureInput struct {
	ID          string
	Name        string
	Description string
	Icon        string
	Enabled     bool
	CreatedAt   *time.Time
	UpdatedAt   *time.Time
}

func NewNature(input *NewNatureInput) (*Nature, error) {
	id, err := core.NewIDFromString(input.ID)
	if err != nil {
		return nil, err
	}

	// Use provided timestamps or default to current time using core timestamp
	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdAt, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, err
	}

	updatedAt, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, err
	}

	return &Nature{
		id:          *id,
		name:        input.Name,
		description: input.Description,
		icon:        input.Icon,
		enabled:     input.Enabled,
		createdAt:   *createdAt,
		updatedAt:   *updatedAt,
	}, nil
}

// Getter methods following country pattern
func (n Nature) ID() string           { return n.id.Value() }
func (n Nature) Name() string         { return n.name }
func (n Nature) Description() string  { return n.description }
func (n Nature) Icon() string         { return n.icon }
func (n Nature) Enabled() bool        { return n.enabled }
func (n Nature) CreatedAt() time.Time { return n.createdAt.Value() }
func (n Nature) UpdatedAt() time.Time { return n.updatedAt.Value() }
