package entity_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
)

var validID = core.NewID()

func TestNewNature(t *testing.T) {
	tests := []struct {
		name    string
		input   *entity.NewNatureInput
		wantErr bool
	}{
		{
			name: "Valid input with all fields",
			input: &entity.NewNatureInput{
				ID:          validID.Value(),
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
				CreatedAt:   core.ToPtr(time.Now()),
				UpdatedAt:   core.ToPtr(time.Now()),
			},
			wantErr: false,
		},
		{
			name: "Valid input without timestamps",
			input: &entity.NewNatureInput{
				ID:          validID.Value(),
				Name:        "Dermatology",
				Description: "Skin-related medical procedures",
				Icon:        "skin-icon.png",
				Enabled:     true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with disabled nature",
			input: &entity.NewNatureInput{
				ID:          validID.Value(),
				Name:        "Test Nature",
				Description: "Test description",
				Icon:        "test-icon.png",
				Enabled:     false,
			},
			wantErr: false,
		},
		{
			name: "Invalid ID format",
			input: &entity.NewNatureInput{
				ID:          "invalid-id-format",
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			wantErr: true,
		},
		{
			name: "Empty ID",
			input: &entity.NewNatureInput{
				ID:          "",
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			wantErr: true,
		},
		{
			name: "Empty name",
			input: &entity.NewNatureInput{
				ID:          validID.Value(),
				Name:        "",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			wantErr: false, // Name validation happens at business layer, not entity
		},
		{
			name: "Empty description",
			input: &entity.NewNatureInput{
				ID:          validID.Value(),
				Name:        "Cardiology",
				Description: "",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			wantErr: false, // Description validation happens at business layer, not entity
		},
		{
			name: "Empty icon",
			input: &entity.NewNatureInput{
				ID:          validID.Value(),
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "",
				Enabled:     true,
			},
			wantErr: false, // Icon validation happens at business layer, not entity
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nature, err := entity.NewNature(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, nature)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, nature)

				// Verify all getter methods work correctly
				assert.Equal(t, tt.input.ID, nature.ID())
				assert.Equal(t, tt.input.Name, nature.Name())
				assert.Equal(t, tt.input.Description, nature.Description())
				assert.Equal(t, tt.input.Icon, nature.Icon())
				assert.Equal(t, tt.input.Enabled, nature.Enabled())

				// Verify timestamps are set (either from input or default)
				assert.NotZero(t, nature.CreatedAt())
				assert.NotZero(t, nature.UpdatedAt())

				if tt.input.CreatedAt != nil {
					assert.Equal(t, tt.input.CreatedAt.UTC(), nature.CreatedAt().UTC())
				}
				if tt.input.UpdatedAt != nil {
					assert.Equal(t, tt.input.UpdatedAt.UTC(), nature.UpdatedAt().UTC())
				}
			}
		})
	}
}

func TestNatureGetters(t *testing.T) {
	now := time.Now()
	input := &entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Test Nature",
		Description: "Test description",
		Icon:        "test-icon.png",
		Enabled:     true,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	}

	nature, err := entity.NewNature(input)
	assert.NoError(t, err)
	assert.NotNil(t, nature)

	// Test all getter methods
	assert.Equal(t, input.ID, nature.ID())
	assert.Equal(t, input.Name, nature.Name())
	assert.Equal(t, input.Description, nature.Description())
	assert.Equal(t, input.Icon, nature.Icon())
	assert.Equal(t, input.Enabled, nature.Enabled())
	assert.Equal(t, now.UTC(), nature.CreatedAt().UTC())
	assert.Equal(t, now.UTC(), nature.UpdatedAt().UTC())
}

func TestNatureWithDifferentTimestamps(t *testing.T) {
	createdTime := time.Now().Add(-time.Hour)
	updatedTime := time.Now()

	input := &entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Nature with Different Timestamps",
		Description: "Test description",
		Icon:        "test-icon.png",
		Enabled:     true,
		CreatedAt:   &createdTime,
		UpdatedAt:   &updatedTime,
	}

	nature, err := entity.NewNature(input)
	assert.NoError(t, err)
	assert.NotNil(t, nature)

	// Verify timestamps are preserved correctly
	assert.Equal(t, createdTime.UTC(), nature.CreatedAt().UTC())
	assert.Equal(t, updatedTime.UTC(), nature.UpdatedAt().UTC())
	assert.True(t, nature.CreatedAt().Before(nature.UpdatedAt()))
}

func TestNatureWithNilTimestamps(t *testing.T) {
	input := &entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Nature with Nil Timestamps",
		Description: "Test description",
		Icon:        "test-icon.png",
		Enabled:     true,
		CreatedAt:   nil,
		UpdatedAt:   nil,
	}

	beforeCreate := time.Now()
	nature, err := entity.NewNature(input)
	afterCreate := time.Now()

	assert.NoError(t, err)
	assert.NotNil(t, nature)

	// Verify default timestamps are set to current time
	assert.True(t, nature.CreatedAt().After(beforeCreate) || nature.CreatedAt().Equal(beforeCreate))
	assert.True(t, nature.CreatedAt().Before(afterCreate) || nature.CreatedAt().Equal(afterCreate))
	assert.True(t, nature.UpdatedAt().After(beforeCreate) || nature.UpdatedAt().Equal(beforeCreate))
	assert.True(t, nature.UpdatedAt().Before(afterCreate) || nature.UpdatedAt().Equal(afterCreate))
}
