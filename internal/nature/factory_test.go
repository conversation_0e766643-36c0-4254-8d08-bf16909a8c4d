package nature_test

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/nature"
	sql_mocks "gitlab.viswalslab.com/backend/price-list/mocks/internal_mock/nature/sql"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	return sql_mocks.SetupTestDB(t)
}

func TestNewFactory(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLNature: "https://sqs.us-east-1.amazonaws.com/123456789/nature-queue",
			Region:            "us-east-1",
			AccessKeyID:       "test-access-key",
			SecretAccessKey:   "test-secret-key",
		},
	}

	factory, err := nature.NewFactory(db, cfg)

	assert.NoError(t, err)
	assert.NotNil(t, factory)
	assert.NotNil(t, factory.NatureEventListener)
}

func TestNewFactory_EmptyConfig(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			// Empty AWS config
		},
	}

	factory, err := nature.NewFactory(db, cfg)

	// Should fail gracefully with empty config
	// The factory should handle empty config gracefully
	if err != nil {
		assert.Error(t, err)
		assert.Nil(t, factory)
	} else {
		assert.NotNil(t, factory)
	}
}

func TestNewFactory_InvalidAWSConfig(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLNature: "invalid-url",
			Region:            "",
			AccessKeyID:       "",
			SecretAccessKey:   "",
		},
	}

	// This should fail due to invalid AWS configuration
	factory, err := nature.NewFactory(db, cfg)

	// Should fail with invalid config
	assert.Error(t, err)
	assert.Nil(t, factory)
}

func TestFactory_Structure(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory structure test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLNature: "https://sqs.us-east-1.amazonaws.com/123456789/nature-queue",
			Region:            "us-east-1",
			AccessKeyID:       "test-access-key",
			SecretAccessKey:   "test-secret-key",
		},
	}

	factory, err := nature.NewFactory(db, cfg)
	assert.NoError(t, err)

	// Verify factory structure
	assert.NotNil(t, factory)

	// Test that all required services are available
	assert.NotNil(t, factory.NatureEventListener)
}
