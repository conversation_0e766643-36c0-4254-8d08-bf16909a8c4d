package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/service"
)

func TestNatureValidationService_ValidateNameAndUUID(t *testing.T) {
	validationService := service.NewNatureValidationService()
	ctx := context.Background()
	validID := core.NewID()

	tests := []struct {
		name    string
		nameVal string
		uuid    string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "Valid name and UUID",
			nameVal: "Cardiology",
			uuid:    validID.Value(),
			wantErr: false,
		},
		{
			name:    "Valid name with empty UUID",
			nameVal: "Dermatology",
			uuid:    "",
			wantErr: false,
		},
		{
			name:    "Empty name",
			nameVal: "",
			uuid:    validID.Value(),
			wantErr: true,
			errMsg:  "nature name is required",
		},
		{
			name:    "Invalid UUID format",
			nameVal: "Cardiology",
			uuid:    "invalid-uuid-format",
			wantErr: true,
			errMsg:  "invalid uuid format",
		},
		{
			name:    "Empty name and invalid UUID",
			nameVal: "",
			uuid:    "invalid-uuid",
			wantErr: true,
			errMsg:  "nature name is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validationService.ValidateNameAndUUID(ctx, tt.nameVal, tt.uuid)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNatureValidationService_ValidateBusinessRules(t *testing.T) {
	validationService := service.NewNatureValidationService()
	ctx := context.Background()
	validID := core.NewID()

	// Create a valid nature entity for testing
	validNature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Cardiology",
		Description: "Heart-related medical procedures",
		Icon:        "heart-icon.png",
		Enabled:     true,
	})
	assert.NoError(t, err)

	// Create nature entities with missing fields
	emptyNameNature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "",
		Description: "Description",
		Icon:        "icon.png",
		Enabled:     true,
	})
	assert.NoError(t, err)

	emptyDescNature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Name",
		Description: "",
		Icon:        "icon.png",
		Enabled:     true,
	})
	assert.NoError(t, err)

	emptyIconNature, err := entity.NewNature(&entity.NewNatureInput{
		ID:          validID.Value(),
		Name:        "Name",
		Description: "Description",
		Icon:        "",
		Enabled:     true,
	})
	assert.NoError(t, err)

	tests := []struct {
		name    string
		nature  *entity.Nature
		wantErr bool
		errMsg  string
	}{
		{
			name:    "Valid nature entity",
			nature:  validNature,
			wantErr: false,
		},
		{
			name:    "Nil nature entity",
			nature:  nil,
			wantErr: true,
			errMsg:  "nature entity is required",
		},
		{
			name:    "Empty name",
			nature:  emptyNameNature,
			wantErr: true,
			errMsg:  "nature name is required",
		},
		{
			name:    "Empty description",
			nature:  emptyDescNature,
			wantErr: true,
			errMsg:  "nature description is required",
		},
		{
			name:    "Empty icon",
			nature:  emptyIconNature,
			wantErr: true,
			errMsg:  "nature icon is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validationService.ValidateBusinessRules(ctx, tt.nature)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNatureValidationService_ValidateUpsertInput(t *testing.T) {
	validationService := service.NewNatureValidationService()
	ctx := context.Background()
	validID := core.NewID()

	tests := []struct {
		name    string
		input   *service.UpsertValidationInput
		wantErr bool
		errMsg  string
	}{
		{
			name: "Valid input with ID",
			input: &service.UpsertValidationInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Cardiology",
				Description: "Heart-related medical procedures",
				Icon:        "heart-icon.png",
				Enabled:     true,
			},
			wantErr: false,
		},
		{
			name: "Valid input without ID",
			input: &service.UpsertValidationInput{
				ID:          nil,
				Name:        "Dermatology",
				Description: "Skin-related medical procedures",
				Icon:        "skin-icon.png",
				Enabled:     true,
			},
			wantErr: false,
		},
		{
			name: "Empty name",
			input: &service.UpsertValidationInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "",
				Description: "Description",
				Icon:        "icon.png",
				Enabled:     true,
			},
			wantErr: true,
			errMsg:  "nature name is required",
		},
		{
			name: "Empty description",
			input: &service.UpsertValidationInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Name",
				Description: "",
				Icon:        "icon.png",
				Enabled:     true,
			},
			wantErr: true,
			errMsg:  "nature description is required",
		},
		{
			name: "Empty icon",
			input: &service.UpsertValidationInput{
				ID:          core.ToPtr(validID.Value()),
				Name:        "Name",
				Description: "Description",
				Icon:        "",
				Enabled:     true,
			},
			wantErr: true,
			errMsg:  "nature icon is required",
		},
		{
			name: "Invalid ID format",
			input: &service.UpsertValidationInput{
				ID:          core.ToPtr("invalid-id-format"),
				Name:        "Name",
				Description: "Description",
				Icon:        "icon.png",
				Enabled:     true,
			},
			wantErr: true,
			errMsg:  "invalid uuid format",
		},
		{
			name:    "Nil input",
			input:   nil,
			wantErr: true,
			errMsg:  "input is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validationService.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNatureValidationService_Interface(t *testing.T) {
	// Test that the concrete implementation satisfies the interface
	var _ service.NatureValidationService = service.NewNatureValidationService()
}
