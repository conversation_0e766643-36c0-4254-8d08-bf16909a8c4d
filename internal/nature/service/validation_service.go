package service

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/nature/entity"
)

// NatureValidationService provides validation operations for nature domain
type NatureValidationService interface {
	ValidateNameAndUUID(ctx context.Context, name string, uuid string) error
	ValidateBusinessRules(ctx context.Context, nature *entity.Nature) error
	ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error
}

// UpsertValidationInput contains data for upsert validation
type UpsertValidationInput struct {
	ID          *string
	Name        string
	Description string
	Icon        string
	Enabled     bool
}

type validationService struct{}

// NewNatureValidationService creates a new validation service
func NewNatureValidationService() NatureValidationService {
	return &validationService{}
}

// ValidateNameAndUUID validates name and uuid combination for business rules
func (s *validationService) ValidateNameAndUUID(ctx context.Context, name string, uuid string) error {
	// Validate name is not empty
	if name == "" {
		return core.NewBusinessError("nature name is required")
	}

	// Validate uuid format if provided using core validation
	if uuid != "" {
		if _, err := core.NewIDFromString(uuid); err != nil {
			return core.NewBusinessError("invalid uuid format: %v", err)
		}
	}

	return nil
}

// ValidateBusinessRules validates business-specific rules for nature entity
func (s *validationService) ValidateBusinessRules(ctx context.Context, nature *entity.Nature) error {
	if nature == nil {
		return core.NewBusinessError("nature entity is required")
	}

	if nature.Name() == "" {
		return core.NewBusinessError("nature name is required")
	}

	if nature.Description() == "" {
		return core.NewBusinessError("nature description is required")
	}

	if nature.Icon() == "" {
		return core.NewBusinessError("nature icon is required")
	}

	return nil
}

// ValidateUpsertInput validates input for upsert operations
func (s *validationService) ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error {
	if input == nil {
		return core.NewBusinessError("upsert input is required")
	}

	uuid := ""
	if input.ID != nil {
		uuid = *input.ID
	}

	if err := s.ValidateNameAndUUID(ctx, input.Name, uuid); err != nil {
		return err
	}

	if input.Description == "" {
		return core.NewBusinessError("nature description is required")
	}

	if input.Icon == "" {
		return core.NewBusinessError("nature icon is required")
	}

	return nil
}
