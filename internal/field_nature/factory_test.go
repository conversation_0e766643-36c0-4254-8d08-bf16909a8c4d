package field_nature

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewFieldHandlerFactory(t *testing.T) {
	t.Run("creates handler with all dependencies wired", func(t *testing.T) {
		db, _ := setupMockDB(t)
		handler := NewFieldHandlerFactory(db, vlog.NewWithLevel("error"))
		assert.NotNil(t, handler)
		assert.IsType(t, &transport.FieldHandler{}, handler)
		assert.NotNil(t, handler.Usecase)
	})

	t.Run("handler is ready for use", func(t *testing.T) {
		db, _ := setupMockDB(t)
		handler := NewFieldHandlerFactory(db, vlog.NewWithLevel("error"))
		assert.NotNil(t, handler)
		assert.IsType(t, &transport.FieldHandler{}, handler)
		assert.NotNil(t, handler.Usecase)
	})

	t.Run("multiple factory calls create independent instances", func(t *testing.T) {
		db, _ := setupMockDB(t)
		handler1 := NewFieldHandlerFactory(db, vlog.NewWithLevel("error"))
		handler2 := NewFieldHandlerFactory(db, vlog.NewWithLevel("error"))
		assert.NotNil(t, handler1)
		assert.NotNil(t, handler2)
		assert.NotSame(t, handler1, handler2)
		assert.NotSame(t, handler1.Usecase, handler2.Usecase)
	})
}
