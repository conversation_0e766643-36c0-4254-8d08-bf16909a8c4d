package usecase

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
)

type mockListFieldsRepo struct {
	GetAllFunc func(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error)
}

func (m *mockListFieldsRepo) GetByID(ctx context.Context, id string) (*entity.Field, error) {
	return nil, nil
}
func (m *mockListFieldsRepo) GetAll(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error) {
	return m.GetAllFunc(ctx, params)
}

func TestFieldUsecase_ListFieldsUsecase(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		repo := &mockListFieldsRepo{
			GetAllFunc: func(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error) {
				return []entity.Field{{Name: "Field 1"}}, 1, nil
			},
		}
		uc := &FieldUsecase{repo: repo}
		resp, pag, err := uc.ListFields(context.Background(), transport.ListFieldsRequest{})
		assert.NoError(t, err)
		assert.Len(t, resp, 1)
		assert.Equal(t, "Field 1", resp[0].Name)
		assert.NotNil(t, pag)
		assert.Equal(t, 1, pag.Total)
	})

	t.Run("error", func(t *testing.T) {
		repo := &mockListFieldsRepo{
			GetAllFunc: func(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error) {
				return nil, 0, errors.New("db error")
			},
		}
		uc := &FieldUsecase{repo: repo}
		resp, pag, err := uc.ListFields(context.Background(), transport.ListFieldsRequest{})
		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Nil(t, pag)
	})
}
