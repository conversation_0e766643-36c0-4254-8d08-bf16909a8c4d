package usecase

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// MockFieldRepository is a mock implementation of FieldRepository
type MockFieldRepository struct {
	mock.Mock
}

func (m *MockFieldRepository) GetAll(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error) {
	args := m.Called(ctx, params)
	return args.Get(0).([]entity.Field), args.Get(1).(int64), args.Error(2)
}

func (m *MockFieldRepository) GetByID(ctx context.Context, id string) (*entity.Field, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.Field), args.Error(1)
}

func TestNewFieldUsecase(t *testing.T) {
	mockRepo := new(MockFieldRepository)
	usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
	assert.NotNil(t, usecase)
	assert.Equal(t, mockRepo, usecase.repo)
}

func TestFieldUsecase_ListFields(t *testing.T) {
	ctx := context.Background()

	t.Run("successful list fields", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))

		params := transport.ListFieldsRequest{Page: 1, Size: 10}
		mockRepo.On("GetAll", ctx, params).Return([]entity.Field{{Name: "Field 1"}}, int64(1), nil)

		resp, pag, err := usecase.ListFields(ctx, params)
		assert.NoError(t, err)
		assert.Len(t, resp, 1)
		assert.Equal(t, "Field 1", resp[0].Name)
		assert.Equal(t, 1, pag.Total)
	})

	t.Run("error from repository", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))

		params := transport.ListFieldsRequest{Page: 1, Size: 10}
		mockRepo.On("GetAll", ctx, params).Return([]entity.Field{}, int64(0), errors.New("db error"))

		resp, pag, err := usecase.ListFields(ctx, params)
		assert.Error(t, err)
		assert.Nil(t, resp)
		assert.Nil(t, pag)
	})
}

func TestFieldUsecase_ListFields_Advanced(t *testing.T) {
	ctx := context.Background()

	t.Run("multiple fields", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		params := transport.ListFieldsRequest{Page: 1, Size: 10}
		fields := []entity.Field{
			{Name: "Field 1", Enabled: true},
			{Name: "Field 2", Enabled: false},
		}
		mockRepo.On("GetAll", ctx, params).Return(fields, int64(2), nil)
		resp, pag, err := usecase.ListFields(ctx, params)
		assert.NoError(t, err)
		assert.Len(t, resp, 2)
		assert.Equal(t, "Field 1", resp[0].Name)
		assert.Equal(t, "Field 2", resp[1].Name)
		assert.Equal(t, 2, pag.Total)
	})

	t.Run("empty result", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		params := transport.ListFieldsRequest{Page: 0, Size: 10}
		mockRepo.On("GetAll", ctx, params).Return([]entity.Field{}, int64(0), nil)
		resp, pag, err := usecase.ListFields(ctx, params)
		assert.NoError(t, err)
		assert.Empty(t, resp)
		assert.NotNil(t, pag)
		assert.Equal(t, 0, pag.CurrentPage)
		assert.Equal(t, 0, pag.Total)
	})

	t.Run("negative page number", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		params := transport.ListFieldsRequest{Page: -1, Size: 10}
		fields := []entity.Field{{Name: "Field 1"}}
		mockRepo.On("GetAll", ctx, params).Return(fields, int64(1), nil)
		resp, pag, err := usecase.ListFields(ctx, params)
		assert.NoError(t, err)
		assert.Len(t, resp, 1)
		assert.NotNil(t, pag)
		assert.Equal(t, 0, pag.CurrentPage)
		assert.Equal(t, 1, pag.Total)
	})

	t.Run("repository error", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		params := transport.ListFieldsRequest{Page: 0, Size: 10}
		expectedError := errors.New("database connection error")
		mockRepo.On("GetAll", ctx, params).Return([]entity.Field{}, int64(0), expectedError)
		resp, pag, err := usecase.ListFields(ctx, params)
		assert.Error(t, err)
		assert.Equal(t, expectedError, err)
		assert.Nil(t, resp)
		assert.Nil(t, pag)
	})

	t.Run("with filters", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		enabled := true
		name := "Field 1"
		params := transport.ListFieldsRequest{
			Page:      0,
			Size:      10,
			Enabled:   &enabled,
			Name:      &name,
			OrderBy:   "name",
			Direction: "ASC",
		}
		fields := []entity.Field{{Name: "Field 1", Enabled: true}}
		mockRepo.On("GetAll", ctx, params).Return(fields, int64(1), nil)
		resp, pag, err := usecase.ListFields(ctx, params)
		assert.NoError(t, err)
		assert.Len(t, resp, 1)
		assert.Equal(t, "Field 1", resp[0].Name)
		assert.Equal(t, 0, pag.CurrentPage)
		assert.Equal(t, 1, pag.Total)
	})
}

func TestFieldUsecase_GetField(t *testing.T) {
	ctx := context.Background()

	t.Run("successful get field", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))

		mockRepo.On("GetByID", ctx, "some-id").Return(&entity.Field{UUID: entity.Field{}.UUID, Name: "Test Field"}, nil)

		resp, err := usecase.GetField(ctx, "some-id")
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "Test Field", resp.Name)
	})

	t.Run("error from repository", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))

		mockRepo.On("GetByID", ctx, "some-id").Return(nil, errors.New("not found"))

		resp, err := usecase.GetField(ctx, "some-id")
		assert.Error(t, err)
		assert.Nil(t, resp)
	})
}

func TestFieldUsecase_GetField_Advanced(t *testing.T) {
	ctx := context.Background()

	t.Run("field not found", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		mockRepo.On("GetByID", ctx, "non-existent-id").Return(nil, errors.New("field not found"))
		resp, err := usecase.GetField(ctx, "non-existent-id")
		assert.Error(t, err)
		assert.Nil(t, resp)
	})

	t.Run("repository error", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		mockRepo.On("GetByID", ctx, "some-id").Return(nil, errors.New("database connection error"))
		resp, err := usecase.GetField(ctx, "some-id")
		assert.Error(t, err)
		assert.Nil(t, resp)
	})

	t.Run("disabled field", func(t *testing.T) {
		mockRepo := new(MockFieldRepository)
		usecase := NewFieldUsecase(mockRepo, vlog.NewWithLevel("error"))
		mockRepo.On("GetByID", ctx, "disabled-id").Return(&entity.Field{UUID: entity.Field{}.UUID, Name: "Disabled Field", Enabled: false}, nil)
		resp, err := usecase.GetField(ctx, "disabled-id")
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "Disabled Field", resp.Name)
		assert.False(t, resp.Enabled)
	})
}
