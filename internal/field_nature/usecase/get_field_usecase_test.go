package usecase

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
)

type mockGetFieldRepo struct {
	GetByIDFunc func(ctx context.Context, id string) (*entity.Field, error)
}

func (m *mockGetFieldRepo) GetByID(ctx context.Context, id string) (*entity.Field, error) {
	return m.GetByIDFunc(ctx, id)
}
func (m *mockGetFieldRepo) GetAll(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error) {
	return nil, 0, nil
}

func TestFieldUsecase_GetFieldUsecase(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		repo := &mockGetFieldRepo{
			GetByIDFunc: func(ctx context.Context, id string) (*entity.Field, error) {
				return &entity.Field{Name: "Test Field"}, nil
			},
		}
		uc := &FieldUsecase{repo: repo}
		resp, err := uc.GetField(context.Background(), "some-id")
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, "Test Field", resp.Name)
	})

	t.Run("error", func(t *testing.T) {
		repo := &mockGetFieldRepo{
			GetByIDFunc: func(ctx context.Context, id string) (*entity.Field, error) {
				return nil, errors.New("not found")
			},
		}
		uc := &FieldUsecase{repo: repo}
		resp, err := uc.GetField(context.Background(), "some-id")
		assert.Error(t, err)
		assert.Nil(t, resp)
	})
}
