package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
)

func (u *FieldUsecase) ListFields(ctx context.Context, params transport.ListFieldsRequest) ([]transport.FieldResponse, *transport.ListFieldsOutputPagination, error) {
	fields, total, err := u.repo.GetAll(ctx, params)
	if err != nil {
		return nil, nil, err
	}
	Payloads := make([]transport.FieldResponse, len(fields))
	for i, field := range fields {
		Payloads[i] = transport.ToFieldResponse(field)
	}
	currentPage := params.Page
	if currentPage < 0 {
		currentPage = 0
	}
	return Payloads, &transport.ListFieldsOutputPagination{CurrentPage: currentPage, Total: int(total)}, nil
}
