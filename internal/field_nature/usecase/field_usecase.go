package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type FieldService interface {
	GetField(ctx context.Context, id string) (*transport.FieldResponse, error)
	ListFields(ctx context.Context, params transport.ListFieldsRequest) ([]transport.FieldResponse, *transport.ListFieldsOutputPagination, error)
}

type FieldUsecase struct {
	repo   repository.FieldRepository
	logger vlog.Logger
}

func NewFieldUsecase(repo repository.FieldRepository, logger vlog.Logger) *FieldUsecase {
	return &FieldUsecase{repo: repo, logger: logger}
}
