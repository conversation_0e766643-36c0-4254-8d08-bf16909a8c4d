package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (u *FieldUsecase) GetField(ctx context.Context, id string) (*transport.FieldResponse, error) {
	field, err := u.repo.GetByID(ctx, id)
	if err != nil {
		vlog.FromContext(ctx).Error("Failed to get field by ID", vlog.F("id", id), vlog.F("error", err))
		return nil, err
	}
	Payload := transport.ToFieldResponse(*field)
	return &Payload, nil
}
