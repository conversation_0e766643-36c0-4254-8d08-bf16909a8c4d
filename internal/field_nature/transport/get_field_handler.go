package transport

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (h *FieldHandler) GetField(c echo.Context) error {
	fieldId := c.Param("fieldId")
	if fieldId == "" {
		h.Logger.Error("GetFieldHandler: fieldId is required", vlog.F("error", "missing fieldId"))
		return echo.NewHTTPError(http.StatusBadRequest, "fieldId is required")
	}

	if _, err := core.NewIDFromString(fieldId); err != nil {
		h.Logger.Error("GetFieldHandler: invalid fieldId format", vlog.F("error", err), vlog.F("fieldId", fieldId))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid fieldId format")
	}
	field, err := h.Usecase.GetField(c.Request().Context(), fieldId)
	if err != nil {
		h.Logger.Error("GetFieldHandler: field not found", vlog.F("error", err), vlog.F("fieldId", fieldId))
		return echo.NewHTTPError(http.StatusNotFound, err.Error())
	}
	return c.JSON(http.StatusOK, GetFieldResponse{
		Status:  true,
		Message: "",
		Data:    field,
	})
}
