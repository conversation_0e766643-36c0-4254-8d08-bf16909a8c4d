package transport

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestFieldHandler_GetField(t *testing.T) {
	e := echo.New()

	t.Run("successful get field", func(t *testing.T) {
		mockUsecase := new(MockFieldUsecase)
		handler := &FieldHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		fieldID := "123e4567-e89b-12d3-a456-************"
		expectedField := &FieldResponse{UUID: fieldID, Name: "Test Field"}

		mockUsecase.On("GetField", mock.Anything, fieldID).Return(expectedField, nil)

		req := httptest.NewRequest(http.MethodGet, "/fields/"+fieldID, nil)
		rec := httptest.NewRecorder()
		c := e.New<PERSON>ontext(req, rec)
		c.SetParam<PERSON>ames("fieldId")
		c.SetParamValues(fieldID)

		err := handler.GetField(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var response GetFieldResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.True(t, response.Status)
		assert.Equal(t, expectedField, response.Data)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("missing fieldId param", func(t *testing.T) {
		mockUsecase := new(MockFieldUsecase)
		handler := &FieldHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		req := httptest.NewRequest(http.MethodGet, "/fields/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.GetField(c)
		require.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "fieldId is required")
	})

	t.Run("invalid UUID format", func(t *testing.T) {
		mockUsecase := new(MockFieldUsecase)
		handler := &FieldHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		invalidID := "invalid-uuid"
		req := httptest.NewRequest(http.MethodGet, "/fields/"+invalidID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("fieldId")
		c.SetParamValues(invalidID)

		err := handler.GetField(c)
		require.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "Invalid fieldId format")
	})

	t.Run("field not found", func(t *testing.T) {
		mockUsecase := new(MockFieldUsecase)
		handler := &FieldHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		fieldID := "123e4567-e89b-12d3-a456-************"
		mockUsecase.On("GetField", mock.Anything, fieldID).Return(nil, errors.New("not found"))

		req := httptest.NewRequest(http.MethodGet, "/fields/"+fieldID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("fieldId")
		c.SetParamValues(fieldID)

		err := handler.GetField(c)
		require.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusNotFound, httpErr.Code)
	})
}
