package transport

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
)

type MockFieldUsecase struct {
	mock.Mock
}

func (m *MockFieldUsecase) ListFields(ctx context.Context, params ListFieldsRequest) ([]FieldResponse, *ListFieldsOutputPagination, error) {
	return nil, nil, nil
}
func (m *MockFieldUsecase) GetField(ctx context.Context, id string) (*FieldResponse, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*FieldResponse), args.Error(1)
}

func TestToFieldResponse_WithNatures(t *testing.T) {
	fieldID := core.NewID()
	areaID := core.NewID()
	natureID1 := core.NewID()
	natureID2 := core.NewID()
	natures := []entity.Nature{
		{
			UUID:    *natureID1,
			Name:    "Nature 1",
			Icon:    "Icon1",
			Enabled: true,
		},
		{
			UUID:    *natureID2,
			Name:    "Nature 2",
			Icon:    "Icon2",
			Enabled: false,
		},
	}
	field := entity.Field{
		UUID:        *fieldID,
		Name:        "Test Field",
		Description: "desc",
		Icon:        "icon",
		Position:    1,
		Enabled:     true,
		AreaUUID:    areaID,
		Natures:     natures,
	}
	resp := ToFieldResponse(field)
	assert.Equal(t, fieldID.Value(), resp.UUID)
	assert.Equal(t, "Test Field", resp.Name)
	assert.Equal(t, "desc", resp.Description)
	assert.Equal(t, "icon", resp.Icon)
	assert.Equal(t, 1, resp.Position)
	assert.True(t, resp.Enabled)
	assert.Equal(t, areaID.Value(), resp.AreaUUID)
	assert.Len(t, resp.Natures, 2)
	assert.Equal(t, natureID1.Value(), resp.Natures[0].UUID)
	assert.Equal(t, "Nature 1", resp.Natures[0].Name)
	assert.Equal(t, "Icon1", resp.Natures[0].Icon)
	assert.True(t, resp.Natures[0].Enabled)
	assert.Equal(t, natureID2.Value(), resp.Natures[1].UUID)
	assert.Equal(t, "Nature 2", resp.Natures[1].Name)
	assert.Equal(t, "Icon2", resp.Natures[1].Icon)
	assert.False(t, resp.Natures[1].Enabled)
}

func TestToFieldResponse_NoNatures(t *testing.T) {
	fieldID := core.NewID()
	areaID := core.NewID()
	field := entity.Field{
		UUID:        *fieldID,
		Name:        "Test Field",
		Description: "desc",
		Icon:        "icon",
		Position:    1,
		Enabled:     true,
		AreaUUID:    areaID,
		Natures:     nil,
	}
	resp := ToFieldResponse(field)
	assert.Equal(t, fieldID.Value(), resp.UUID)
	assert.Equal(t, 0, len(resp.Natures))
}

func TestToFieldResponse_NilAreaUUID(t *testing.T) {
	fieldID := core.NewID()
	field := entity.Field{
		UUID:        *fieldID,
		Name:        "Test Field",
		Description: "desc",
		Icon:        "icon",
		Position:    1,
		Enabled:     true,
		AreaUUID:    nil,
		Natures:     nil,
	}
	resp := ToFieldResponse(field)
	assert.Equal(t, "", resp.AreaUUID)
}

func TestFieldHandler_ListFields_AllQueryParams(t *testing.T) {
	e := echo.New()
	h := &FieldHandler{Usecase: &mockListFieldsUsecase{}}
	req := httptest.NewRequest(http.MethodGet, "/fields?page=1&size=5&name=Field+1&enabled=true&orderBy=name&direction=ASC&areaUUID=area-uuid", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := h.ListFields(c)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)
	var response ListFieldsOutput
	_ = json.Unmarshal(rec.Body.Bytes(), &response)
	assert.True(t, response.Status)
	assert.Equal(t, []FieldResponse{{UUID: "uuid1", Name: "Field 1"}}, response.Data)
	assert.Equal(t, &ListFieldsOutputPagination{CurrentPage: 0, Total: 1}, response.Pagination)
}

func TestFieldHandler_ListFields_SizeExceedsMaxLimit(t *testing.T) {
	e := echo.New()
	h := &FieldHandler{Usecase: &mockListFieldsUsecase{}}
	req := httptest.NewRequest(http.MethodGet, "/fields?size=200", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := h.ListFields(c)
	assert.NoError(t, err)
}
