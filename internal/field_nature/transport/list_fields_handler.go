package transport

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (h *FieldHandler) ListFields(c echo.Context) error {
	page, err := strconv.Atoi(c.Query<PERSON>aram("page"))
	if err != nil || page < 0 {
		page = 0
	}
	size, err := strconv.Atoi(c.QueryParam("size"))
	if err != nil || size <= 0 {
		size = 10
	}
	if size > 100 {
		size = 100
	}
	orderBy := c.QueryParam("orderBy")
	direction := c.Query<PERSON>aram("direction")
	name := c.Query<PERSON>aram("name")
	areaUUID := c.<PERSON>("areaUUID")
	enabledStr := c.QueryParam("enabled")
	var enabled *bool
	if enabledStr != "" {
		val, err := strconv.ParseBool(enabledStr)
		if err == nil {
			enabled = &val
		}
	}

	params := ListFieldsRequest{
		Page:      page,
		Size:      size,
		OrderBy:   orderBy,
		Direction: direction,
		Name:      &name,
		Enabled:   enabled,
		AreaUUID:  &areaUUID,
	}

	fields, pagination, err := h.Usecase.ListFields(c.Request().Context(), params)
	if err != nil {
		h.Logger.Error("ListFieldsHandler: failed to list fields", vlog.F("error", err))
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, ListFieldsOutput{
		Status:     true,
		Message:    "",
		Data:       fields,
		Pagination: pagination,
	})
}
