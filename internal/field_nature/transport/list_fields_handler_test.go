package transport

import (
	"context"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

type mockListFieldsUsecase struct{}

func (m *mockListFieldsUsecase) ListFields(ctx context.Context, params ListFieldsRequest) ([]FieldResponse, *ListFieldsOutputPagination, error) {
	return []FieldResponse{{UUID: "uuid1", Name: "Field 1"}}, &ListFieldsOutputPagination{CurrentPage: 0, Total: 1}, nil
}
func (m *mockListFieldsUsecase) GetField(ctx context.Context, id string) (*FieldResponse, error) {
	return nil, nil
}

func TestFieldHandler_ListFields_Split(t *testing.T) {
	e := echo.New()
	req := httptest.NewRequest("GET", "/fields?page=0&size=1", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	h := &FieldHandler{Usecase: &mockListFieldsUsecase{}}
	err := h.<PERSON><PERSON>ields(c)
	assert.NoError(t, err)
	assert.Equal(t, 200, rec.Code)
}
