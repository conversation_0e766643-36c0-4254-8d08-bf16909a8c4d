package transport

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type NatureResponse struct {
	UUID    string `json:"uuid"`
	Name    string `json:"name"`
	Icon    string `json:"icon"`
	Enabled bool   `json:"enabled"`
}

type FieldResponse struct {
	UUID        string           `json:"uuid"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Icon        string           `json:"icon"`
	Position    int              `json:"position"`
	Enabled     bool             `json:"enabled"`
	AreaUUID    string           `json:"area_uuid"`
	Natures     []NatureResponse `json:"natures"`
}

type ListFieldsOutput struct {
	Status     bool                        `json:"status"`
	Message    string                      `json:"message"`
	Data       []FieldResponse             `json:"data"`
	Pagination *ListFieldsOutputPagination `json:"pagination,omitempty"`
}

type GetFieldResponse struct {
	Status  bool           `json:"status"`
	Message string         `json:"message"`
	Data    *FieldResponse `json:"data"`
}

type ListFieldsOutputPagination struct {
	CurrentPage int `json:"current_page"`
	Total       int `json:"total"`
}

type ListFieldsRequest struct {
	Page      int
	Size      int
	Enabled   *bool
	Name      *string
	OrderBy   string
	Direction string
	AreaUUID  *string
}

type FieldUsecase interface {
	ListFields(ctx context.Context, params ListFieldsRequest) ([]FieldResponse, *ListFieldsOutputPagination, error)
	GetField(ctx context.Context, id string) (*FieldResponse, error)
}

type FieldHandler struct {
	Usecase FieldUsecase
	Logger  vlog.Logger
}

func ToFieldResponse(field entity.Field) FieldResponse {
	natures := make([]NatureResponse, len(field.Natures))
	for i, n := range field.Natures {
		natures[i] = NatureResponse{
			UUID:    n.UUID.Value(),
			Name:    n.Name,
			Icon:    n.Icon,
			Enabled: n.Enabled,
		}
	}
	return FieldResponse{
		UUID:        field.UUID.Value(),
		Name:        field.Name,
		Description: field.Description,
		Icon:        field.Icon,
		Position:    field.Position,
		Enabled:     field.Enabled,
		AreaUUID: func() string {
			if field.AreaUUID != nil {
				return field.AreaUUID.Value()
			}
			return ""
		}(),
		Natures: natures,
	}
}
