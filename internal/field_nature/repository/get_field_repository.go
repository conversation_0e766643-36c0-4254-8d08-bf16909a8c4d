package repository

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *fieldRepository) GetByID(ctx context.Context, id string) (*entity.Field, error) {
	logger := r.logger.With(vlog.F("method", "GetByID"), vlog.F("action", "fetch field by id"))

	query := `SELECT * FROM field WHERE uuid = $1`
	args := []interface{}{id}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	var dbf fieldModel
	if err := r.db.GetContext(ctx, &dbf, query, args...); err != nil {
		logger.Error("failed to get field", vlog.F("error", err), vlog.F("uuid", id))
		return nil, err
	}

	coreID, err := core.NewIDFromString(dbf.UUID)
	if err != nil {
		logger.Error("failed to parse ID from DB", vlog.F("error", err), vlog.F("db_uuid", dbf.UUID))
		return nil, err
	}
	var areaUUID *core.Identifier
	if dbf.AreaUUID != nil && *dbf.AreaUUID != "" {
		parsedAreaUUID, err := core.NewIDFromString(*dbf.AreaUUID)
		if err != nil {
			logger.Error("failed to parse AreaUUID from DB", vlog.F("error", err), vlog.F("db_area_uuid", dbf.AreaUUID))
			return nil, err
		}
		areaUUID = parsedAreaUUID
	}
	createdAt, err := core.NewTimestampFromTime(dbf.CreatedAt)
	if err != nil {
		logger.Error("failed to parse CreatedAt", vlog.F("error", err))
		return nil, err
	}
	var updatedAt *core.Timestamp
	if !dbf.UpdatedAt.IsZero() {
		updatedAt, err = core.NewTimestampFromTime(dbf.UpdatedAt)
		if err != nil {
			logger.Error("failed to parse UpdatedAt", vlog.F("error", err))
			return nil, err
		}
	}
	// Fetch natures for this field
	natures, err := r.getNaturesByFieldUUID(ctx, dbf.UUID)
	if err != nil {
		logger.Error("failed to fetch natures", vlog.F("error", err), vlog.F("field_uuid", dbf.UUID))
		return nil, err
	}
	field := &entity.Field{
		UUID:        *coreID,
		Name:        dbf.Name,
		Description: derefString(dbf.Description),
		Icon:        derefString(dbf.Icon),
		Position:    derefInt(dbf.Position),
		Enabled:     dbf.Enabled,
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
		AreaUUID:    areaUUID,
		Natures:     natures,
	}
	return field, nil
}
