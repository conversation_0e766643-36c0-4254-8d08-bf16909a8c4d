package repository

import (
	"context"
	"database/sql"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestFieldRepository_GetAll_Success(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	now := time.Now()
	fieldUUID1 := "550e8400-e29b-41d4-a716-************"
	fieldUUID2 := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 ORDER BY name ASC LIMIT \$1 OFFSET \$2`).WithArgs(10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID1, "Field 1", "desc1", "icon1", 1, true, now, now, areaUUID).
			AddRow(fieldUUID2, "Field 2", "desc2", "icon2", 2, false, now, now, areaUUID))

	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID1).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID2).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}))

	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1`).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

	params := transport.ListFieldsRequest{Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 2)
	assert.Equal(t, int64(2), total)
	assert.Equal(t, "Field 1", fields[0].Name)
	assert.Equal(t, 1, fields[0].Position)
	assert.Len(t, fields[0].Natures, 1)
	assert.Equal(t, "Nature 1", fields[0].Natures[0].Name)
	assert.Equal(t, "Field 2", fields[1].Name)
	assert.Equal(t, 2, fields[1].Position)
	assert.Len(t, fields[1].Natures, 0)
}

func TestFieldRepository_GetAll_EmptyResults(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 ORDER BY name ASC LIMIT \$1 OFFSET \$2`).WithArgs(10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1`).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

	params := transport.ListFieldsRequest{Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 0)
	assert.Equal(t, int64(0), total)
}

func TestFieldRepository_GetAll_FieldsQueryError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()

	mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM field ORDER BY $1 ASC LIMIT $2 OFFSET $3")).
		WithArgs("name", 10, 0).
		WillReturnError(sql.ErrConnDone)

	params := transport.ListFieldsRequest{Page: 0, Size: 10}
	_, _, err := repo.GetAll(ctx, params)
	assert.Error(t, err)
}

func TestFieldRepository_GetAll_NaturesQueryError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	now := time.Now()
	fieldUUID := core.NewID().Value()
	areaUUID := core.NewID().Value()

	mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM field ORDER BY $1 ASC LIMIT $2 OFFSET $3")).
		WithArgs("name", 10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, "Field 1", "desc1", "icon1", 1, true, now, now, areaUUID))

	mock.ExpectQuery(regexp.QuoteMeta("SELECT n.uuid, n.name, n.icon, n.enabled FROM nature n JOIN field_nature fn ON n.uuid = fn.nature_uuid WHERE fn.field_uuid = $1")).
		WithArgs(fieldUUID).
		WillReturnError(sql.ErrConnDone)

	params := transport.ListFieldsRequest{Page: 0, Size: 10}
	_, _, err := repo.GetAll(ctx, params)
	assert.Error(t, err)
}

func TestFieldRepository_GetAll_CountQueryError(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	now := time.Now()
	fieldUUID := core.NewID().Value()
	areaUUID := core.NewID().Value()

	mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM field ORDER BY $1 ASC LIMIT $2 OFFSET $3")).
		WithArgs("name", 10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, "Field 1", "desc1", "icon1", 1, true, now, now, areaUUID))

	mock.ExpectQuery(regexp.QuoteMeta("SELECT n.uuid, n.name, n.icon, n.enabled FROM nature n JOIN field_nature fn ON n.uuid = fn.nature_uuid WHERE fn.field_uuid = $1")).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}))

	mock.ExpectQuery(regexp.QuoteMeta("SELECT COUNT(*) FROM field")).
		WillReturnError(sql.ErrConnDone)

	params := transport.ListFieldsRequest{Page: 0, Size: 10}
	_, _, err := repo.GetAll(ctx, params)
	assert.Error(t, err)
}

func TestFieldRepository_GetAll_WithFilters(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	now := time.Now()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	name := "Field 1"
	enabled := true

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 AND LOWER\(name\) LIKE \$1 AND enabled = \$2 AND area_uuid = \$3 ORDER BY name ASC LIMIT \$4 OFFSET \$5`).WithArgs("%field 1%", enabled, areaUUID, 10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, name, "desc1", "icon1", 1, enabled, now, now, areaUUID))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1 AND LOWER\(name\) LIKE \$1 AND enabled = \$2 AND area_uuid = \$3`).WithArgs("%field 1%", enabled, areaUUID).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	params := transport.ListFieldsRequest{Name: &name, Enabled: &enabled, AreaUUID: &areaUUID, Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, name, fields[0].Name)
}
