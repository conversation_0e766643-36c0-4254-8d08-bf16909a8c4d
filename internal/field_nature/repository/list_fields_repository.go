package repository

import (
	"context"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *fieldRepository) GetAll(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error) {
	logger := r.logger.With(vlog.F("method", "GetAll"), vlog.F("action", "list fields"))

	// Build query and args
	query := "SELECT * FROM field WHERE 1=1"
	var args []interface{}
	argIdx := 1
	if params.Name != nil && *params.Name != "" {
		query += fmt.Sprintf(" AND LOWER(name) LIKE $%d", argIdx)
		args = append(args, "%"+strings.ToLower(*params.Name)+"%")
		argIdx++
	}
	if params.Enabled != nil {
		query += fmt.Sprintf(" AND enabled = $%d", argIdx)
		args = append(args, *params.Enabled)
		argIdx++
	}
	if params.AreaUUID != nil && *params.AreaUUID != "" {
		query += fmt.Sprintf(" AND area_uuid = $%d", argIdx)
		args = append(args, *params.AreaUUID)
		argIdx++
	}
	orderBy := "name"
	if params.OrderBy != "" {
		orderBy = params.OrderBy
	}
	direction := "ASC"
	if strings.ToUpper(params.Direction) == "DESC" {
		direction = "DESC"
	}
	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, direction)
	if params.Size > 0 {
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIdx, argIdx+1)
		args = append(args, params.Size)
		offset := 0
		if params.Page > 0 {
			offset = params.Page * params.Size
		}
		args = append(args, offset)
	}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	var dbFields []fieldModel
	if err := r.db.SelectContext(ctx, &dbFields, query, args...); err != nil {
		logger.Error("failed to select fields", vlog.F("error", err))
		return nil, 0, err
	}

	fields := make([]entity.Field, 0, len(dbFields))
	for _, dbf := range dbFields {
		id, err := core.NewIDFromString(dbf.UUID)
		if err != nil {
			logger.Error("failed to parse ID", vlog.F("error", err), vlog.F("db_id", dbf.UUID))
			return nil, 0, err
		}
		areaUUID, err := core.NewIDFromString(*dbf.AreaUUID)
		if err != nil {
			logger.Error("failed to parse AreaUUID", vlog.F("error", err), vlog.F("db_area_uuid", dbf.AreaUUID))
			return nil, 0, err
		}
		createdAt, err := core.NewTimestampFromTime(dbf.CreatedAt)
		if err != nil {
			logger.Error("failed to parse CreatedAt", vlog.F("error", err))
			return nil, 0, err
		}
		var updatedAt *core.Timestamp
		if !dbf.UpdatedAt.IsZero() {
			updatedAt, err = core.NewTimestampFromTime(dbf.UpdatedAt)
			if err != nil {
				logger.Error("failed to parse UpdatedAt", vlog.F("error", err))
				return nil, 0, err
			}
		}
		// Fetch natures for this field
		natures, err := r.getNaturesByFieldUUID(ctx, dbf.UUID)
		if err != nil {
			logger.Error("failed to fetch natures", vlog.F("error", err), vlog.F("field_uuid", dbf.UUID))
			return nil, 0, err
		}
		fields = append(fields, entity.Field{
			UUID:        *id,
			Name:        dbf.Name,
			Description: derefString(dbf.Description),
			Icon:        derefString(dbf.Icon),
			Position:    derefInt(dbf.Position),
			Enabled:     dbf.Enabled,
			CreatedAt:   createdAt,
			UpdatedAt:   updatedAt,
			AreaUUID:    areaUUID,
			Natures:     natures,
		})
	}

	// Count query
	countQuery := "SELECT COUNT(uuid) FROM field WHERE 1=1"
	countArgs := make([]interface{}, 0)
	countIdx := 1
	if params.Name != nil && *params.Name != "" {
		countQuery += fmt.Sprintf(" AND LOWER(name) LIKE $%d", countIdx)
		countArgs = append(countArgs, "%"+strings.ToLower(*params.Name)+"%")
		countIdx++
	}
	if params.Enabled != nil {
		countQuery += fmt.Sprintf(" AND enabled = $%d", countIdx)
		countArgs = append(countArgs, *params.Enabled)
		countIdx++
	}
	if params.AreaUUID != nil && *params.AreaUUID != "" {
		countQuery += fmt.Sprintf(" AND area_uuid = $%d", countIdx)
		countArgs = append(countArgs, *params.AreaUUID)
		countIdx++
	}
	logger.Debug("executing count", vlog.F("query", countQuery), vlog.F("args", countArgs))

	var total int64
	if err := r.db.GetContext(ctx, &total, countQuery, countArgs...); err != nil {
		logger.Error("failed to get total count", vlog.F("error", err))
		return nil, 0, err
	}

	return fields, total, nil
}
