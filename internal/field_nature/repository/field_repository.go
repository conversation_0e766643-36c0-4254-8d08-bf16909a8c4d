package repository

import (
	"context"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type FieldRepository interface {
	GetAll(ctx context.Context, params transport.ListFieldsRequest) ([]entity.Field, int64, error)
	GetByID(ctx context.Context, id string) (*entity.Field, error)
}

type fieldRepository struct {
	db     *sqlx.DB
	logger vlog.Logger
}

type fieldModel struct {
	UUID        string    `db:"uuid"`
	Name        string    `db:"name"`
	Description *string   `db:"description"`
	Icon        *string   `db:"icon"`
	Position    *int      `db:"position"`
	Enabled     bool      `db:"enabled"`
	CreatedAt   time.Time `db:"created_at"`
	UpdatedAt   time.Time `db:"updated_at"`
	AreaUUID    *string   `db:"area_uuid"`
}

func NewFieldRepository(db *sqlx.DB, logger vlog.Logger) FieldRepository {
	return &fieldRepository{db: db, logger: logger}
}

// Utility functions for pointer dereferencing
func derefString(ptr *string) string {
	if ptr != nil {
		return *ptr
	}
	return ""
}

func derefInt(ptr *int) int {
	if ptr != nil {
		return *ptr
	}
	return 0
}
