package repository

import (
	"context"
	"testing"

	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestNewFieldRepository(t *testing.T) {
	db, _, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := NewFieldRepository(sqlxdb, vlog.NewWithLevel("error"))
	assert.NotNil(t, repo)
}

func TestDerefString(t *testing.T) {
	str := "hello"
	assert.Equal(t, "hello", derefString(&str))
	assert.Equal(t, "", derefString(nil))
}

func TestDerefInt(t *testing.T) {
	val := 42
	assert.Equal(t, 42, derefInt(&val))
	assert.Equal(t, 0, derefInt(nil))
}

func TestFieldRepository_getNaturesByFieldUUID_Unit(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"

	rows := sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
		AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true).
		AddRow("550e8400-e29b-41d4-a716-************", "Nature 2", "Icon2", false)

	mock.ExpectQuery("SELECT n.uuid, n.name, n.icon, n.enabled").
		WithArgs(fieldUUID).
		WillReturnRows(rows)

	natures, err := repo.getNaturesByFieldUUID(ctx, fieldUUID)
	assert.NoError(t, err)
	assert.Len(t, natures, 2)
	assert.Equal(t, "Nature 1", natures[0].Name)
	assert.Equal(t, "Nature 2", natures[1].Name)
	assert.True(t, natures[0].Enabled)
	assert.False(t, natures[1].Enabled)
}

func TestFieldRepository_GetAll_NoFilters(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 ORDER BY name ASC LIMIT \$1 OFFSET \$2`).WithArgs(10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, "Field 1", "desc1", "icon1", 1, true, now, now, areaUUID))

	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))

	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1`).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	params := transport.ListFieldsRequest{Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, "Field 1", fields[0].Name)
	assert.Len(t, fields[0].Natures, 1)
	assert.Equal(t, "Nature 1", fields[0].Natures[0].Name)
}

func TestFieldRepository_GetAll_FilterByName(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	name := "Field 1"

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 AND LOWER\(name\) LIKE \$1 ORDER BY name ASC LIMIT \$2 OFFSET \$3`).WithArgs("%field 1%", 10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, name, "desc1", "icon1", 1, true, now, now, areaUUID))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1 AND LOWER\(name\) LIKE \$1`).WithArgs("%field 1%").
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	params := transport.ListFieldsRequest{Name: &name, Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, name, fields[0].Name)
}

func TestFieldRepository_GetAll_FilterByEnabled(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	enabled := true

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 AND enabled = \$1 ORDER BY name ASC LIMIT \$2 OFFSET \$3`).WithArgs(enabled, 10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, "Field 1", "desc1", "icon1", 1, enabled, now, now, areaUUID))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1 AND enabled = \$1`).WithArgs(enabled).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	params := transport.ListFieldsRequest{Enabled: &enabled, Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(1), total)
	assert.True(t, fields[0].Enabled)
}

func TestFieldRepository_GetAll_FilterByAreaUUID(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 AND area_uuid = \$1 ORDER BY name ASC LIMIT \$2 OFFSET \$3`).WithArgs(areaUUID, 10, 0).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, "Field 1", "desc1", "icon1", 1, true, now, now, areaUUID))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1 AND area_uuid = \$1`).WithArgs(areaUUID).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	params := transport.ListFieldsRequest{AreaUUID: &areaUUID, Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, areaUUID, fields[0].AreaUUID.Value())
}

func TestFieldRepository_GetAll_CombinedFilters(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	name := "Field 1"
	enabled := true

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 AND LOWER\(name\) LIKE \$1 AND enabled = \$2 AND area_uuid = \$3 ORDER BY name ASC`).WithArgs("%field 1%", enabled, areaUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, name, "desc1", "icon1", 1, enabled, now, now, areaUUID))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1 AND LOWER\(name\) LIKE \$1 AND enabled = \$2 AND area_uuid = \$3`).WithArgs("%field 1%", enabled, areaUUID).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	params := transport.ListFieldsRequest{Name: &name, Enabled: &enabled, AreaUUID: &areaUUID}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, name, fields[0].Name)
}

func TestFieldRepository_GetAll_Pagination(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 ORDER BY name ASC LIMIT \$1 OFFSET \$2`).WithArgs(2, 2).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, "Field 2", "desc2", "icon2", 2, true, now, now, areaUUID))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 2", "Icon2", true))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1`).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

	params := transport.ListFieldsRequest{Page: 1, Size: 2}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(2), total)
	assert.Equal(t, "Field 2", fields[0].Name)
}

func TestFieldRepository_GetAll_Sorting(t *testing.T) {
	db, mock, _ := sqlmock.New()
	sqlxdb := sqlx.NewDb(db, "postgres")
	repo := &fieldRepository{db: sqlxdb, logger: vlog.NewWithLevel("error")}
	ctx := context.Background()
	fieldUUID := "550e8400-e29b-41d4-a716-************"
	areaUUID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()

	mock.ExpectQuery(`SELECT \* FROM field WHERE 1=1 ORDER BY name DESC`).WithArgs().
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "description", "icon", "position", "enabled", "created_at", "updated_at", "area_uuid"}).
			AddRow(fieldUUID, "Field 1", "desc1", "icon1", 1, true, now, now, areaUUID))
	mock.ExpectQuery(`SELECT n.uuid, n.name, n.icon, n.enabled`).
		WithArgs(fieldUUID).
		WillReturnRows(sqlmock.NewRows([]string{"uuid", "name", "icon", "enabled"}).
			AddRow("550e8400-e29b-41d4-a716-************", "Nature 1", "Icon1", true))
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM field WHERE 1=1`).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	params := transport.ListFieldsRequest{OrderBy: "name", Direction: "DESC", Page: 0, Size: 10}
	fields, total, err := repo.GetAll(ctx, params)
	assert.NoError(t, err)
	assert.Len(t, fields, 1)
	assert.Equal(t, int64(1), total)
	assert.Equal(t, "Field 1", fields[0].Name)
}
