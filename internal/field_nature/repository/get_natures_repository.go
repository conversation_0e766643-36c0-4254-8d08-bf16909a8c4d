package repository

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *fieldRepository) getNaturesByFieldUUID(ctx context.Context, fieldUUID string) ([]entity.Nature, error) {
	query := `SELECT n.uuid, n.name, n.icon, n.enabled
		FROM nature n
		JOIN field_nature fn ON n.uuid = fn.nature_uuid
		WHERE fn.field_uuid = $1`
	var natures []struct {
		UUID    string  `db:"uuid"`
		Name    string  `db:"name"`
		Icon    *string `db:"icon"`
		Enabled bool    `db:"enabled"`
	}
	log := vlog.FromContext(ctx)
	log.Info("getNaturesByFieldUUID: querying for field_uuid", vlog.F("field_uuid", fieldUUID))
	if err := r.db.SelectContext(ctx, &natures, query, fieldUUID); err != nil {
		log.Error("getNaturesByFieldUUID: failed to fetch natures", vlog.F("error", err), vlog.F("field_uuid", fieldUUID))
		return nil, err
	}
	log.Info("getNaturesByFieldUUID: fetched natures", vlog.F("count", len(natures)), vlog.F("field_uuid", fieldUUID))
	result := make([]entity.Nature, len(natures))
	for i, n := range natures {
		id, err := core.NewIDFromString(n.UUID)
		if err != nil {
			log.Error("getNaturesByFieldUUID: failed to parse nature UUID", vlog.F("error", err), vlog.F("uuid", n.UUID))
			return nil, err
		}
		result[i] = entity.Nature{
			UUID:    *id,
			Name:    n.Name,
			Icon:    derefString(n.Icon),
			Enabled: n.Enabled,
		}
	}
	return result, nil
}
