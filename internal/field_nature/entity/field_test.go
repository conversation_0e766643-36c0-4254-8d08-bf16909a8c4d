package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

func TestField_Creation(t *testing.T) {
	t.Run("valid field creation", func(t *testing.T) {
		id := core.NewID()
		field := Field{
			UUID:        *id,
			Name:        "Psychiatry",
			Description: "Mental health field",
			Icon:        "PSY",
			Position:    1,
			Enabled:     true,
		}

		assert.Equal(t, *id, field.UUID)
		assert.Equal(t, "Psychiatry", field.Name)
		assert.Equal(t, "Mental health field", field.Description)
		assert.Equal(t, 1, field.Position)
		assert.True(t, field.Enabled)
		assert.Equal(t, "PSY", field.Icon)
	})

	t.Run("field with disabled status", func(t *testing.T) {
		id := core.NewID()
		field := Field{
			UUID:        *id,
			Name:        "Prosthodontics",
			Description: "Dental prosthetics field",
			Icon:        "PRO",
			Position:    1,
			Enabled:     true,
		}

		assert.Equal(t, *id, field.UUID)
		assert.Equal(t, "Prosthodontics", field.Name)
		assert.Equal(t, "Dental prosthetics field", field.Description)
		assert.Equal(t, 1, field.Position)
		assert.True(t, field.Enabled)
		assert.Equal(t, "PRO", field.Icon)
	})
}

func TestField_ZeroValues(t *testing.T) {
	field := Field{}
	assert.Equal(t, "", field.UUID.Value())
	assert.Empty(t, field.Name)
	assert.False(t, field.Enabled)
	assert.Empty(t, field.Icon)
	assert.Equal(t, 0, field.Position)
}

func TestField_WithNatures(t *testing.T) {
	fieldID := core.NewID()
	natureID1 := core.NewID()
	natureID2 := core.NewID()
	natures := []Nature{
		{
			UUID:    *natureID1,
			Name:    "Smile Consultation",
			Icon:    "SmileConsultation",
			Enabled: true,
		},
		{
			UUID:    *natureID2,
			Name:    "Veneers Consultation",
			Icon:    "VeneersConsultation",
			Enabled: false,
		},
	}
	field := Field{
		UUID:    *fieldID,
		Name:    "Cardiology",
		Natures: natures,
	}
	assert.Equal(t, 2, len(field.Natures))
	assert.Equal(t, "Smile Consultation", field.Natures[0].Name)
	assert.Equal(t, "Veneers Consultation", field.Natures[1].Name)
	assert.True(t, field.Natures[0].Enabled)
	assert.False(t, field.Natures[1].Enabled)
}

func TestNature_Creation(t *testing.T) {
	id := core.NewID()
	nature := Nature{
		UUID:    *id,
		Name:    "Test Nature",
		Icon:    "TestIcon",
		Enabled: true,
	}
	assert.Equal(t, *id, nature.UUID)
	assert.Equal(t, "Test Nature", nature.Name)
	assert.Equal(t, "TestIcon", nature.Icon)
	assert.True(t, nature.Enabled)
}
