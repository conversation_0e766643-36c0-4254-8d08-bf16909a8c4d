package field_nature

import (
	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/field_nature/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func NewFieldHandlerFactory(db *sqlx.DB, logger vlog.Logger) *transport.FieldHandler {
	repo := repository.NewFieldRepository(db, logger)
	uc := usecase.NewFieldUsecase(repo, logger)
	return &transport.FieldHandler{Usecase: uc, Logger: logger}
}
