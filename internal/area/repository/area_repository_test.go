package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

const (
	medicineID   = "84ce4d70-a442-412b-bf27-06f4544a8661"
	dentistryID  = "4beed17b-a38a-4da1-8b26-94d2f1513001"
	psychologyID = "e117dcf1-4acd-499f-80d2-7c868f23d6d0"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewAreaRepository(t *testing.T) {
	db, _ := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAreaRepository(db, logger)

	assert.NotNil(t, repo)
	assert.Implements(t, (*AreaRepository)(nil), repo)
}

func TestAreaRepository_GetAll(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAreaRepository(db, logger)
	ctx := context.Background()
	columns := []string{"id", "label", "enabled", "acronym", "created_at", "updated_at"}
	now := time.Now()

	t.Run("get all areas without filters", func(t *testing.T) {
		rows := sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now)
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC`).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		params := transport.ListAreasRequest{}
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Len(t, areas, 3)
		require.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("filter by acronym", func(t *testing.T) {
		acronym := "MED"
		rows := sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now)
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND acronym = \$1 ORDER BY label ASC`).WithArgs(acronym).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND acronym = \$1`).WithArgs(acronym).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		params := transport.ListAreasRequest{Acronym: &acronym}
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, areas, 1)
		assert.Equal(t, "MED", areas[0].Acronym)
		require.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("filter by label", func(t *testing.T) {
		label := "Med"
		rows := sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now)
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND LOWER\(label\) LIKE \$1 ORDER BY label ASC`).WithArgs("%med%").WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND LOWER\(label\) LIKE \$1`).WithArgs("%med%").WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		params := transport.ListAreasRequest{Label: &label}
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, areas, 1)
		assert.Equal(t, "Medicine", areas[0].Label)
		require.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("filter by enabled status", func(t *testing.T) {
		enabled := true
		rows := sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now)
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND enabled = \$1 ORDER BY label ASC`).WithArgs(enabled).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND enabled = \$1`).WithArgs(enabled).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
		params := transport.ListAreasRequest{Enabled: &enabled}
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, areas, 2)
		for _, area := range areas {
			assert.True(t, area.Enabled)
		}
		require.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("pagination", func(t *testing.T) {
		rows := sqlmock.NewRows(columns).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now)
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC LIMIT \$1 OFFSET \$2`).WithArgs(2, 2).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		params := transport.ListAreasRequest{Page: 1, Size: 2}
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Len(t, areas, 1)
		require.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("sorting by label desc", func(t *testing.T) {
		rows := sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now)
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label DESC`).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		params := transport.ListAreasRequest{OrderBy: "label", Direction: "DESC"}
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Equal(t, "Medicine", areas[0].Label)
		assert.Equal(t, "Dentistry", areas[1].Label)
		assert.Equal(t, "Psychology", areas[2].Label)
		require.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty filters", func(t *testing.T) {
		rows := sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now)
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC`).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		emptyStr := ""
		params := transport.ListAreasRequest{Acronym: &emptyStr, Label: &emptyStr}
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Len(t, areas, 3)
		require.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestAreaRepository_GetByID(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAreaRepository(db, logger)
	ctx := context.Background()

	columns := []string{"id", "label", "enabled", "acronym", "created_at", "updated_at"}
	now := time.Now()
	row := sqlmock.NewRows(columns).
		AddRow(medicineID, "Medicine", true, "MED", now, now)

	mock.ExpectQuery(`SELECT \* FROM area WHERE id = \$1`).WithArgs(medicineID).WillReturnRows(row)

	area, err := repo.GetByID(ctx, medicineID)
	require.NoError(t, err)
	assert.NotNil(t, area)
	assert.Equal(t, "Medicine", area.Label)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaRepository_GetAll_EdgeCases(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAreaRepository(db, logger)
	ctx := context.Background()
	columns := []string{"id", "label", "enabled", "acronym", "created_at", "updated_at"}
	now := time.Now()

	t.Run("filter by non-existent acronym", func(t *testing.T) {
		acronym := "NONEXISTENT"
		params := transport.ListAreasRequest{Acronym: &acronym}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND acronym = \$1 ORDER BY label ASC`).WithArgs(acronym).WillReturnRows(sqlmock.NewRows(columns))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND acronym = \$1`).WithArgs(acronym).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))
		areas, total, err := repo.GetAll(ctx, params)

		require.NoError(t, err)
		assert.Equal(t, int64(0), total)
		assert.Empty(t, areas)
	})

	t.Run("filter by partial label match", func(t *testing.T) {
		label := "ent"
		params := transport.ListAreasRequest{Label: &label}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND LOWER\(label\) LIKE \$1 ORDER BY label ASC`).WithArgs("%" + label + "%").WillReturnRows(sqlmock.NewRows(columns).AddRow(dentistryID, "Dentistry", true, "DENT", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND LOWER\(label\) LIKE \$1`).WithArgs("%" + label + "%").WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		areas, total, err := repo.GetAll(ctx, params)

		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, areas, 1)
		assert.Equal(t, "Dentistry", areas[0].Label)
	})

	t.Run("filter by disabled status", func(t *testing.T) {
		enabled := false
		params := transport.ListAreasRequest{Enabled: &enabled}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND enabled = \$1 ORDER BY label ASC`).WithArgs(enabled).WillReturnRows(sqlmock.NewRows(columns).AddRow(psychologyID, "Psychology", false, "PSY", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND enabled = \$1`).WithArgs(enabled).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		areas, total, err := repo.GetAll(ctx, params)

		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, areas, 1)
		assert.Equal(t, "Psychology", areas[0].Label)
		assert.False(t, areas[0].Enabled)
	})

	t.Run("pagination with page 0", func(t *testing.T) {
		params := transport.ListAreasRequest{Page: 0, Size: 2}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC LIMIT \$1 OFFSET \$2`).WithArgs(2, 0).WillReturnRows(sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Len(t, areas, 2)
	})

	t.Run("pagination beyond available data", func(t *testing.T) {
		params := transport.ListAreasRequest{Page: 10, Size: 2}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC LIMIT \$1 OFFSET \$2`).WithArgs(2, 20).WillReturnRows(sqlmock.NewRows(columns))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Empty(t, areas)
	})

	t.Run("large page size", func(t *testing.T) {
		params := transport.ListAreasRequest{Page: 0, Size: 1000}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC LIMIT \$1 OFFSET \$2`).WithArgs(1000, 0).WillReturnRows(sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Len(t, areas, 3)
	})

	t.Run("sorting by acronym asc", func(t *testing.T) {
		params := transport.ListAreasRequest{OrderBy: "acronym", Direction: "ASC"}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY acronym ASC`).WithArgs().WillReturnRows(sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Equal(t, "MED", areas[0].Acronym)
		assert.Equal(t, "DENT", areas[1].Acronym)
		assert.Equal(t, "PSY", areas[2].Acronym)
	})

	t.Run("sorting by enabled desc", func(t *testing.T) {
		params := transport.ListAreasRequest{OrderBy: "enabled", Direction: "DESC"}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY enabled DESC`).WithArgs().WillReturnRows(sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.True(t, areas[0].Enabled)
		assert.True(t, areas[1].Enabled)
		assert.False(t, areas[2].Enabled)
	})

	t.Run("invalid order direction defaults to ASC", func(t *testing.T) {
		params := transport.ListAreasRequest{OrderBy: "label", Direction: "INVALID"}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC`).WithArgs().WillReturnRows(sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Equal(t, "Medicine", areas[0].Label)
		assert.Equal(t, "Dentistry", areas[1].Label)
		assert.Equal(t, "Psychology", areas[2].Label)
	})

	t.Run("combined filters and sorting", func(t *testing.T) {
		enabled := true
		params := transport.ListAreasRequest{
			Enabled:   &enabled,
			OrderBy:   "label",
			Direction: "DESC",
			Page:      0,
			Size:      2,
		}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND enabled = \$1 ORDER BY label DESC LIMIT \$2 OFFSET \$3`).WithArgs(enabled, 2, 0).WillReturnRows(sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND enabled = \$1`).WithArgs(enabled).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, areas, 2)
		assert.Equal(t, "Medicine", areas[0].Label)
		assert.Equal(t, "Dentistry", areas[1].Label)
		for _, area := range areas {
			assert.True(t, area.Enabled)
		}
	})

	t.Run("case insensitive label search", func(t *testing.T) {
		label := "MEDICINE"
		params := transport.ListAreasRequest{Label: &label, Page: 0, Size: 2}
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 AND LOWER\(label\) LIKE \$1 ORDER BY label ASC LIMIT \$2 OFFSET \$3`).WithArgs("%medicine%", 2, 0).WillReturnRows(sqlmock.NewRows(columns).AddRow(medicineID, "Medicine", true, "MED", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1 AND LOWER\(label\) LIKE \$1`).WithArgs("%medicine%").WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, areas, 1)
		assert.Equal(t, "Medicine", areas[0].Label)
	})
}

func TestAreaRepository_DatabaseErrors(t *testing.T) {
	t.Run("closed database connection", func(t *testing.T) {
		db, _ := setupMockDB(t)
		repo := NewAreaRepository(db, vlog.NewWithLevel("error"))
		ctx := context.Background()

		db.Close()

		_, _, err := repo.GetAll(ctx, transport.ListAreasRequest{})
		assert.Error(t, err)

		_, err = repo.GetByID(ctx, uuid.New().String())
		assert.Error(t, err)
	})
}

func TestAreaRepository_ContextCancellation(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAreaRepository(db, vlog.NewWithLevel("error"))

	t.Run("cancelled context for GetAll", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		// Simulate context cancellation error
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC`).WithArgs().WillReturnError(context.Canceled)
		_, _, err := repo.GetAll(ctx, transport.ListAreasRequest{})
		if err != nil {
			assert.Contains(t, err.Error(), "context")
		}
	})

	t.Run("cancelled context for GetByID", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		mock.ExpectQuery(`SELECT \* FROM area WHERE id = \$1`).WithArgs(sqlmock.AnyArg()).WillReturnError(context.Canceled)
		_, err := repo.GetByID(ctx, uuid.New().String())
		if err != nil {
			assert.Contains(t, err.Error(), "context")
		}
	})
}

func TestAreaRepository_NilPointerHandling(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAreaRepository(db, vlog.NewWithLevel("error"))
	ctx := context.Background()

	t.Run("nil filter pointers are handled correctly", func(t *testing.T) {
		params := transport.ListAreasRequest{
			Acronym: nil,
			Label:   nil,
			Enabled: nil,
		}
		columns := []string{"id", "label", "enabled", "acronym", "created_at", "updated_at"}
		now := time.Now()
		mock.ExpectQuery(`SELECT \* FROM area WHERE 1=1 ORDER BY label ASC`).WithArgs().WillReturnRows(sqlmock.NewRows(columns).
			AddRow(medicineID, "Medicine", true, "MED", now, now).
			AddRow(dentistryID, "Dentistry", true, "DENT", now, now).
			AddRow(psychologyID, "Psychology", false, "PSY", now, now))
		mock.ExpectQuery(`SELECT COUNT\(id\) FROM area WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))

		areas, total, err := repo.GetAll(ctx, params)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(0))
		assert.NotNil(t, areas)
	})
}
