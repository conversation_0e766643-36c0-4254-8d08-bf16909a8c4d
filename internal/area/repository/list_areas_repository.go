package repository

import (
	"context"
	"fmt"
	"strings"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type areaRepositoryGetAll struct{ db *sqlx.DB }

func (r *areaRepository) GetAll(ctx context.Context, params transport.ListAreasRequest) ([]entity.Area, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetAll"), vlog.F("action", "list areas"))

	query := "SELECT * FROM area WHERE 1=1"
	var args []interface{}
	argIdx := 1
	if params.Acronym != nil && *params.Acronym != "" {
		query += fmt.Sprintf(" AND acronym = $%d", argIdx)
		args = append(args, *params.Acronym)
		argIdx++
	}
	if params.Label != nil && *params.Label != "" {
		query += fmt.Sprintf(" AND LOWER(label) LIKE $%d", argIdx)
		args = append(args, "%"+strings.ToLower(*params.Label)+"%")
		argIdx++
	}
	if params.Enabled != nil {
		query += fmt.Sprintf(" AND enabled = $%d", argIdx)
		args = append(args, *params.Enabled)
		argIdx++
	}
	orderBy := "label"
	if params.OrderBy != "" {
		orderBy = params.OrderBy
	}
	direction := "ASC"
	if strings.ToUpper(params.Direction) == "DESC" {
		direction = "DESC"
	}
	query += fmt.Sprintf(" ORDER BY %s %s", orderBy, direction)
	if params.Size > 0 {
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIdx, argIdx+1)
		args = append(args, params.Size)
		offset := 0
		if params.Page > 0 {
			offset = params.Page * params.Size
		}
		args = append(args, offset)
	}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	rows, err := r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		logger.Error("areaRepository.GetAll: failed to select areas", vlog.F("error", err))
		return nil, 0, err
	}
	defer rows.Close()

	var areas []entity.Area
	for rows.Next() {
		var dba areaModel
		if err := rows.StructScan(&dba); err != nil {
			logger.Error("areaRepository.GetAll: areaRepository.GetAll:failed to scan area row", vlog.F("error", err))
			return nil, 0, err
		}
		id, err := core.NewIDFromString(dba.ID)
		if err != nil {
			logger.Error("areaRepository.GetAll: failed to parse ID", vlog.F("error", err), vlog.F("db_id", dba.ID))
			return nil, 0, err
		}
		createdAt, err := core.NewTimestampFromTime(dba.CreatedAt)
		if err != nil {
			logger.Error("areaRepository.GetAll: failed to parse CreatedAt", vlog.F("error", err))
			return nil, 0, err
		}
		var updatedAt *core.Timestamp
		if !dba.UpdatedAt.IsZero() {
			updatedAt, err = core.NewTimestampFromTime(dba.UpdatedAt)
			if err != nil {
				logger.Error("areaRepository.GetAll: failed to parse UpdatedAt", vlog.F("error", err))
				return nil, 0, err
			}
		}
		areas = append(areas, entity.Area{
			ID:        *id,
			Label:     dba.Label,
			Enabled:   dba.Enabled,
			Acronym:   dba.Acronym,
			CreatedAt: createdAt,
			UpdatedAt: updatedAt,
		})
	}

	if err := rows.Err(); err != nil {
		logger.Error("rows iteration error", vlog.F("error", err))
		return nil, 0, err
	}

	// Count query
	countQuery := "SELECT COUNT(id) FROM area WHERE 1=1"
	countArgs := make([]interface{}, 0)
	countIdx := 1
	if params.Acronym != nil && *params.Acronym != "" {
		countQuery += fmt.Sprintf(" AND acronym = $%d", countIdx)
		countArgs = append(countArgs, *params.Acronym)
		countIdx++
	}
	if params.Label != nil && *params.Label != "" {
		countQuery += fmt.Sprintf(" AND LOWER(label) LIKE $%d", countIdx)
		countArgs = append(countArgs, "%"+strings.ToLower(*params.Label)+"%")
		countIdx++
	}
	if params.Enabled != nil {
		countQuery += fmt.Sprintf(" AND enabled = $%d", countIdx)
		countArgs = append(countArgs, *params.Enabled)
		countIdx++
	}
	logger.Debug("areaRepository.GetAll: executing count", vlog.F("query", countQuery), vlog.F("args", countArgs))

	var total int64
	if err := r.db.GetContext(ctx, &total, countQuery, countArgs...); err != nil {
		logger.Error("areaRepository.GetAll: failed to get total count", vlog.F("error", err))
		return nil, 0, err
	}

	return areas, total, nil
}
