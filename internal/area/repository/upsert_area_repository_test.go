package repository_test

import (
	"context"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func createTestArea(t *testing.T) *entity.Area {
	validID := *core.NewID()
	return &entity.Area{
		ID:      validID,
		Label:   "Medicine",
		Acronym: "MED",
		Enabled: true,
	}
}

func TestAreaRepository_UpsertArea_Insert(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAreaUpsertRepository(db, vlog.NewWithLevel("error"))
	area := createTestArea(t)

	// Expect successful INSERT
	mock.ExpectExec(`INSERT INTO public.area \(id, label, acronym, enabled, created_at, updated_at\) VALUES \(\$1, \$2, \$3, \$4, NOW\(\), NOW\(\)\) ON CONFLICT \(id\) DO UPDATE SET label = EXCLUDED.label, acronym = EXCLUDED.acronym, enabled = EXCLUDED.enabled, updated_at = NOW\(\)`).
		WithArgs(area.ID.Value(), area.Label, area.Acronym, area.Enabled).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := repo.UpsertArea(context.Background(), area)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaRepository_UpsertArea_Update(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAreaUpsertRepository(db, vlog.NewWithLevel("error"))
	area := createTestArea(t)

	// Expect successful UPDATE (same as INSERT with ON CONFLICT)
	mock.ExpectExec(`INSERT INTO public.area \(id, label, acronym, enabled, created_at, updated_at\) VALUES \(\$1, \$2, \$3, \$4, NOW\(\), NOW\(\)\) ON CONFLICT \(id\) DO UPDATE SET label = EXCLUDED.label, acronym = EXCLUDED.acronym, enabled = EXCLUDED.enabled, updated_at = NOW\(\)`).
		WithArgs(area.ID.Value(), area.Label, area.Acronym, area.Enabled).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := repo.UpsertArea(context.Background(), area)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaRepository_UpsertArea_AcronymConstraintViolation(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAreaUpsertRepository(db, vlog.NewWithLevel("error"))
	area := createTestArea(t)

	// Expect acronym constraint violation
	mock.ExpectExec(`INSERT INTO public.area \(id, label, acronym, enabled, created_at, updated_at\) VALUES \(\$1, \$2, \$3, \$4, NOW\(\), NOW\(\)\) ON CONFLICT \(id\) DO UPDATE SET label = EXCLUDED.label, acronym = EXCLUDED.acronym, enabled = EXCLUDED.enabled, updated_at = NOW\(\)`).
		WithArgs(area.ID.Value(), area.Label, area.Acronym, area.Enabled).
		WillReturnError(errors.New("duplicate key value violates unique constraint \"area_acronym_key\""))

	err := repo.UpsertArea(context.Background(), area)

	assert.Error(t, err)
	assert.IsType(t, &repository.AreaAcronymExistsError{}, err)
	assert.Equal(t, "MED", err.(*repository.AreaAcronymExistsError).Acronym)
	assert.Contains(t, err.Error(), "area acronym 'MED' already exists")
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaRepository_UpsertArea_GenericDatabaseError(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAreaUpsertRepository(db, vlog.NewWithLevel("error"))
	area := createTestArea(t)

	// Expect generic database error
	mock.ExpectExec(`INSERT INTO public.area \(id, label, acronym, enabled, created_at, updated_at\) VALUES \(\$1, \$2, \$3, \$4, NOW\(\), NOW\(\)\) ON CONFLICT \(id\) DO UPDATE SET label = EXCLUDED.label, acronym = EXCLUDED.acronym, enabled = EXCLUDED.enabled, updated_at = NOW\(\)`).
		WithArgs(area.ID.Value(), area.Label, area.Acronym, area.Enabled).
		WillReturnError(errors.New("connection timeout"))

	err := repo.UpsertArea(context.Background(), area)

	assert.Error(t, err)
	assert.Equal(t, "connection timeout", err.Error())
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaRepository_UpsertArea_DisabledArea(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAreaUpsertRepository(db, vlog.NewWithLevel("error"))
	area := createTestArea(t)
	area.Enabled = false // Set to disabled

	// Expect successful upsert with disabled status
	mock.ExpectExec(`INSERT INTO public.area \(id, label, acronym, enabled, created_at, updated_at\) VALUES \(\$1, \$2, \$3, \$4, NOW\(\), NOW\(\)\) ON CONFLICT \(id\) DO UPDATE SET label = EXCLUDED.label, acronym = EXCLUDED.acronym, enabled = EXCLUDED.enabled, updated_at = NOW\(\)`).
		WithArgs(area.ID.Value(), area.Label, area.Acronym, false).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := repo.UpsertArea(context.Background(), area)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaRepository_UpsertArea_LongLabelAndAcronym(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAreaUpsertRepository(db, vlog.NewWithLevel("error"))
	area := createTestArea(t)
	area.Label = "Advanced Cardiovascular Medicine and Surgery"
	area.Acronym = "ACMS"

	// Expect successful upsert with long label and acronym
	mock.ExpectExec(`INSERT INTO public.area \(id, label, acronym, enabled, created_at, updated_at\) VALUES \(\$1, \$2, \$3, \$4, NOW\(\), NOW\(\)\) ON CONFLICT \(id\) DO UPDATE SET label = EXCLUDED.label, acronym = EXCLUDED.acronym, enabled = EXCLUDED.enabled, updated_at = NOW\(\)`).
		WithArgs(area.ID.Value(), area.Label, area.Acronym, area.Enabled).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := repo.UpsertArea(context.Background(), area)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestAreaAcronymExistsError_Error(t *testing.T) {
	err := &repository.AreaAcronymExistsError{Acronym: "MED"}
	assert.Equal(t, "area acronym 'MED' already exists", err.Error())
}
