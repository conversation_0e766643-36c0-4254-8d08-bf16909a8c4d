package repository

import (
	"context"
	"fmt"
	"strings"

	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
)

// AreaAcronymExistsError represents a domain error when area acronym already exists
type AreaAcronymExistsError struct {
	Acronym string
}

func (e *AreaAcronymExistsError) Error() string {
	return fmt.Sprintf("area acronym '%s' already exists", e.Acronym)
}

// UpsertArea upserts an area entity into the database
func (r *areaRepository) UpsertArea(ctx context.Context, area *entity.Area) error {
	query := `
		INSERT INTO public.area (id, label, acronym, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, NOW(), NOW())
		ON CONFLICT (id) DO UPDATE SET
			label = EXCLUDED.label,
			acronym = EXCLUDED.acronym,
			enabled = EXCLUDED.enabled,
			updated_at = NOW()
	`

	_, err := r.db.ExecContext(ctx, query,
		area.ID.Value(),
		area.Label,
		area.Acronym,
		area.Enabled,
	)

	if err != nil {
		// Check for acronym constraint violation
		if strings.Contains(err.Error(), "area_acronym_key") || strings.Contains(err.Error(), "area_acronym") {
			return &AreaAcronymExistsError{Acronym: area.Acronym}
		}
		return err
	}

	return nil
}
