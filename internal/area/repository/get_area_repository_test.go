package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestAreaRepository_GetByID_Split(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAreaRepository(db, vlog.NewWithLevel("error"))
	ctx := context.Background()
	columns := []string{"id", "label", "enabled", "acronym", "created_at", "updated_at"}
	areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	row := sqlmock.NewRows(columns).
		AddRow(areaID, "Medicine", true, "MED", now, now)
	mock.ExpectQuery(`SELECT \* FROM area WHERE id = \$1`).WithArgs(areaID).WillReturnRows(row)

	area, err := repo.GetByID(ctx, areaID)
	require.NoError(t, err)
	assert.NotNil(t, area)
	assert.Equal(t, areaID, area.ID.Value())
}
