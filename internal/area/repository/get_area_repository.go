package repository

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)



func (r *areaRepository) GetByID(ctx context.Context, id string) (*entity.Area, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetByID"), vlog.F("action", "fetch area by id"))

	query := `SELECT * FROM area WHERE id = $1`
	args := []interface{}{id}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	var dba areaModel
	if err := r.db.GetContext(ctx, &dba, query, args...); err != nil {
		logger.Error("failed to get area", vlog.F("error", err), vlog.F("id", id))
		return nil, err
	}

	coreID, err := core.NewIDFromString(dba.ID)
	if err != nil {
		logger.Error("failed to parse ID from DB", vlog.F("error", err), vlog.F("db_id", dba.ID))
		return nil, err
	}
	createdAt, err := core.NewTimestampFromTime(dba.CreatedAt)
	if err != nil {
		logger.Error("failed to parse CreatedAt", vlog.F("error", err))
		return nil, err
	}
	var updatedAt *core.Timestamp
	if !dba.UpdatedAt.IsZero() {
		updatedAt, err = core.NewTimestampFromTime(dba.UpdatedAt)
		if err != nil {
			logger.Error("failed to parse UpdatedAt", vlog.F("error", err))
			return nil, err
		}
	}
	area := &entity.Area{
		ID:        *coreID,
		Label:     dba.Label,
		Enabled:   dba.Enabled,
		Acronym:   dba.Acronym,
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}
	return area, nil
}
