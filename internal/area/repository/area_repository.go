package repository

import (
	"context"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type AreaRepository interface {
	GetAll(ctx context.Context, params transport.ListAreasRequest) ([]entity.Area, int64, error)
	GetByID(ctx context.Context, id string) (*entity.Area, error)
}

// AreaUpsertRepository interface for SQS upsert functionality
type AreaUpsertRepository interface {
	UpsertArea(ctx context.Context, area *entity.Area) error
}

type areaRepository struct {
	db     *sqlx.DB
	logger vlog.Logger
}

type areaModel struct {
	ID        string    `db:"id"`
	Label     string    `db:"label"`
	Enabled   bool      `db:"enabled"`
	Acronym   string    `db:"acronym"`
	CreatedAt time.Time `db:"created_at"`
	UpdatedAt time.Time `db:"updated_at"`
}

func NewAreaRepository(db *sqlx.DB, logger vlog.Logger) AreaRepository {
	return &areaRepository{db: db, logger: logger}
}

// NewAreaUpsertRepository creates a new area repository for upsert operations
func NewAreaUpsertRepository(db *sqlx.DB, logger vlog.Logger) AreaUpsertRepository {
	return &areaRepository{db: db, logger: logger}
}
