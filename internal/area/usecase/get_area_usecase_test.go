package usecase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
)

type mockRepoGetArea struct{}

func (m *mockRepoGetArea) GetAll(ctx context.Context, params transport.ListAreasRequest) ([]entity.Area, int64, error) {
	return nil, 0, nil
}
func (m *mockRepoGetArea) GetByID(ctx context.Context, id string) (*entity.Area, error) {
	return &entity.Area{Label: "Test"}, nil
}

func TestAreaUsecase_GetArea_Split(t *testing.T) {
	uc := &AreaUsecase{repo: &mockRepoGetArea{}}
	area, err := uc.GetArea(context.Background(), "some-id")
	require.NoError(t, err)
	assert.NotNil(t, area)
	assert.Equal(t, "Test", area.Label)
}
