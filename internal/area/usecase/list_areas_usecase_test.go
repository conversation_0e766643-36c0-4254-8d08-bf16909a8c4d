package usecase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
)

type mockRepo struct{}

func (m *mockRepo) GetAll(ctx context.Context, params transport.ListAreasRequest) ([]entity.Area, int64, error) {
	return []entity.Area{{Label: "Test"}}, 1, nil
}
func (m *mockRepo) GetByID(ctx context.Context, id string) (*entity.Area, error) {
	return &entity.Area{Label: "Test"}, nil
}

func TestAreaUsecase_ListAreas_Split(t *testing.T) {
	uc := &AreaUsecase{repo: &mockRepo{}}
	areas, pagination, err := uc.ListAreas(context.Background(), transport.ListAreasRequest{})
	require.NoError(t, err)
	assert.NotNil(t, areas)
	assert.NotNil(t, pagination)
	assert.Equal(t, "Test", areas[0].Label)
}
