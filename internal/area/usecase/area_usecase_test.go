package usecase

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
)

// MockAreaRepository is a mock implementation of AreaRepository
type MockAreaRepository struct {
	mock.Mock
}

func MustParse(v string) *core.Identifier {
	id, err := core.NewIDFromString(v)
	if err != nil {
		return nil
	}
	return id
}

func (m *MockAreaRepository) GetAll(ctx context.Context, params transport.ListAreasRequest) ([]entity.Area, int64, error) {
	args := m.Called(ctx, params)
	return args.Get(0).([]entity.Area), args.Get(1).(int64), args.Error(2)
}

func (m *MockAreaRepository) GetByID(ctx context.Context, id string) (*entity.Area, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.Area), args.Error(1)
}

func TestNewAreaUsecase(t *testing.T) {
	mockRepo := new(MockAreaRepository)
	usecase := NewAreaUsecase(mockRepo)

	assert.NotNil(t, usecase)
	assert.Equal(t, mockRepo, usecase.repo)
	assert.Implements(t, (*AreaService)(nil), usecase)
}

func TestAreaUsecase_ListAreas(t *testing.T) {
	ctx := context.Background()

	t.Run("successful list areas", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		params := transport.ListAreasRequest{
			Page: 1,
			Size: 10,
		}

		areas := []entity.Area{
			{
				ID:      *MustParse("84ce4d70-a442-412b-bf27-06f4544a8661"),
				Label:   "Medicine",
				Enabled: true,
				Acronym: "MED",
			},
			{
				ID:      *MustParse("4beed17b-a38a-4da1-8b26-94d2f1513001"),
				Label:   "Dentistry",
				Enabled: true,
				Acronym: "DENT",
			},
		}

		mockRepo.On("GetAll", ctx, params).Return(areas, int64(2), nil)

		result, pagination, err := usecase.ListAreas(ctx, params)

		require.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, "84ce4d70-a442-412b-bf27-06f4544a8661", result[0].ID)
		assert.Equal(t, "Medicine", result[0].Label)
		assert.True(t, result[0].Enabled)
		assert.Equal(t, "MED", result[0].Acronym)

		assert.Equal(t, "4beed17b-a38a-4da1-8b26-94d2f1513001", result[1].ID)
		assert.Equal(t, "Dentistry", result[1].Label)
		assert.True(t, result[1].Enabled)
		assert.Equal(t, "DENT", result[1].Acronym)

		assert.NotNil(t, pagination)
		assert.Equal(t, 1, pagination.CurrentPage)
		assert.Equal(t, 2, pagination.Total)

		mockRepo.AssertExpectations(t)
	})

	t.Run("empty result", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		params := transport.ListAreasRequest{Page: 0, Size: 10}
		mockRepo.On("GetAll", ctx, params).Return([]entity.Area{}, int64(0), nil)

		result, pagination, err := usecase.ListAreas(ctx, params)

		require.NoError(t, err)
		assert.Empty(t, result)
		assert.NotNil(t, pagination)
		assert.Equal(t, 0, pagination.CurrentPage)
		assert.Equal(t, 0, pagination.Total)

		mockRepo.AssertExpectations(t)
	})

	t.Run("negative page number", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		params := transport.ListAreasRequest{Page: -1, Size: 10}
		areas := []entity.Area{
			{
				ID:      *MustParse("84ce4d70-a442-412b-bf27-06f4544a8661"),
				Label:   "Medicine",
				Enabled: true,
				Acronym: "MED",
			},
		}

		mockRepo.On("GetAll", ctx, params).Return(areas, int64(1), nil)

		result, pagination, err := usecase.ListAreas(ctx, params)

		require.NoError(t, err)
		assert.Len(t, result, 1)
		assert.NotNil(t, pagination)
		assert.Equal(t, 0, pagination.CurrentPage) // Should be corrected to 0
		assert.Equal(t, 1, pagination.Total)

		mockRepo.AssertExpectations(t)
	})

	t.Run("repository error", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		params := transport.ListAreasRequest{Page: 0, Size: 10}
		expectedError := errors.New("database connection error")

		mockRepo.On("GetAll", ctx, params).Return([]entity.Area{}, int64(0), expectedError)

		result, pagination, err := usecase.ListAreas(ctx, params)

		assert.Error(t, err)
		assert.Equal(t, expectedError, err)
		assert.Nil(t, result)
		assert.Nil(t, pagination)

		mockRepo.AssertExpectations(t)
	})

	t.Run("with filters", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		enabled := true
		acronym := "MED"
		label := "Medicine"
		params := transport.ListAreasRequest{
			Page:      0,
			Size:      10,
			Enabled:   &enabled,
			Acronym:   &acronym,
			Label:     &label,
			OrderBy:   "label",
			Direction: "ASC",
		}

		areas := []entity.Area{
			{
				ID:      *MustParse("84ce4d70-a442-412b-bf27-06f4544a8661"),
				Label:   "Medicine",
				Enabled: true,
				Acronym: "MED",
			},
		}

		mockRepo.On("GetAll", ctx, params).Return(areas, int64(1), nil)

		result, pagination, err := usecase.ListAreas(ctx, params)

		require.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, "Medicine", result[0].Label)
		assert.Equal(t, 0, pagination.CurrentPage)
		assert.Equal(t, 1, pagination.Total)

		mockRepo.AssertExpectations(t)
	})
}

func TestAreaUsecase_GetArea(t *testing.T) {
	ctx := context.Background()

	t.Run("successful get area", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		area := &entity.Area{
			ID:      *MustParse(areaID),
			Label:   "Medicine",
			Enabled: true,
			Acronym: "MED",
		}

		mockRepo.On("GetByID", ctx, areaID).Return(area, nil)

		result, err := usecase.GetArea(ctx, areaID)

		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, areaID, result.ID)
		assert.Equal(t, "Medicine", result.Label)
		assert.True(t, result.Enabled)
		assert.Equal(t, "MED", result.Acronym)

		mockRepo.AssertExpectations(t)
	})

	t.Run("area not found", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		areaID := "non-existent-id"
		expectedError := errors.New("area not found")

		mockRepo.On("GetByID", ctx, areaID).Return((*entity.Area)(nil), expectedError)

		result, err := usecase.GetArea(ctx, areaID)

		assert.Error(t, err)
		assert.Equal(t, expectedError, err)
		assert.Nil(t, result)

		mockRepo.AssertExpectations(t)
	})

	t.Run("repository error", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		expectedError := errors.New("database connection error")

		mockRepo.On("GetByID", ctx, areaID).Return((*entity.Area)(nil), expectedError)

		result, err := usecase.GetArea(ctx, areaID)

		assert.Error(t, err)
		assert.Equal(t, expectedError, err)
		assert.Nil(t, result)

		mockRepo.AssertExpectations(t)
	})

	t.Run("disabled area", func(t *testing.T) {
		mockRepo := new(MockAreaRepository)
		usecase := NewAreaUsecase(mockRepo)

		areaID := "e117dcf1-4acd-499f-80d2-7c868f23d6d0"
		area := &entity.Area{
			ID:      *MustParse(areaID),
			Label:   "Psychology",
			Enabled: false,
			Acronym: "PSY",
		}

		mockRepo.On("GetByID", ctx, areaID).Return(area, nil)

		result, err := usecase.GetArea(ctx, areaID)

		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, areaID, result.ID)
		assert.Equal(t, "Psychology", result.Label)
		assert.False(t, result.Enabled)
		assert.Equal(t, "PSY", result.Acronym)

		mockRepo.AssertExpectations(t)
	})
}
