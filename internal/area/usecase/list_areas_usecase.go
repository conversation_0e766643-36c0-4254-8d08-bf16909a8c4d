package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
)

func (u *AreaUsecase) ListAreas(ctx context.Context, params transport.ListAreasRequest) ([]transport.AreaResponse, *transport.ListAreasOutputPagination, error) {
	areas, total, err := u.repo.GetAll(ctx, params)
	if err != nil {
		return nil, nil, err
	}
	Payloads := make([]transport.AreaResponse, len(areas))
	for i, area := range areas {
		Payloads[i] = transport.ToAreaResponse(area)
	}
	currentPage := params.Page
	if currentPage < 0 {
		currentPage = 0
	}
	return Payloads, &transport.ListAreasOutputPagination{CurrentPage: currentPage, Total: int(total)}, nil
}
