package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (u *AreaUsecase) GetArea(ctx context.Context, id string) (*transport.AreaResponse, error) {
	area, err := u.repo.GetByID(ctx, id)
	if err != nil {
		vlog.FromContext(ctx).Error("Failed to get area by ID", vlog.F("id", id), vlog.F("error", err))
		return nil, err
	}
	Payload := transport.ToAreaResponse(*area)
	return &Payload, nil
}
