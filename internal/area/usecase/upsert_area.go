package usecase

import (
	"context"
	"errors"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/area/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var upsertAreaUsecaseName = "UpsertAreaUsecase"

type AreaUpsertRepository interface {
	UpsertArea(ctx context.Context, area *entity.Area) error
}

type UpsertAreaInput struct {
	ID      *string
	Label   string
	Acronym string
	Enabled bool
}

type UpsertAreaOutput struct {
	ID      string
	Label   string
	Acronym string
	Enabled bool
}

type UpsertAreaUsecase struct {
	repo              AreaUpsertRepository
	validationService service.AreaValidationService
}

func NewUpsertAreaUsecase(repo AreaUpsertRepository, validationService service.AreaValidationService) *UpsertAreaUsecase {
	return &UpsertAreaUsecase{
		repo:              repo,
		validationService: validationService,
	}
}

func (uc *UpsertAreaUsecase) Execute(ctx context.Context, input *UpsertAreaInput) (*UpsertAreaOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", upsertAreaUsecaseName))
	logger.Debug("trying to execute", vlog.F("input", input))

	// Validate input
	if err := uc.validateInput(input); err != nil {
		logger.Error("validation failed", vlog.F("error", err))
		return nil, err
	}

	// Validate using service layer business rules
	serviceInput := &service.UpsertValidationInput{
		ID:      input.ID,
		Label:   input.Label,
		Acronym: input.Acronym,
		Enabled: input.Enabled,
	}
	if err := uc.validationService.ValidateUpsertInput(ctx, serviceInput); err != nil {
		logger.Error("service validation failed", vlog.F("error", err))
		return nil, err
	}

	// Determine entity ID
	var entityID core.Identifier
	if input.ID != nil && *input.ID != "" {
		idPtr, err := core.NewIDFromString(*input.ID)
		if err != nil {
			logger.Error("failed to parse provided ID", vlog.F("error", err))
			return nil, core.NewBusinessError("invalid ID format: %v", err)
		}
		entityID = *idPtr
	} else {
		entityID = *core.NewID()
	}

	// Create area entity
	area := &entity.Area{
		ID:      entityID,
		Label:   input.Label,
		Acronym: input.Acronym,
		Enabled: input.Enabled,
	}

	// Validate business rules using service layer
	if err := uc.validationService.ValidateBusinessRules(ctx, area); err != nil {
		logger.Error("business rules validation failed", vlog.F("error", err))
		return nil, err
	}

	// Upsert area entity
	if err := uc.repo.UpsertArea(ctx, area); err != nil {
		// Check if this is an acronym constraint violation
		var acronymExistsErr *repository.AreaAcronymExistsError
		if errors.As(err, &acronymExistsErr) {
			logger.Error("Acronym constraint violation - message will go to DLQ",
				vlog.F("error", err),
				vlog.F("area_acronym", area.Acronym),
				vlog.F("area_id", area.ID.Value()))
			return nil, core.NewBusinessError("Area acronym '%s' already exists", acronymExistsErr.Acronym)
		}

		logger.Error("failed to upsert area", vlog.F("error", err))
		return nil, core.NewBusinessError("database error occurred while upserting area: %v", err)
	}

	// Create output
	out := &UpsertAreaOutput{
		ID:      area.ID.Value(),
		Label:   area.Label,
		Acronym: area.Acronym,
		Enabled: area.Enabled,
	}

	logger.Debug("execution finished", vlog.F("output", out))
	return out, nil
}

// validateInput validates the input using core validation patterns
func (uc *UpsertAreaUsecase) validateInput(input *UpsertAreaInput) error {
	if input == nil {
		return core.NewBusinessError("input is required")
	}

	// Use core validation patterns
	if input.Label == "" {
		return core.NewBusinessError("label is required")
	}
	if input.Acronym == "" {
		return core.NewBusinessError("acronym is required")
	}

	// Validate UUID format if provided using core
	if input.ID != nil && *input.ID != "" {
		if _, err := core.NewIDFromString(*input.ID); err != nil {
			return core.NewBusinessError("invalid UUID format: %v", err)
		}
	}

	return nil
}
