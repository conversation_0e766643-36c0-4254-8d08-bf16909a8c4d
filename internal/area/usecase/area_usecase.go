package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
)

type AreaService interface {
	GetArea(ctx context.Context, id string) (*transport.AreaResponse, error)
	ListAreas(ctx context.Context, params transport.ListAreasRequest) ([]transport.AreaResponse, *transport.ListAreasOutputPagination, error)
}

type AreaUsecase struct {
	repo repository.AreaRepository
}

func NewAreaUsecase(repo repository.AreaRepository) *AreaUsecase {
	return &AreaUsecase{repo: repo}
}
