package event_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/event"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
)

// MockUpsertAreaUsecase is a mock implementation of the UpsertAreaUsecase interface
type MockUpsertAreaUsecase struct {
	mock.Mock
}

func (m *MockUpsertAreaUsecase) Execute(ctx context.Context, input *usecase.UpsertAreaInput) (*usecase.UpsertAreaOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.UpsertAreaOutput), args.Error(1)
}

func TestAreaUpsertEventProcessor_ProcessMessage(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name           string
		message        *event.AreaEventMessage
		setupMock      func(*MockUpsertAreaUsecase)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Successful processing with ID",
			message: &event.AreaEventMessage{
				Data: event.AreaEventData{
					ID:      core.ToPtr(validID.Value()),
					Label:   "Medicine",
					Acronym: "MED",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "area.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAreaUsecase) {
				expectedInput := &usecase.UpsertAreaInput{
					ID:      core.ToPtr(validID.Value()),
					Label:   "Medicine",
					Acronym: "MED",
					Enabled: true,
				}
				expectedOutput := &usecase.UpsertAreaOutput{
					ID:      validID.Value(),
					Label:   "Medicine",
					Acronym: "MED",
					Enabled: true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name: "Successful processing without ID",
			message: &event.AreaEventMessage{
				Data: event.AreaEventData{
					Label:   "Dentistry",
					Acronym: "DENT",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "area.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAreaUsecase) {
				expectedInput := &usecase.UpsertAreaInput{
					ID:      nil,
					Label:   "Dentistry",
					Acronym: "DENT",
					Enabled: true,
				}
				expectedOutput := &usecase.UpsertAreaOutput{
					ID:      "new-generated-id",
					Label:   "Dentistry",
					Acronym: "DENT",
					Enabled: true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name:           "Nil message",
			message:        nil,
			setupMock:      func(mockUsecase *MockUpsertAreaUsecase) {},
			wantErr:        true,
			wantErrMessage: "message is required",
		},
		{
			name: "Empty label",
			message: &event.AreaEventMessage{
				Data: event.AreaEventData{
					Label:   "",
					Acronym: "MED",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "area.upsert",
			},
			setupMock:      func(mockUsecase *MockUpsertAreaUsecase) {},
			wantErr:        true,
			wantErrMessage: "area label is required in message data",
		},
		{
			name: "Empty acronym",
			message: &event.AreaEventMessage{
				Data: event.AreaEventData{
					Label:   "Medicine",
					Acronym: "",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "area.upsert",
			},
			setupMock:      func(mockUsecase *MockUpsertAreaUsecase) {},
			wantErr:        true,
			wantErrMessage: "area acronym is required in message data",
		},
		{
			name: "Invalid UUID format",
			message: &event.AreaEventMessage{
				Data: event.AreaEventData{
					ID:      core.ToPtr("invalid-uuid"),
					Label:   "Medicine",
					Acronym: "MED",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "area.upsert",
			},
			setupMock:      func(mockUsecase *MockUpsertAreaUsecase) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format in message data",
		},
		{
			name: "Usecase execution error",
			message: &event.AreaEventMessage{
				Data: event.AreaEventData{
					Label:   "Medicine",
					Acronym: "MED",
					Enabled: true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "area.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAreaUsecase) {
				mockUsecase.On("Execute", mock.Anything, mock.Anything).Return(nil, errors.New("database error"))
			},
			wantErr:        true,
			wantErrMessage: "failed to process area upsert from message",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUsecase := new(MockUpsertAreaUsecase)
			tt.setupMock(mockUsecase)

			processor := event.NewAreaUpsertEventProcessor(mockUsecase)
			err := processor.ProcessMessage(context.Background(), tt.message)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			mockUsecase.AssertExpectations(t)
		})
	}
}

func TestNewAreaUpsertEventProcessor(t *testing.T) {
	mockUsecase := new(MockUpsertAreaUsecase)
	processor := event.NewAreaUpsertEventProcessor(mockUsecase)

	assert.NotNil(t, processor)
	assert.IsType(t, &event.AreaUpsertEventProcessor{}, processor)
}
