package event

import (
	"time"
)

// AreaEventData represents the data structure for area events
type AreaEventData struct {
	ID      *string `json:"id,omitempty"`
	Label   string  `json:"label"`
	Acronym string  `json:"acronym"`
	Enabled bool    `json:"enabled"`
}

// AreaEventMessage represents the event message structure for area operations
type AreaEventMessage struct {
	Data      AreaEventData `json:"data"`
	Timestamp time.Time     `json:"timestamp"`
	Source    string        `json:"source"`
	EventType string        `json:"event_type"`
}
