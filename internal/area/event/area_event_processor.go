package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertAreaUsecase defines the interface for area upsert operations
type UpsertAreaUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertAreaInput) (*usecase.UpsertAreaOutput, error)
}

// AreaUpsertEventProcessor processes area upsert events
type AreaUpsertEventProcessor struct {
	upsertUsecase UpsertAreaUsecase
}

// NewAreaUpsertEventProcessor creates a new area upsert event processor
func NewAreaUpsertEventProcessor(upsertUsecase UpsertAreaUsecase) *AreaUpsertEventProcessor {
	return &AreaUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// ProcessMessage processes an area event message
func (p *AreaUpsertEventProcessor) ProcessMessage(ctx context.Context, message *AreaEventMessage) error {
	if message == nil {
		return core.NewBusinessError("message is required")
	}

	logger := vlog.New().With(vlog.F("processor", "AreaUpsertEventProcessor"))
	logger.Debug("handling area event message", vlog.F("event_type", message.EventType))

	// Validate message data
	if message.Data.Label == "" {
		logger.Error("validation failed", vlog.F("error", "area label is required"))
		return core.NewBusinessError("area label is required in message data")
	}
	if message.Data.Acronym == "" {
		logger.Error("validation failed", vlog.F("error", "area acronym is required"))
		return core.NewBusinessError("area acronym is required in message data")
	}

	// Validate ID format if provided
	if message.Data.ID != nil && *message.Data.ID != "" {
		if _, err := core.NewIDFromString(*message.Data.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.Data.ID))
			return core.NewBusinessError("invalid UUID format in message data: %v", err)
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertAreaInput{
		Label:   message.Data.Label,
		Acronym: message.Data.Acronym,
		Enabled: message.Data.Enabled,
	}

	if message.Data.ID != nil {
		upsertInput.ID = message.Data.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		logger.Error("failed to process area upsert from message", vlog.F("error", err))
		return fmt.Errorf("failed to process area upsert from message: %w", err)
	}

	logger.Info("successfully processed area upsert", vlog.F("label", message.Data.Label))
	return nil
}
