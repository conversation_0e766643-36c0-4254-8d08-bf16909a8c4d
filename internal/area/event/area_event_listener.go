package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// AreaEventSubscriber defines the interface for subscribing to area events
type AreaEventSubscriber interface {
	// Subscribe starts listening to area events
	Subscribe(ctx context.Context, processor interface {
		ProcessMessage(ctx context.Context, message *AreaEventMessage) error
	}) error

	// Start starts the subscription service
	Start(ctx context.Context) error

	// Stop stops the subscription service
	Stop() error

	// IsRunning returns true if the subscriber is running
	IsRunning() bool
}

// AreaEventListener manages the area event subscription service
type AreaEventListener struct {
	subscriber AreaEventSubscriber
	cfg        config.Configuration
}

// NewAreaEventListener creates a new area event listener instance
func NewAreaEventListener(cfg config.Configuration, upsertUsecase UpsertAreaUsecase) (*AreaEventListener, error) {
	// Validate that area queue URL is configured
	if cfg.AWS.SQSQueueURLArea == "" {
		return nil, fmt.Errorf("AWS_SQS_QUEUE_URL_AREA is required for area event operations")
	}

	// Initialize SQS event subscriber with area queue URL
	subscriber, err := NewSQSAreaEventSubscriber(cfg, cfg.AWS.SQSQueueURLArea)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS event subscriber: %w", err)
	}

	// Create event handler
	eventProcessor := NewAreaUpsertEventProcessor(upsertUsecase)

	// Set up subscriber with handler
	if err := subscriber.Subscribe(context.Background(), eventProcessor); err != nil {
		return nil, fmt.Errorf("failed to set up event subscription: %w", err)
	}

	return &AreaEventListener{
		subscriber: subscriber,
		cfg:        cfg,
	}, nil
}

// Start starts the area event listener
func (s *AreaEventListener) Start(ctx context.Context) error {
	logger := vlog.New().With(vlog.F("service", "AreaEventListener"))
	logger.Info("starting area event listener")

	if err := s.subscriber.Start(ctx); err != nil {
		logger.Error("failed to start event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to start event subscriber: %w", err)
	}

	logger.Info("area event listener started successfully")
	return nil
}

// Stop stops the area event listener
func (s *AreaEventListener) Stop() error {
	logger := vlog.New().With(vlog.F("service", "AreaEventListener"))
	logger.Info("stopping area event listener")

	if err := s.subscriber.Stop(); err != nil {
		logger.Error("failed to stop event subscriber", vlog.F("error", err))
		return fmt.Errorf("failed to stop event subscriber: %w", err)
	}

	logger.Info("area event listener stopped successfully")
	return nil
}

// IsRunning returns true if the service is currently running
func (s *AreaEventListener) IsRunning() bool {
	return s.subscriber.IsRunning()
}
