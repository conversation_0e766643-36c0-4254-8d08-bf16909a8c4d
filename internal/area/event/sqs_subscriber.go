package event

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	awspkg "gitlab.viswalslab.com/backend/price-list/pkg/aws"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// SQSAreaEventSubscriber implements AreaEventSubscriber using AWS SQS
type SQSAreaEventSubscriber struct {
	sqsClient *awspkg.SQSClient
	cfg       config.Configuration
	processor interface {
		// ProcessMessage processes an area event message
		ProcessMessage(ctx context.Context, message *AreaEventMessage) error
	}
	logger    vlog.Logger
	isRunning bool
	mutex     sync.RWMutex
	stopChan  chan struct{}
	wg        sync.WaitGroup
}

// NewSQSAreaEventSubscriber creates a new SQS-based area event subscriber
func NewSQSAreaEventSubscriber(cfg config.Configuration, queueURL string) (*SQSAreaEventSubscriber, error) {
	sqsConfig := awspkg.SQSConfig{
		Region:          cfg.AWS.Region,
		AccessKeyID:     cfg.AWS.AccessKeyID,
		SecretAccessKey: cfg.AWS.SecretAccessKey,
		QueueURL:        queueURL,
	}

	sqsClient, err := awspkg.InitializeSQSClient(sqsConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS client: %w", err)
	}

	return &SQSAreaEventSubscriber{
		sqsClient: sqsClient,
		cfg:       cfg,
		logger:    vlog.New().With(vlog.F("subscriber", "AreaEventSubscriber")),
		stopChan:  make(chan struct{}),
	}, nil
}

// Subscribe configures the event processor for the subscriber
func (s *SQSAreaEventSubscriber) Subscribe(ctx context.Context, processor interface {
	ProcessMessage(ctx context.Context, message *AreaEventMessage) error
}) error {
	s.processor = processor
	return nil
}

// Start starts the subscription service
func (s *SQSAreaEventSubscriber) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("area event subscriber is already running")
	}

	if s.processor == nil {
		return fmt.Errorf("event processor is required")
	}

	s.logger.Info("starting area event subscriber")

	s.isRunning = true
	s.wg.Add(1)

	go s.pollMessages(ctx)

	return nil
}

// Stop stops the subscription service
func (s *SQSAreaEventSubscriber) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("stopping area event subscriber")

	close(s.stopChan)
	s.wg.Wait()

	s.isRunning = false

	if err := s.sqsClient.Close(); err != nil {
		s.logger.Error("failed to close area event subscriber client", vlog.F("error", err))
		return fmt.Errorf("failed to close area event subscriber client: %w", err)
	}

	s.logger.Info("area event subscriber stopped successfully")
	return nil
}

// IsRunning returns true if the subscriber is running
func (s *SQSAreaEventSubscriber) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// pollMessages continuously polls for messages from SQS
func (s *SQSAreaEventSubscriber) pollMessages(ctx context.Context) {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("context cancelled, stopping area event polling")
			return
		case <-s.stopChan:
			s.logger.Info("stop signal received, stopping area event polling")
			return
		case <-ticker.C:
			s.processMessages(ctx)
		}
	}
}

// processMessages processes incoming SQS messages
func (s *SQSAreaEventSubscriber) processMessages(ctx context.Context) {
	result, err := s.sqsClient.ReceiveMessages(ctx, 10, 20)
	if err != nil {
		s.logger.Error("error receiving area event messages", vlog.F("error", err))
		return
	}

	if len(result.Messages) == 0 {
		return
	}

	for _, msg := range result.Messages {
		processErr := s.processMessage(ctx, &msg)
		if processErr != nil {
			s.logger.Error("error processing area event message", vlog.F("error", processErr))
		}

		// Always delete the message from SQS after processing attempt
		// This prevents infinite reprocessing of failed messages
		if msg.ReceiptHandle != nil {
			if _, deleteErr := s.sqsClient.DeleteMessage(ctx, *msg.ReceiptHandle); deleteErr != nil {
				s.logger.Error("error deleting area event message", vlog.F("error", deleteErr))
			} else {
				if processErr != nil {
					s.logger.Info("deleted failed message from queue to prevent reprocessing",
						vlog.F("processing_error", processErr.Error()))
				}
			}
		}
	}
}

// processMessage processes a single SQS message
func (s *SQSAreaEventSubscriber) processMessage(ctx context.Context, msg *types.Message) error {
	if msg.Body == nil {
		return fmt.Errorf("message body is nil")
	}

	var eventMessage AreaEventMessage
	if err := json.Unmarshal([]byte(*msg.Body), &eventMessage); err != nil {
		return fmt.Errorf("failed to unmarshal message: %w", err)
	}

	if err := s.processor.ProcessMessage(ctx, &eventMessage); err != nil {
		return fmt.Errorf("failed to handle event message: %w", err)
	}

	return nil
}
