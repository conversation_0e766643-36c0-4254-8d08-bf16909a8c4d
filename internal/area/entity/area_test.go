package entity

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

func TestArea_Creation(t *testing.T) {
	t.Run("valid area creation", func(t *testing.T) {
		id := core.NewID()
		area := Area{
			ID:      *id,
			Label:   "Medicine",
			Enabled: true,
			Acronym: "MED",
		}

		assert.Equal(t, *id, area.ID)
		assert.Equal(t, "Medicine", area.Label)
		assert.True(t, area.Enabled)
		assert.Equal(t, "MED", area.Acronym)
	})

	t.Run("area with disabled status", func(t *testing.T) {
		id := core.NewID()
		area := Area{
			ID:      *id,
			Label:   "Dentistry",
			Enabled: false,
			Acronym: "DENT",
		}

		assert.Equal(t, *id, area.ID)
		assert.Equal(t, "Dentistry", area.Label)
		assert.False(t, area.Enabled)
		assert.Equal(t, "DENT", area.Acronym)
	})
}

func TestArea_ZeroValues(t *testing.T) {
	area := Area{}
	assert.Equal(t, "", area.ID.Value())
	assert.Empty(t, area.Label)
	assert.False(t, area.Enabled)
	assert.Empty(t, area.Acronym)
}
