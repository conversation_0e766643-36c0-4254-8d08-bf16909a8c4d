package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/area/service"
)

func TestAreaValidationService_ValidateLabelAndUUID(t *testing.T) {
	validationService := service.NewAreaValidationService()
	ctx := context.Background()
	validID := core.NewID()

	tests := []struct {
		name     string
		labelVal string
		uuid     string
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "Valid label and UUID",
			labelVal: "Medicine",
			uuid:     validID.Value(),
			wantErr:  false,
		},
		{
			name:     "Valid label with empty UUID",
			labelVal: "Dentistry",
			uuid:     "",
			wantErr:  false,
		},
		{
			name:     "Empty label",
			labelVal: "",
			uuid:     validID.Value(),
			wantErr:  true,
			errMsg:   "area label is required",
		},
		{
			name:     "Invalid UUID format",
			labelVal: "Medicine",
			uuid:     "invalid-uuid-format",
			wantErr:  true,
			errMsg:   "invalid uuid format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validationService.ValidateLabelAndUUID(ctx, tt.labelVal, tt.uuid)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAreaValidationService_ValidateBusinessRules(t *testing.T) {
	validationService := service.NewAreaValidationService()
	ctx := context.Background()
	validID := *core.NewID()

	tests := []struct {
		name    string
		area    *entity.Area
		wantErr bool
		errMsg  string
	}{
		{
			name: "Valid area entity",
			area: &entity.Area{
				ID:      validID,
				Label:   "Medicine",
				Acronym: "MED",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name:    "Nil area entity",
			area:    nil,
			wantErr: true,
			errMsg:  "area entity is required",
		},
		{
			name: "Empty label",
			area: &entity.Area{
				ID:      validID,
				Label:   "",
				Acronym: "MED",
				Enabled: true,
			},
			wantErr: true,
			errMsg:  "area label is required",
		},
		{
			name: "Empty acronym",
			area: &entity.Area{
				ID:      validID,
				Label:   "Medicine",
				Acronym: "",
				Enabled: true,
			},
			wantErr: true,
			errMsg:  "area acronym is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validationService.ValidateBusinessRules(ctx, tt.area)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAreaValidationService_ValidateUpsertInput(t *testing.T) {
	validationService := service.NewAreaValidationService()
	ctx := context.Background()
	validID := core.NewID()

	tests := []struct {
		name    string
		input   *service.UpsertValidationInput
		wantErr bool
		errMsg  string
	}{
		{
			name: "Valid input with ID",
			input: &service.UpsertValidationInput{
				ID:      core.ToPtr(validID.Value()),
				Label:   "Medicine",
				Acronym: "MED",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name: "Valid input without ID",
			input: &service.UpsertValidationInput{
				Label:   "Dentistry",
				Acronym: "DENT",
				Enabled: true,
			},
			wantErr: false,
		},
		{
			name:    "Nil input",
			input:   nil,
			wantErr: true,
			errMsg:  "upsert input is required",
		},
		{
			name: "Empty label",
			input: &service.UpsertValidationInput{
				Label:   "",
				Acronym: "MED",
				Enabled: true,
			},
			wantErr: true,
			errMsg:  "area label is required",
		},
		{
			name: "Empty acronym",
			input: &service.UpsertValidationInput{
				Label:   "Medicine",
				Acronym: "",
				Enabled: true,
			},
			wantErr: true,
			errMsg:  "area acronym is required",
		},
		{
			name: "Invalid UUID format",
			input: &service.UpsertValidationInput{
				ID:      core.ToPtr("invalid-uuid-format"),
				Label:   "Medicine",
				Acronym: "MED",
				Enabled: true,
			},
			wantErr: true,
			errMsg:  "invalid uuid format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validationService.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNewAreaValidationService(t *testing.T) {
	validationService := service.NewAreaValidationService()
	assert.NotNil(t, validationService)
	assert.Implements(t, (*service.AreaValidationService)(nil), validationService)
}
