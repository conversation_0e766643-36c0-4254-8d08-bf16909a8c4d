package area

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/area/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/area/event"
	"gitlab.viswalslab.com/backend/price-list/internal/area/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/area/service"
	"gitlab.viswalslab.com/backend/price-list/internal/area/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type Factory struct {
	AreaEventListener *event.AreaEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase *usecase.UpsertAreaUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertAreaInput) (*usecase.UpsertAreaOutput, error) {
	// Direct delegation to usecase - no conversion needed since types are the same
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration, logger vlog.Logger) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewAreaValidationService()

	// Initialize repository for upsert operations
	areaUpsertRepo := repository.NewAreaUpsertRepository(db, logger)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertAreaUsecase(areaUpsertRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	// Initialize event listener
	eventListener, err := event.NewAreaEventListener(cfg, adapter)
	if err != nil {
		return nil, err
	}

	return &Factory{
		AreaEventListener: eventListener,
	}, nil
}

func NewAreaHandlerFactory(db *sqlx.DB, logger vlog.Logger) *transport.AreaHandler {
	repo := repository.NewAreaRepository(db, logger)
	uc := usecase.NewAreaUsecase(repo)
	return &transport.AreaHandler{Usecase: uc, Logger: logger}
}

