package transport

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type AreaResponse struct {
	ID      string `json:"id"`
	Label   string `json:"label"`
	Enabled bool   `json:"enabled"`
	Acronym string `json:"acronym"`
}

type ListAreasOutput struct {
	Status     bool                       `json:"status"`
	Message    string                     `json:"message"`
	Data       []AreaResponse             `json:"data"`
	Pagination *ListAreasOutputPagination `json:"pagination,omitempty"`
}

type GetAreaResponse struct {
	Status  bool          `json:"status"`
	Message string        `json:"message"`
	Data    *AreaResponse `json:"data"`
}

type ListAreasOutputPagination struct {
	CurrentPage int `json:"current_page"`
	Total       int `json:"total"`
}

type ListAreasRequest struct {
	Page      int
	Size      int
	Enabled   *bool
	Acronym   *string
	Label     *string
	OrderBy   string
	Direction string
}

type AreaUsecase interface {
	ListAreas(ctx context.Context, params ListAreasRequest) ([]AreaResponse, *ListAreasOutputPagination, error)
	GetArea(ctx context.Context, id string) (*AreaResponse, error)
}

type AreaHandler struct {
	Usecase AreaUsecase
	Logger  vlog.Logger
}

func ToAreaResponse(area entity.Area) AreaResponse {
	return AreaResponse{
		ID:      area.ID.Value(),
		Label:   area.Label,
		Enabled: area.Enabled,
		Acronym: area.Acronym,
	}
}
