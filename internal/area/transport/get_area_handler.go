package transport

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// GetArea handles GET /areas/:areaId
func (h *AreaHandler) GetArea(c echo.Context) error {
	areaId := c.Param("areaId")
	if areaId == "" {
		h.Logger.Error("GetAreaHandler: areaId is required", vlog.F("error", "missing areaId"))
		return echo.NewHTTPError(http.StatusBadRequest, "areaId is required")
	}

	if _, err := core.NewIDFromString(areaId); err != nil {
		h.Logger.<PERSON>rror("GetAreaHandler: invalid areaId format", vlog.F("error", err), vlog.F("areaId", areaId))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid areaId format")
	}
	area, err := h.Usecase.GetArea(c.Request().Context(), areaId)
	if err != nil {
		h.Logger.Error("GetAreaHandler: area not found", vlog.F("error", err), vlog.F("areaId", areaId))
		return echo.NewHTTPError(http.StatusNotFound, err.Error())
	}
	return c.JSON(http.StatusOK, GetAreaResponse{
		Status:  true,
		Message: "",
		Data:    area,
	})
}
