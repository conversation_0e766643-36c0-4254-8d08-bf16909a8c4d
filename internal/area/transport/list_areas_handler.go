package transport

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (h *AreaHandler) ListAreas(c echo.Context) error {
	page, err := strconv.Atoi(c.Query<PERSON>aram("page"))
	if err != nil || page < 0 {
		page = 0
	}
	size, err := strconv.Atoi(c.QueryParam("size"))
	if err != nil || size <= 0 {
		size = 10
	}
	if size > 100 {
		size = 100
	}
	orderBy := c.QueryParam("orderBy")
	direction := c.QueryParam("direction")
	acronym := c.QueryParam("acronym")
	label := c.Query<PERSON>aram("label")
	enabledStr := c.QueryParam("enabled")
	var enabled *bool
	if enabledStr != "" {
		val, err := strconv.ParseBool(enabledStr)
		if err == nil {
			enabled = &val
		}
	}

	params := ListAreasRequest{
		Page:      page,
		Size:      size,
		OrderBy:   orderBy,
		Direction: direction,
		Acronym:   &acronym,
		Label:     &label,
		Enabled:   enabled,
	}

	areas, pagination, err := h.Usecase.ListAreas(c.Request().Context(), params)
	if err != nil {
		h.Logger.Error("ListAreasHandler: failed to list areas", vlog.F("error", err))
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, ListAreasOutput{
		Status:     true,
		Message:    "",
		Data:       areas,
		Pagination: pagination,
	})
}
