package transport

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestAreaHandler_GetArea(t *testing.T) {
	e := echo.New()

	t.Run("successful get area", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		expectedArea := &AreaResponse{
			ID: areaID, Label: "Medicine", Enabled: true, Acronym: "MED",
		}

		mockUsecase.On("GetArea", mock.Anything, areaID).Return(expectedArea, nil)

		req := httptest.NewRequest(http.MethodGet, "/areas/"+areaID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.Set<PERSON>aram<PERSON>ames("areaId")
		c.SetParamValues(areaID)

		err := handler.GetArea(c)
		require.NoError(t, err)

		assert.Equal(t, http.StatusOK, rec.Code)

		var response GetAreaResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.True(t, response.Status)
		assert.Equal(t, expectedArea, response.Data)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("missing areaId param", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		req := httptest.NewRequest(http.MethodGet, "/areas/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.GetArea(c)
		require.Error(t, err)

		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "areaId is required")
	})

	t.Run("invalid UUID format", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		invalidID := "invalid-uuid"
		req := httptest.NewRequest(http.MethodGet, "/areas/"+invalidID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("areaId")
		c.SetParamValues(invalidID)

		err := handler.GetArea(c)
		require.Error(t, err)

		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "Invalid areaId format")
	})

	t.Run("area not found", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		areaID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		mockUsecase.On("GetArea", mock.Anything, areaID).Return(nil, assert.AnError)

		req := httptest.NewRequest(http.MethodGet, "/areas/"+areaID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("areaId")
		c.SetParamValues(areaID)

		err := handler.GetArea(c)
		require.Error(t, err)

		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusNotFound, httpErr.Code)
	})
}
