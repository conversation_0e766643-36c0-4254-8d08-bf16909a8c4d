package transport

import (
	"context"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

func TestAreaHandler_ListAreas_Split(t *testing.T) {
	e := echo.New()
	req := httptest.NewRequest("GET", "/areas", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	h := &AreaHandler{Usecase: &mockAreaUsecase{}}
	err := h.ListAreas(c)
	assert.NoError(t, err)
	assert.Equal(t, 200, rec.Code)
}

type mockAreaUsecase struct{}

func (m *mockAreaUsecase) ListAreas(ctx context.Context, params ListAreasRequest) ([]AreaResponse, *ListAreasOutputPagination, error) {
	return []AreaResponse{}, &ListAreasOutputPagination{CurrentPage: 0, Total: 0}, nil
}
func (m *mockAreaUsecase) GetArea(ctx context.Context, id string) (*AreaResponse, error) {
	return nil, nil
}
