package transport

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/area/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// MockAreaUsecase is a mock implementation of AreaUsecase
type MockAreaUsecase struct {
	mock.Mock
}

func MustParse(v string) *core.Identifier {
	id, err := core.NewIDFromString(v)
	if err != nil {
		return nil
	}
	return id
}

func (m *MockAreaUsecase) ListAreas(ctx context.Context, params ListAreasRequest) ([]AreaResponse, *ListAreasOutputPagination, error) {
	args := m.Called(ctx, params)
	return args.Get(0).([]AreaResponse), args.Get(1).(*ListAreasOutputPagination), args.Error(2)
}

func (m *MockAreaUsecase) GetArea(ctx context.Context, id string) (*AreaResponse, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AreaResponse), args.Error(1)
}

func TestAreaHandler_ListAreas(t *testing.T) {
	e := echo.New()

	t.Run("successful list with default params", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		expectedAreas := []AreaResponse{
			{ID: "84ce4d70-a442-412b-bf27-06f4544a8661", Label: "Medicine", Enabled: true, Acronym: "MED"},
			{ID: "4beed17b-a38a-4da1-8b26-94d2f1513001", Label: "Dentistry", Enabled: true, Acronym: "DENT"},
		}
		expectedPagination := &ListAreasOutputPagination{CurrentPage: 0, Total: 2}

		mockUsecase.On("ListAreas", mock.Anything, mock.MatchedBy(func(params ListAreasRequest) bool {
			return params.Page == 0 && params.Size == 10
		})).Return(expectedAreas, expectedPagination, nil)

		req := httptest.NewRequest(http.MethodGet, "/areas", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.ListAreas(c)
		require.NoError(t, err)

		assert.Equal(t, http.StatusOK, rec.Code)

		var response ListAreasOutput
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.True(t, response.Status)
		assert.Equal(t, expectedAreas, response.Data)
		assert.Equal(t, expectedPagination, response.Pagination)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("successful list with query params", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		expectedAreas := []AreaResponse{
			{ID: "84ce4d70-a442-412b-bf27-06f4544a8661", Label: "Medicine", Enabled: true, Acronym: "MED"},
		}
		expectedPagination := &ListAreasOutputPagination{CurrentPage: 1, Total: 1}

		mockUsecase.On("ListAreas", mock.Anything, mock.MatchedBy(func(params ListAreasRequest) bool {
			return params.Page == 1 && params.Size == 5 &&
				*params.Acronym == "MED" && *params.Label == "Med" &&
				*params.Enabled == true && params.OrderBy == "label" && params.Direction == "ASC"
		})).Return(expectedAreas, expectedPagination, nil)

		req := httptest.NewRequest(http.MethodGet, "/areas?page=1&size=5&acronym=MED&label=Med&enabled=true&orderBy=label&direction=ASC", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.ListAreas(c)
		require.NoError(t, err)

		assert.Equal(t, http.StatusOK, rec.Code)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("invalid page param defaults to 0", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		mockUsecase.On("ListAreas", mock.Anything, mock.MatchedBy(func(params ListAreasRequest) bool {
			return params.Page == 0
		})).Return([]AreaResponse{}, &ListAreasOutputPagination{}, nil)

		req := httptest.NewRequest(http.MethodGet, "/areas?page=invalid", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.ListAreas(c)
		require.NoError(t, err)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("invalid size param defaults to 10", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		mockUsecase.On("ListAreas", mock.Anything, mock.MatchedBy(func(params ListAreasRequest) bool {
			return params.Size == 10
		})).Return([]AreaResponse{}, &ListAreasOutputPagination{}, nil)

		req := httptest.NewRequest(http.MethodGet, "/areas?size=invalid", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.ListAreas(c)
		require.NoError(t, err)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("size exceeds max limit", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		mockUsecase.On("ListAreas", mock.Anything, mock.MatchedBy(func(params ListAreasRequest) bool {
			return params.Size == 100
		})).Return([]AreaResponse{}, &ListAreasOutputPagination{}, nil)

		req := httptest.NewRequest(http.MethodGet, "/areas?size=200", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.ListAreas(c)
		require.NoError(t, err)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("usecase returns error", func(t *testing.T) {
		mockUsecase := new(MockAreaUsecase)
		handler := &AreaHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		mockUsecase.On("ListAreas", mock.Anything, mock.Anything).Return([]AreaResponse{}, (*ListAreasOutputPagination)(nil), errors.New("database error"))

		req := httptest.NewRequest(http.MethodGet, "/areas", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.ListAreas(c)
		require.Error(t, err)

		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusInternalServerError, httpErr.Code)
		mockUsecase.AssertExpectations(t)
	})
}

func TestToAreaResponse(t *testing.T) {
	t.Run("converts entity to Payload correctly", func(t *testing.T) {
		id := MustParse("84ce4d70-a442-412b-bf27-06f4544a8661")
		area := entity.Area{
			ID:      *id,
			Label:   "Medicine",
			Enabled: true,
			Acronym: "MED",
		}

		Payload := ToAreaResponse(area)

		assert.Equal(t, id.Value(), Payload.ID)
		assert.Equal(t, "Medicine", Payload.Label)
		assert.True(t, Payload.Enabled)
		assert.Equal(t, "MED", Payload.Acronym)
	})
}
