package repository

import (
	"context"
	"time"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type AppointmentTypeRepository interface {
	GetAll(ctx context.Context, input transport.ListAppointmentTypesRequest) ([]entity.AppointmentType, int64, error)
	GetByID(ctx context.Context, id string) (*entity.AppointmentType, error)
}

type appointmentTypeRepository struct {
	db     *sqlx.DB
	logger vlog.Logger
}

type appointmentTypeModel struct {
	UUID            string    `db:"uuid"`
	AppointmentType string    `db:"appointment_type"`
	Enabled         bool      `db:"enabled"`
	CreatedAt       time.Time `db:"created_at"`
	UpdatedAt       time.Time `db:"updated_at"`
}

func NewAppointmentTypeRepository(db *sqlx.DB, logger vlog.Logger) AppointmentTypeRepository {
	return &appointmentTypeRepository{db: db, logger: logger}
}
