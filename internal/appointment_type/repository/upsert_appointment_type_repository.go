package repository

import (
	"context"
	"fmt"
	"strings"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// AppointmentTypeNameExistsError represents an error when appointment type name already exists
type AppointmentTypeNameExistsError struct {
	Name string
}

func (e *AppointmentTypeNameExistsError) Error() string {
	return fmt.Sprintf("appointment type name '%s' already exists", e.Name)
}

// appointmentTypeUpsertRepository implements AppointmentTypeUpsertRepository
type appointmentTypeUpsertRepository struct {
	db *sqlx.DB
}

// NewAppointmentTypeUpsertRepository creates a new appointment type upsert repository
func NewAppointmentTypeUpsertRepository(db *sqlx.DB) *appointmentTypeUpsertRepository {
	return &appointmentTypeUpsertRepository{db: db}
}

// UpsertAppointmentType inserts or updates an appointment type
func (r *appointmentTypeUpsertRepository) UpsertAppointmentType(ctx context.Context, appointmentType *entity.AppointmentType) error {
	logger := vlog.FromContext(ctx).With(vlog.F("repository", "AppointmentTypeUpsertRepository"), vlog.F("method", "UpsertAppointmentType"))

	query := `
		INSERT INTO public.appointment_type (uuid, appointment_type, enabled, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (uuid) 
		DO UPDATE SET 
			appointment_type = EXCLUDED.appointment_type,
			enabled = EXCLUDED.enabled,
			updated_at = EXCLUDED.updated_at
	`

	args := []interface{}{
		appointmentType.ID(),
		appointmentType.AppointmentType(),
		appointmentType.Enabled(),
		appointmentType.CreatedAt(),
		appointmentType.UpdatedAt(),
	}

	logger.Debug("executing upsert query", vlog.F("query", query), vlog.F("args", args))

	_, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		// Check if it's a name constraint violation
		if strings.Contains(err.Error(), "appointment_type_name_key") {
			logger.Error("appointment type name constraint violation", vlog.F("error", err), vlog.F("appointment_type", appointmentType.AppointmentType()))
			return &AppointmentTypeNameExistsError{Name: appointmentType.AppointmentType()}
		}

		logger.Error("failed to upsert appointment type", vlog.F("error", err))
		return fmt.Errorf("failed to upsert appointment type: %w", err)
	}

	logger.Info("appointment type upserted successfully", vlog.F("id", appointmentType.ID()), vlog.F("appointment_type", appointmentType.AppointmentType()))
	return nil
}
