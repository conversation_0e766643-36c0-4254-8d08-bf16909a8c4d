package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestAppointmentTypeRepository_GetByID(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAppointmentTypeRepository(db, vlog.NewWithLevel("error"))
	ctx := context.Background()
	columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
	apptTypeID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	row := sqlmock.NewRows(columns).
		AddRow(apptTypeID, "Consultation", true, now, now)
	mock.ExpectQuery(`SELECT \* FROM appointment_type WHERE uuid = \$1`).WithArgs(apptTypeID).WillReturnRows(row)

	apptType, err := repo.GetByID(ctx, apptTypeID)
	require.NoError(t, err)
	assert.NotNil(t, apptType)
	assert.Equal(t, apptTypeID, apptType.ID())
}
