package repository

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *appointmentTypeRepository) GetByID(ctx context.Context, id string) (*entity.AppointmentType, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetByID"), vlog.F("action", "fetch appointment type by id"))

	query := `SELECT * FROM appointment_type WHERE uuid = $1`
	args := []interface{}{id}

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	var dba appointmentTypeModel
	if err := r.db.GetContext(ctx, &dba, query, args...); err != nil {
		logger.Error("failed to get appointment type", vlog.F("error", err), vlog.F("id", id))
		return nil, err
	}

	coreID, err := core.NewIDFromString(dba.UUID)
	if err != nil {
		logger.Error("failed to parse ID from DB", vlog.F("error", err), vlog.F("db_id", dba.UUID))
		return nil, err
	}

	appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              coreID.Value(),
		AppointmentType: dba.AppointmentType,
		Enabled:         dba.Enabled,
		CreatedAt:       &dba.CreatedAt,
		UpdatedAt:       &dba.UpdatedAt,
	})
	if err != nil {
		logger.Error("failed to create appointment type entity", vlog.F("error", err))
		return nil, err
	}
	return appointmentType, nil
}
