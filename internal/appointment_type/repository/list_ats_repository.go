package repository

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (r *appointmentTypeRepository) GetAll(ctx context.Context, input transport.ListAppointmentTypesRequest) ([]entity.AppointmentType, int64, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("method", "GetAll"), vlog.F("action", "list appointment types"))

	query := "SELECT * FROM appointment_type WHERE 1=1"
	var args []interface{}
	argIdx := 1
	if input.AppointmentType != nil && *input.AppointmentType != "" {
		query += fmt.Sprintf(" AND appointment_type = $%d", argIdx)
		args = append(args, *input.AppointmentType)
		argIdx++
	}
	if input.Enabled != nil {
		query += fmt.Sprintf(" AND enabled = $%d", argIdx)
		args = append(args, *input.Enabled)
		argIdx++
	}
	query += " ORDER BY appointment_type ASC"

	logger.Debug("executing", vlog.F("query", query), vlog.F("args", args))

	rows, err := r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		logger.Error("appointmentTypeRepository.GetAll: failed to select appointment types", vlog.F("error", err))
		return nil, 0, err
	}
	defer rows.Close()

	var appointmentTypes []entity.AppointmentType
	for rows.Next() {
		var dba appointmentTypeModel
		if err := rows.StructScan(&dba); err != nil {
			logger.Error("appointmentTypeRepository.GetAll: failed to scan appointment type row", vlog.F("error", err))
			return nil, 0, err
		}
		id, err := core.NewIDFromString(dba.UUID)
		if err != nil {
			logger.Error("appointmentTypeRepository.GetAll: failed to parse ID", vlog.F("error", err), vlog.F("db_id", dba.UUID))
			return nil, 0, err
		}

		appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
			ID:              id.Value(),
			AppointmentType: dba.AppointmentType,
			Enabled:         dba.Enabled,
			CreatedAt:       &dba.CreatedAt,
			UpdatedAt:       &dba.UpdatedAt,
		})
		if err != nil {
			logger.Error("failed to create appointment type entity", vlog.F("error", err))
			return nil, 0, err
		}
		appointmentTypes = append(appointmentTypes, *appointmentType)
	}

	if err := rows.Err(); err != nil {
		logger.Error("rows iteration error", vlog.F("error", err))
		return nil, 0, err
	}

	// Count query
	countQuery := "SELECT COUNT(uuid) FROM appointment_type WHERE 1=1"
	countArgs := make([]interface{}, 0)
	countIdx := 1
	if input.AppointmentType != nil && *input.AppointmentType != "" {
		countQuery += fmt.Sprintf(" AND appointment_type = $%d", countIdx)
		countArgs = append(countArgs, *input.AppointmentType)
		countIdx++
	}
	if input.Enabled != nil {
		countQuery += fmt.Sprintf(" AND enabled = $%d", countIdx)
		countArgs = append(countArgs, *input.Enabled)
		countIdx++
	}
	logger.Debug("appointmentTypeRepository.GetAll: executing count", vlog.F("query", countQuery), vlog.F("args", countArgs))

	var total int64
	if err := r.db.GetContext(ctx, &total, countQuery, countArgs...); err != nil {
		logger.Error("appointmentTypeRepository.GetAll: failed to get total count", vlog.F("error", err))
		return nil, 0, err
	}

	return appointmentTypes, total, nil
}
