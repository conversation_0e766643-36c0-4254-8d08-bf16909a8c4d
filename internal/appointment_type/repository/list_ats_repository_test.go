package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func TestAppointmentTypeRepository_GetAll_Split(t *testing.T) {
	db, mock := setupMockDB(t)
	repo := NewAppointmentTypeRepository(db, vlog.NewWithLevel("error"))
	ctx := context.Background()
	columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
	now := time.Now()
	rows := sqlmock.NewRows(columns).
		AddRow("84ce4d70-a442-412b-bf27-06f4544a8661", "Consultation", true, now, now).
		AddRow("4beed17b-a38a-4da1-8b26-94d2f1513001", "Follow-up", true, now, now).
		AddRow("e117dcf1-4acd-499f-80d2-7c868f23d6d0", "Surgery", false, now, now)
	mock.ExpectQuery(`SELECT \* FROM appointment_type WHERE 1=1 ORDER BY appointment_type ASC`).WillReturnRows(rows)
	mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM appointment_type WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(3))

	input := transport.ListAppointmentTypesRequest{}
	types, total, err := repo.GetAll(ctx, input)
	require.NoError(t, err)
	assert.Equal(t, int64(3), total)
	assert.Len(t, types, 3)
}