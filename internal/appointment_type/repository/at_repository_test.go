package repository

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

const (
	consultationID = "84ce4d70-a442-412b-bf27-06f4544a8661"
	followupID     = "4beed17b-a38a-4da1-8b26-94d2f1513001"
)

type testAppointmentType struct {
	UUID            string
	AppointmentType string
	Enabled         bool
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewAppointmentTypeRepository(t *testing.T) {
	db, _ := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAppointmentTypeRepository(db, logger)
	assert.NotNil(t, repo)
	assert.Implements(t, (*AppointmentTypeRepository)(nil), repo)
}

func TestAppointmentTypeRepository_GetAll(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAppointmentTypeRepository(db, logger)
	ctx := context.Background()
	columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
	now := time.Now()

	t.Run("get all appointment types without filters", func(t *testing.T) {
		rows := sqlmock.NewRows(columns).
			AddRow(consultationID, "Consultation", true, now, now).
			AddRow(followupID, "Follow-up", false, now, now)
		mock.ExpectQuery(`SELECT \* FROM appointment_type WHERE 1=1 ORDER BY appointment_type ASC`).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM appointment_type WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
		input := transport.ListAppointmentTypesRequest{}
		types, total, err := repo.GetAll(ctx, input)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, types, 2)
		require.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("filter by enabled status", func(t *testing.T) {
		enabled := true
		rows := sqlmock.NewRows(columns).
			AddRow(consultationID, "Consultation", true, now, now)
		mock.ExpectQuery(`SELECT \* FROM appointment_type WHERE 1=1 AND enabled = \$1 ORDER BY appointment_type ASC`).WithArgs(enabled).WillReturnRows(rows)
		mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM appointment_type WHERE 1=1 AND enabled = \$1`).WithArgs(enabled).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))
		input := transport.ListAppointmentTypesRequest{Enabled: &enabled}
		types, total, err := repo.GetAll(ctx, input)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, types, 1)
		assert.True(t, types[0].Enabled())
		require.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestAppointmentTypeRepository_GetByID_Split(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAppointmentTypeRepository(db, logger)
	ctx := context.Background()
	columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
	apptTypeID := "84ce4d70-a442-412b-bf27-06f4544a8661"
	now := time.Now()
	row := sqlmock.NewRows(columns).
		AddRow(apptTypeID, "Consultation", true, now, now)
	mock.ExpectQuery(`SELECT \* FROM appointment_type WHERE uuid = \$1`).WithArgs(apptTypeID).WillReturnRows(row)

	apptType, err := repo.GetByID(ctx, apptTypeID)
	require.NoError(t, err)
	assert.NotNil(t, apptType)
	assert.Equal(t, apptTypeID, apptType.ID())
}

func TestAppointmentTypeRepository_DatabaseErrors(t *testing.T) {
	t.Run("closed database connection", func(t *testing.T) {
		db, _ := setupMockDB(t)
		logger := vlog.NewWithLevel("error")
		repo := NewAppointmentTypeRepository(db, logger)
		ctx := context.Background()

		db.Close()

		_, _, err := repo.GetAll(ctx, transport.ListAppointmentTypesRequest{})
		assert.Error(t, err)

		_, err = repo.GetByID(ctx, consultationID)
		assert.Error(t, err)
	})
}

func TestAppointmentTypeRepository_ContextCancellation(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAppointmentTypeRepository(db, logger)

	t.Run("cancelled context for GetAll", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel()
		mock.ExpectQuery(`SELECT \* FROM appointment_type WHERE 1=1 ORDER BY appointment_type ASC`).WithArgs().WillReturnError(context.Canceled)
		_, _, err := repo.GetAll(ctx, transport.ListAppointmentTypesRequest{})
		if err != nil {
			assert.Contains(t, err.Error(), "context")
		}
	})

	t.Run("cancelled context for GetByID", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel()
		mock.ExpectQuery(`SELECT uuid FROM appointment_type WHERE uuid = \$1`).WithArgs(sqlmock.AnyArg()).WillReturnError(context.Canceled)
		_, err := repo.GetByID(ctx, consultationID)
		if err != nil {
			assert.Contains(t, err.Error(), "context")
		}
	})
}

func TestAppointmentTypeRepository_NilPointerHandling(t *testing.T) {
	db, mock := setupMockDB(t)
	logger := vlog.NewWithLevel("error")
	repo := NewAppointmentTypeRepository(db, logger)
	ctx := context.Background()

	t.Run("nil filter pointers are handled correctly", func(t *testing.T) {
		input := transport.ListAppointmentTypesRequest{
			Enabled: nil,
		}
		columns := []string{"uuid", "appointment_type", "enabled", "created_at", "updated_at"}
		now := time.Now()
		mock.ExpectQuery(`SELECT \* FROM appointment_type WHERE 1=1 ORDER BY appointment_type ASC`).WithArgs().WillReturnRows(sqlmock.NewRows(columns).
			AddRow(consultationID, "Consultation", true, now, now).
			AddRow(followupID, "Follow-up", false, now, now))
		mock.ExpectQuery(`SELECT COUNT\(uuid\) FROM appointment_type WHERE 1=1`).WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

		types, total, err := repo.GetAll(ctx, input)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(0))
		assert.NotNil(t, types)
	})
}
