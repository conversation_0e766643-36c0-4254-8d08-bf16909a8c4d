package repository_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/repository"
)

func setupTestDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewAppointmentTypeUpsertRepository(t *testing.T) {
	db, _ := setupTestDB(t)
	defer db.Close()

	repo := repository.NewAppointmentTypeUpsertRepository(db)

	assert.NotNil(t, repo)
}

func TestAppointmentTypeRepository_UpsertAppointmentType(t *testing.T) {
	validID := core.NewID()
	now := time.Now().UTC()

	// Create a test appointment type entity
	appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Consultation",
		Enabled:         true,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	})
	require.NoError(t, err)

	tests := []struct {
		name            string
		appointmentType *entity.AppointmentType
		setupMock       func(sqlmock.Sqlmock)
		wantErr         bool
		wantErrMessage  string
	}{
		{
			name:            "Successful upsert",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				expectedQuery := `INSERT INTO public\.appointment_type.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "Consultation", true, now, now).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name:            "Database error",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				expectedQuery := `INSERT INTO public\.appointment_type.*ON CONFLICT.*`
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "Consultation", true, now, now).
					WillReturnError(errors.New("database connection error"))
			},
			wantErr:        true,
			wantErrMessage: "database connection error",
		},
		{
			name:            "Name constraint violation",
			appointmentType: appointmentType,
			setupMock: func(mock sqlmock.Sqlmock) {
				expectedQuery := `INSERT INTO public\.appointment_type.*ON CONFLICT.*`
				constraintErr := errors.New(`pq: duplicate key value violates unique constraint "appointment_type_name_key"`)
				mock.ExpectExec(expectedQuery).
					WithArgs(validID.Value(), "Consultation", true, now, now).
					WillReturnError(constraintErr)
			},
			wantErr:        true,
			wantErrMessage: "appointment type name 'Consultation' already exists",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock := setupTestDB(t)
			defer db.Close()

			tt.setupMock(mock)

			repo := repository.NewAppointmentTypeUpsertRepository(db)
			err := repo.UpsertAppointmentType(context.Background(), tt.appointmentType)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			require.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestAppointmentTypeRepository_UpsertAppointmentType_DisabledStatus(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create a disabled appointment type entity
	appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Cancelled",
		Enabled:         false,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	})
	require.NoError(t, err)

	expectedQuery := `INSERT INTO public\.appointment_type.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "Cancelled", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewAppointmentTypeUpsertRepository(db)
	err = repo.UpsertAppointmentType(context.Background(), appointmentType)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}

func TestAppointmentTypeRepository_UpsertAppointmentType_UpdateExisting(t *testing.T) {
	db, mock := setupTestDB(t)
	defer db.Close()

	validID := core.NewID()
	now := time.Now().UTC()

	// Create an appointment type entity for update
	appointmentType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Updated Follow-up",
		Enabled:         false,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	})
	require.NoError(t, err)

	// Mock expects the upsert query with ON CONFLICT DO UPDATE
	expectedQuery := `INSERT INTO public\.appointment_type.*ON CONFLICT.*`
	mock.ExpectExec(expectedQuery).
		WithArgs(validID.Value(), "Updated Follow-up", false, now, now).
		WillReturnResult(sqlmock.NewResult(1, 1))

	repo := repository.NewAppointmentTypeUpsertRepository(db)
	err = repo.UpsertAppointmentType(context.Background(), appointmentType)

	assert.NoError(t, err)
	require.NoError(t, mock.ExpectationsWereMet())
}
