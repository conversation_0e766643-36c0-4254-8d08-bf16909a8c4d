package service

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
)

// UpsertValidationInput represents input for upsert validation
type UpsertValidationInput struct {
	ID              *string
	AppointmentType string
	Enabled         bool
}

// AppointmentTypeValidationService defines the interface for appointment type validation
type AppointmentTypeValidationService interface {
	ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error
	ValidateBusinessRules(ctx context.Context, appointmentType *entity.AppointmentType) error
}

// appointmentTypeValidationService implements the validation service
type appointmentTypeValidationService struct{}

// NewAppointmentTypeValidationService creates a new validation service
func NewAppointmentTypeValidationService() AppointmentTypeValidationService {
	return &appointmentTypeValidationService{}
}

// ValidateUpsertInput validates the upsert input
func (s *appointmentTypeValidationService) ValidateUpsertInput(ctx context.Context, input *UpsertValidationInput) error {
	if input == nil {
		return core.NewBusinessError("validation input is required")
	}

	// Validate required fields
	if input.AppointmentType == "" {
		return core.NewBusinessError("appointment type name is required")
	}

	// Validate UUID format if provided
	if input.ID != nil && *input.ID != "" {
		if _, err := core.NewIDFromString(*input.ID); err != nil {
			return core.NewBusinessError("invalid UUID format: %v", err)
		}
	}

	return nil
}

// ValidateBusinessRules validates business rules for the appointment type entity
func (s *appointmentTypeValidationService) ValidateBusinessRules(ctx context.Context, appointmentType *entity.AppointmentType) error {
	if appointmentType == nil {
		return core.NewBusinessError("appointment type entity is required")
	}

	// Validate entity fields
	if appointmentType.ID() == "" {
		return core.NewBusinessError("appointment type ID is required")
	}
	if appointmentType.AppointmentType() == "" {
		return core.NewBusinessError("appointment type name is required")
	}

	return nil
}
