package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
)

func TestNewAppointmentTypeValidationService(t *testing.T) {
	service := NewAppointmentTypeValidationService()
	assert.NotNil(t, service)
	assert.Implements(t, (*AppointmentTypeValidationService)(nil), service)
}

func TestValidateBusinessRules(t *testing.T) {
	service := NewAppointmentTypeValidationService()
	ctx := context.Background()

	validID := core.NewID()

	tests := []struct {
		name               string
		setupEntity        func() *entity.AppointmentType
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid entity",
			setupEntity: func() *entity.AppointmentType {
				input := &entity.NewAppointmentTypeInput{
					ID:              validID.Value(),
					AppointmentType: "Consultation",
					Enabled:         true,
				}
				at, _ := entity.NewAppointmentType(input)
				return at
			},
			wantErr: false,
		},
		{
			name: "Valid disabled entity",
			setupEntity: func() *entity.AppointmentType {
				input := &entity.NewAppointmentTypeInput{
					ID:              validID.Value(),
					AppointmentType: "Cancelled",
					Enabled:         false,
				}
				at, _ := entity.NewAppointmentType(input)
				return at
			},
			wantErr: false,
		},
		{
			name: "Nil entity",
			setupEntity: func() *entity.AppointmentType {
				return nil
			},
			wantErr:            true,
			expectedErrMessage: "appointment type entity is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := tt.setupEntity()
			err := service.ValidateBusinessRules(ctx, entity)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpsertInput(t *testing.T) {
	service := NewAppointmentTypeValidationService()
	ctx := context.Background()

	validID := core.NewID()

	tests := []struct {
		name               string
		input              *UpsertValidationInput
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Valid input with ID",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "Consultation",
				Enabled:         true,
			},
			wantErr: false,
		},
		{
			name: "Valid input without ID",
			input: &UpsertValidationInput{
				AppointmentType: "Follow-up",
				Enabled:         true,
			},
			wantErr: false,
		},
		{
			name: "Valid disabled appointment type",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "Cancelled",
				Enabled:         false,
			},
			wantErr: false,
		},
		{
			name:               "Nil input",
			input:              nil,
			wantErr:            true,
			expectedErrMessage: "validation input is required",
		},
		{
			name: "Empty appointment type name",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "",
				Enabled:         true,
			},
			wantErr:            true,
			expectedErrMessage: "appointment type name is required",
		},
		{
			name: "Invalid ID format",
			input: &UpsertValidationInput{
				ID:              core.ToPtr("invalid-uuid"),
				AppointmentType: "Consultation",
				Enabled:         true,
			},
			wantErr:            true,
			expectedErrMessage: "invalid UUID format",
		},
		{
			name: "Empty ID string",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(""),
				AppointmentType: "Consultation",
				Enabled:         true,
			},
			wantErr: false, // Empty string ID is allowed (will be auto-generated)
		},
		{
			name: "Valid input with special characters in name",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "Follow-up Visit",
				Enabled:         true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with spaces in name",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "Emergency Room Visit",
				Enabled:         true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with numbers in name",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "Type-1 Consultation",
				Enabled:         true,
			},
			wantErr: false,
		},
		{
			name: "Valid long name",
			input: &UpsertValidationInput{
				ID:              core.ToPtr(validID.Value()),
				AppointmentType: "Very Long Appointment Type Name That Should Still Be Valid For Testing Purposes",
				Enabled:         true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpsertInput_EdgeCases(t *testing.T) {
	service := NewAppointmentTypeValidationService()
	ctx := context.Background()

	tests := []struct {
		name               string
		input              *UpsertValidationInput
		wantErr            bool
		expectedErrMessage string
	}{
		{
			name: "Whitespace only name - allowed by current validation",
			input: &UpsertValidationInput{
				AppointmentType: "   ",
				Enabled:         true,
			},
			wantErr: false, // Current validation only checks for empty string, not whitespace
		},
		{
			name: "Tab and newline in name - allowed by current validation",
			input: &UpsertValidationInput{
				AppointmentType: "\t\n",
				Enabled:         true,
			},
			wantErr: false, // Current validation only checks for empty string, not whitespace
		},
		{
			name: "Single space name - allowed by current validation",
			input: &UpsertValidationInput{
				AppointmentType: " ",
				Enabled:         true,
			},
			wantErr: false, // Current validation only checks for empty string, not whitespace
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateUpsertInput(ctx, tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErrMessage != "" {
					assert.Contains(t, err.Error(), tt.expectedErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateBusinessRules_EntityValidation(t *testing.T) {
	service := NewAppointmentTypeValidationService()
	ctx := context.Background()

	validID := core.NewID()

	// Test with valid entity
	input := &entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Routine Check-up",
		Enabled:         true,
	}
	appointmentType, err := entity.NewAppointmentType(input)
	assert.NoError(t, err)

	err = service.ValidateBusinessRules(ctx, appointmentType)
	assert.NoError(t, err)

	// Test with disabled entity
	disabledInput := &entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Cancelled",
		Enabled:         false,
	}
	disabledType, err := entity.NewAppointmentType(disabledInput)
	assert.NoError(t, err)

	err = service.ValidateBusinessRules(ctx, disabledType)
	assert.NoError(t, err)
}
