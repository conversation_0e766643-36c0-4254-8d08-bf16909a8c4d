package usecase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
)

type mockRepo struct{}

func (m *mockRepo) GetAll(ctx context.Context, input transport.ListAppointmentTypesRequest) ([]entity.AppointmentType, int64, error) {
	validID := core.NewID()
	appointmentType, _ := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Test",
		Enabled:         true,
	})
	return []entity.AppointmentType{*appointmentType}, 1, nil
}
func (m *mockRepo) GetByID(ctx context.Context, id string) (*entity.AppointmentType, error) {
	validID := core.NewID()
	appointmentType, _ := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Test",
		Enabled:         true,
	})
	return appointmentType, nil
}

func TestAppointmentTypeUsecase_ListAppointmentTypes_Split(t *testing.T) {
	uc := &AppointmentTypeUsecase{repo: &mockRepo{}}
	ats, pagination, err := uc.ListAppointmentTypes(context.Background(), transport.ListAppointmentTypesRequest{})
	require.NoError(t, err)
	assert.NotNil(t, ats)
	assert.NotNil(t, pagination)
	assert.Equal(t, "Test", ats[0].AppointmentType)
}
