package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
)

type AppointmentTypeService interface {
	GetAppointmentType(ctx context.Context, id string) (*transport.AppointmentTypeResponse, error)
	ListAppointmentTypes(ctx context.Context, input transport.ListAppointmentTypesRequest) ([]transport.AppointmentTypeResponse, *transport.ListAppointmentTypesOutputPagination, error)
}

type AppointmentTypeUsecase struct {
	repo repository.AppointmentTypeRepository
}

func NewAppointmentTypeUsecase(repo repository.AppointmentTypeRepository) *AppointmentTypeUsecase {
	return &AppointmentTypeUsecase{repo: repo}
}
