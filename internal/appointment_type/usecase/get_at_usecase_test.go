package usecase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
)

type mockRepoGetAppointmentType struct{}

func (m *mockRepoGetAppointmentType) GetAll(ctx context.Context, input transport.ListAppointmentTypesRequest) ([]entity.AppointmentType, int64, error) {
	return nil, 0, nil
}
func (m *mockRepoGetAppointmentType) GetByID(ctx context.Context, id string) (*entity.AppointmentType, error) {
	validID := core.NewID()
	appointmentType, _ := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Test",
		Enabled:         true,
	})
	return appointmentType, nil
}

func TestAppointmentTypeUsecase_GetAppointmentType_Split(t *testing.T) {
	uc := &AppointmentTypeUsecase{repo: &mockRepoGetAppointmentType{}}
	at, err := uc.GetAppointmentType(context.Background(), "some-id")
	require.NoError(t, err)
	assert.NotNil(t, at)
	assert.Equal(t, "Test", at.AppointmentType)
}
