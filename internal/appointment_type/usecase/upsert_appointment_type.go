package usecase

import (
	"context"
	"errors"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/service"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

var upsertAppointmentTypeUsecaseName = "UpsertAppointmentTypeUsecase"

type AppointmentTypeUpsertRepository interface {
	UpsertAppointmentType(ctx context.Context, appointmentType *entity.AppointmentType) error
}

type UpsertAppointmentTypeInput struct {
	ID              *string
	AppointmentType string
	Enabled         bool
}

type UpsertAppointmentTypeOutput struct {
	ID              string
	AppointmentType string
	Enabled         bool
}

type UpsertAppointmentTypeUsecase struct {
	repo              AppointmentTypeUpsertRepository
	validationService service.AppointmentTypeValidationService
}

func NewUpsertAppointmentTypeUsecase(repo AppointmentTypeUpsertRepository, validationService service.AppointmentTypeValidationService) *UpsertAppointmentTypeUsecase {
	return &UpsertAppointmentTypeUsecase{
		repo:              repo,
		validationService: validationService,
	}
}

func (uc *UpsertAppointmentTypeUsecase) Execute(ctx context.Context, input *UpsertAppointmentTypeInput) (*UpsertAppointmentTypeOutput, error) {
	logger := vlog.FromContext(ctx).With(vlog.F("usecase", upsertAppointmentTypeUsecaseName))

	// Validate input
	if input == nil {
		logger.Error("input validation failed", vlog.F("error", "input is required"))
		return nil, core.NewBusinessError("input is required")
	}

	if input.AppointmentType == "" {
		logger.Error("input validation failed", vlog.F("error", "appointment type name is required"))
		return nil, core.NewBusinessError("appointment type name is required")
	}

	// Validate UUID format if provided
	var id core.Identifier
	if input.ID != nil && *input.ID != "" {
		parsedID, err := core.NewIDFromString(*input.ID)
		if err != nil {
			logger.Error("input validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *input.ID))
			return nil, core.NewBusinessError("invalid UUID format: %v", err)
		}
		id = *parsedID
	} else {
		// Generate new ID if not provided
		id = *core.NewID()
	}

	// Service validation
	validationInput := &service.UpsertValidationInput{
		ID:              input.ID,
		AppointmentType: input.AppointmentType,
		Enabled:         input.Enabled,
	}

	if err := uc.validationService.ValidateUpsertInput(ctx, validationInput); err != nil {
		logger.Error("service validation failed", vlog.F("error", err))
		return nil, err
	}

	// Create appointment type entity
	appointmentTypeEntity, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              id.Value(),
		AppointmentType: input.AppointmentType,
		Enabled:         input.Enabled,
	})
	if err != nil {
		logger.Error("failed to create appointment type entity", vlog.F("error", err))
		return nil, core.NewBusinessError("failed to create appointment type entity: %v", err)
	}

	// Business rules validation
	if err := uc.validationService.ValidateBusinessRules(ctx, appointmentTypeEntity); err != nil {
		logger.Error("business rules validation failed", vlog.F("error", err))
		return nil, err
	}

	// Upsert appointment type
	if err := uc.repo.UpsertAppointmentType(ctx, appointmentTypeEntity); err != nil {
		// Check if it's a name constraint violation
		var nameExistsErr *repository.AppointmentTypeNameExistsError
		if errors.As(err, &nameExistsErr) {
			logger.Error("appointment type name already exists", vlog.F("error", err), vlog.F("appointment_type", input.AppointmentType))
			return nil, core.NewBusinessError("Appointment type name '%s' already exists", input.AppointmentType)
		}

		logger.Error("database error occurred while upserting appointment type", vlog.F("error", err))
		return nil, core.NewBusinessError("database error occurred while upserting appointment type: %v", err)
	}

	logger.Info("appointment type upserted successfully", vlog.F("id", id.Value()), vlog.F("appointment_type", input.AppointmentType))

	return &UpsertAppointmentTypeOutput{
		ID:              id.Value(),
		AppointmentType: input.AppointmentType,
		Enabled:         input.Enabled,
	}, nil
}
