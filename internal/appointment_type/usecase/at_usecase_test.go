package usecase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
)

type MockAppointmentTypeRepository struct {
	mock.Mock
}

func (m *MockAppointmentTypeRepository) GetAll(ctx context.Context, input transport.ListAppointmentTypesRequest) ([]entity.AppointmentType, int64, error) {
	args := m.Called(ctx, input)
	return args.Get(0).([]entity.AppointmentType), args.Get(1).(int64), args.Error(2)
}

func (m *MockAppointmentTypeRepository) GetByID(ctx context.Context, id string) (*entity.AppointmentType, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.AppointmentType), args.Error(1)
}

func TestNewAppointmentTypeUsecase(t *testing.T) {
	mockRepo := new(MockAppointmentTypeRepository)
	usecase := NewAppointmentTypeUsecase(mockRepo)
	assert.NotNil(t, usecase)
	assert.Equal(t, mockRepo, usecase.repo)
	assert.Implements(t, (*AppointmentTypeService)(nil), usecase)
}
