package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
)

func (u *AppointmentTypeUsecase) ListAppointmentTypes(ctx context.Context, input transport.ListAppointmentTypesRequest) ([]transport.AppointmentTypeResponse, *transport.ListAppointmentTypesOutputPagination, error) {
	appointmentTypes, total, err := u.repo.GetAll(ctx, input)
	if err != nil {
		return nil, nil, err
	}
	Payloads := make([]transport.AppointmentTypeResponse, len(appointmentTypes))
	for i, appointmentType := range appointmentTypes {
		Payloads[i] = transport.ToAppointmentTypeResponse(appointmentType)
	}
	return Payloads, &transport.ListAppointmentTypesOutputPagination{CurrentPage: 0, Total: int(total)}, nil
}
