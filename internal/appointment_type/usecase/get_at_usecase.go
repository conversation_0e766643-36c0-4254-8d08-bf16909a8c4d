package usecase

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (u *AppointmentTypeUsecase) GetAppointmentType(ctx context.Context, id string) (*transport.AppointmentTypeResponse, error) {
	appointmentType, err := u.repo.GetByID(ctx, id)
	if err != nil {
		vlog.FromContext(ctx).Error("Failed to get appointment type by ID", vlog.F("id", id), vlog.F("error", err))
		return nil, err
	}
	Payload := transport.ToAppointmentTypeResponse(*appointmentType)
	return &Payload, nil
}
