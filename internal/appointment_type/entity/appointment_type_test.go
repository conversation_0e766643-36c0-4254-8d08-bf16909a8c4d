package entity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
)

var validID = core.NewID()

func TestNewAppointmentType(t *testing.T) {
	tests := []struct {
		name    string
		input   *NewAppointmentTypeInput
		wantErr bool
	}{
		{
			name: "Valid input with all required fields",
			input: &NewAppointmentTypeInput{
				ID:              validID.Value(),
				AppointmentType: "Consultation",
				Enabled:         true,
			},
			wantErr: false,
		},
		{
			name: "Valid input with disabled status",
			input: &NewAppointmentTypeInput{
				ID:              validID.Value(),
				AppointmentType: "Cancelled",
				Enabled:         false,
			},
			wantErr: false,
		},
		{
			name: "Valid input with timestamps",
			input: &NewAppointmentTypeInput{
				ID:              validID.Value(),
				AppointmentType: "Follow-up",
				Enabled:         true,
				CreatedAt:       &time.Time{},
				UpdatedAt:       &time.Time{},
			},
			wantErr: true, // Zero time should fail validation
		},
		{
			name: "Invalid ID format",
			input: &NewAppointmentTypeInput{
				ID:              "invalid-uuid",
				AppointmentType: "Consultation",
				Enabled:         true,
			},
			wantErr: true,
		},
		{
			name: "Empty ID",
			input: &NewAppointmentTypeInput{
				ID:              "",
				AppointmentType: "Consultation",
				Enabled:         true,
			},
			wantErr: true,
		},
		{
			name: "Empty appointment type name",
			input: &NewAppointmentTypeInput{
				ID:              validID.Value(),
				AppointmentType: "",
				Enabled:         true,
			},
			wantErr: true,
		},
		{
			name: "Valid input with long name",
			input: &NewAppointmentTypeInput{
				ID:              validID.Value(),
				AppointmentType: "Very Long Appointment Type Name That Should Still Be Valid",
				Enabled:         true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			appointmentType, err := NewAppointmentType(tt.input)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, appointmentType)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, appointmentType)

				// Verify all getter methods work correctly
				assert.Equal(t, tt.input.ID, appointmentType.ID())
				assert.Equal(t, tt.input.AppointmentType, appointmentType.AppointmentType())
				assert.Equal(t, tt.input.Enabled, appointmentType.Enabled())

				// Verify timestamps are set (either from input or default)
				assert.NotZero(t, appointmentType.CreatedAt())
				assert.NotZero(t, appointmentType.UpdatedAt())

				if tt.input.CreatedAt != nil && !tt.input.CreatedAt.IsZero() {
					assert.Equal(t, tt.input.CreatedAt.UTC(), appointmentType.CreatedAt().UTC())
				}
				if tt.input.UpdatedAt != nil && !tt.input.UpdatedAt.IsZero() {
					assert.Equal(t, tt.input.UpdatedAt.UTC(), appointmentType.UpdatedAt().UTC())
				}
			}
		})
	}
}

func TestNewAppointmentType_PanicOnNilInput(t *testing.T) {
	assert.Panics(t, func() {
		NewAppointmentType(nil)
	})
}

func TestAppointmentTypeGetters(t *testing.T) {
	now := time.Now()
	input := &NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Emergency",
		Enabled:         true,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	}

	appointmentType, err := NewAppointmentType(input)
	assert.NoError(t, err)
	assert.NotNil(t, appointmentType)

	// Test all getter methods
	assert.Equal(t, input.ID, appointmentType.ID())
	assert.Equal(t, input.AppointmentType, appointmentType.AppointmentType())
	assert.Equal(t, input.Enabled, appointmentType.Enabled())
	assert.Equal(t, now.UTC(), appointmentType.CreatedAt().UTC())
	assert.Equal(t, now.UTC(), appointmentType.UpdatedAt().UTC())
}

func TestAppointmentTypeWithDifferentTimestamps(t *testing.T) {
	createdTime := time.Now().Add(-time.Hour)
	updatedTime := time.Now()

	input := &NewAppointmentTypeInput{
		ID:              validID.Value(),
		AppointmentType: "Routine Check-up",
		Enabled:         true,
		CreatedAt:       &createdTime,
		UpdatedAt:       &updatedTime,
	}

	appointmentType, err := NewAppointmentType(input)
	assert.NoError(t, err)
	assert.NotNil(t, appointmentType)

	// Verify timestamps are preserved correctly
	assert.Equal(t, createdTime.UTC(), appointmentType.CreatedAt().UTC())
	assert.Equal(t, updatedTime.UTC(), appointmentType.UpdatedAt().UTC())
	assert.True(t, appointmentType.CreatedAt().Before(appointmentType.UpdatedAt()))
}
