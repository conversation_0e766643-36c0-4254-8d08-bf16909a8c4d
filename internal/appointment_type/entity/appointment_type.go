package entity

import (
	"time"

	"gitlab.viswalslab.com/backend/price-list/core"
)

// AppointmentType represents an appointment type entity
type AppointmentType struct {
	id              core.Identifier
	appointmentType string
	enabled         bool
	createdAt       core.Timestamp
	updatedAt       core.Timestamp
}

// NewAppointmentTypeInput represents the input for creating a new appointment type
type NewAppointmentTypeInput struct {
	ID              string
	AppointmentType string
	Enabled         bool
	CreatedAt       *time.Time
	UpdatedAt       *time.Time
}

// NewAppointmentType creates a new appointment type entity
func NewAppointmentType(input *NewAppointmentTypeInput) (*AppointmentType, error) {
	if input == nil {
		panic("NewAppointmentTypeInput cannot be nil")
	}

	// Parse and validate ID
	id, err := core.NewIDFromString(input.ID)
	if err != nil {
		return nil, err
	}

	// Validate required fields
	if input.AppointmentType == "" {
		return nil, core.NewBusinessError("appointment type name is required")
	}

	// Use provided timestamps or default to current time using core timestamp
	now := core.NewTimestamp().Value()
	createdTime := now
	if input.CreatedAt != nil {
		createdTime = *input.CreatedAt
	}
	updatedTime := now
	if input.UpdatedAt != nil {
		updatedTime = *input.UpdatedAt
	}

	createdTimestamp, err := core.NewTimestampFromTime(createdTime)
	if err != nil {
		return nil, core.NewBusinessError("invalid created timestamp: %v", err)
	}

	updatedTimestamp, err := core.NewTimestampFromTime(updatedTime)
	if err != nil {
		return nil, core.NewBusinessError("invalid updated timestamp: %v", err)
	}

	return &AppointmentType{
		id:              *id,
		appointmentType: input.AppointmentType,
		enabled:         input.Enabled,
		createdAt:       *createdTimestamp,
		updatedAt:       *updatedTimestamp,
	}, nil
}

// Getter methods
func (a *AppointmentType) ID() string {
	return a.id.Value()
}

func (a *AppointmentType) AppointmentType() string {
	return a.appointmentType
}

func (a *AppointmentType) Enabled() bool {
	return a.enabled
}

func (a *AppointmentType) CreatedAt() time.Time {
	return a.createdAt.Value()
}

func (a *AppointmentType) UpdatedAt() time.Time {
	return a.updatedAt.Value()
}
