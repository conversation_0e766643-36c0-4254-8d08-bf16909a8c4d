package appointment_type

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func setupMockDB(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	sqlxDB := sqlx.NewDb(db, "postgres")
	return sqlxDB, mock
}

func TestNewAppointmentTypeHandlerFactory(t *testing.T) {
	t.Run("creates handler with all dependencies wired", func(t *testing.T) {
		db, _ := setupMockDB(t)
		handler := NewAppointmentTypeHandlerFactory(db, vlog.NewWithLevel("error"))
		assert.NotNil(t, handler)
		assert.IsType(t, &transport.AppointmentTypeHandler{}, handler)
		assert.NotNil(t, handler.Usecase)
	})

	t.Run("handler is ready for use", func(t *testing.T) {
		db, _ := setupMockDB(t)
		handler := NewAppointmentTypeHandlerFactory(db, vlog.NewWithLevel("error"))
		assert.NotNil(t, handler)
		assert.IsType(t, &transport.AppointmentTypeHandler{}, handler)
		assert.NotNil(t, handler.Usecase)
	})

	t.Run("multiple factory calls create independent instances", func(t *testing.T) {
		db, _ := setupMockDB(t)
		handler1 := NewAppointmentTypeHandlerFactory(db, vlog.NewWithLevel("error"))
		handler2 := NewAppointmentTypeHandlerFactory(db, vlog.NewWithLevel("error"))
		assert.NotNil(t, handler1)
		assert.NotNil(t, handler2)
		assert.NotSame(t, handler1, handler2)
		assert.NotSame(t, handler1.Usecase, handler2.Usecase)
	})
}

func TestNewFactory(t *testing.T) {
	db, _ := setupMockDB(t)
	defer db.Close()

	// Skip this test as it requires real AWS credentials
	t.Skip("Skipping factory test that requires AWS credentials")

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLAppointmentType: "https://sqs.us-east-1.amazonaws.com/123456789/appointment-type-queue",
			Region:                     "us-east-1",
			AccessKeyID:                "test-access-key",
			SecretAccessKey:            "test-secret-key",
		},
	}

	factory, err := NewFactory(db, cfg)

	assert.NoError(t, err)
	assert.NotNil(t, factory)
	assert.NotNil(t, factory.AppointmentTypeEventListener)
}

func TestNewFactory_MissingQueueURL(t *testing.T) {
	db, _ := setupMockDB(t)
	defer db.Close()

	cfg := config.Configuration{
		AWS: config.AWSConfig{
			SQSQueueURLAppointmentType: "", // Missing queue URL
			Region:                     "us-east-1",
			AccessKeyID:                "test-access-key",
			SecretAccessKey:            "test-secret-key",
		},
	}

	factory, err := NewFactory(db, cfg)

	assert.Error(t, err)
	assert.Nil(t, factory)
	assert.Contains(t, err.Error(), "AWS_SQS_QUEUE_URL_APPOINTMENT_TYPE is required")
}
