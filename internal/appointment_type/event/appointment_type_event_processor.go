package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// UpsertAppointmentTypeUsecase defines the interface for appointment type upsert operations
type UpsertAppointmentTypeUsecase interface {
	Execute(ctx context.Context, input *usecase.UpsertAppointmentTypeInput) (*usecase.UpsertAppointmentTypeOutput, error)
}

// AppointmentTypeUpsertEventProcessor processes appointment type upsert events
type AppointmentTypeUpsertEventProcessor struct {
	upsertUsecase UpsertAppointmentTypeUsecase
}

// NewAppointmentTypeUpsertEventProcessor creates a new appointment type upsert event processor
func NewAppointmentTypeUpsertEventProcessor(upsertUsecase UpsertAppointmentTypeUsecase) *AppointmentTypeUpsertEventProcessor {
	return &AppointmentTypeUpsertEventProcessor{
		upsertUsecase: upsertUsecase,
	}
}

// ProcessMessage processes an appointment type event message
func (p *AppointmentTypeUpsertEventProcessor) ProcessMessage(ctx context.Context, message *AppointmentTypeEventMessage) error {
	logger := vlog.New().With(vlog.F("processor", "AppointmentTypeUpsertEventProcessor"))

	if message == nil {
		logger.Error("validation failed", vlog.F("error", "message is required"))
		return fmt.Errorf("message is required")
	}

	// Validate required fields
	if message.Data.AppointmentType == "" {
		logger.Error("validation failed", vlog.F("error", "appointment type name is required"))
		return fmt.Errorf("appointment type name is required in message data")
	}

	// Validate UUID format if provided
	if message.Data.ID != nil && *message.Data.ID != "" {
		if _, err := core.NewIDFromString(*message.Data.ID); err != nil {
			logger.Error("validation failed", vlog.F("error", "invalid UUID format"), vlog.F("id", *message.Data.ID))
			return fmt.Errorf("invalid UUID format in message data")
		}
	}

	// Convert message data to usecase input
	upsertInput := &usecase.UpsertAppointmentTypeInput{
		AppointmentType: message.Data.AppointmentType,
		Enabled:         message.Data.Enabled,
	}

	if message.Data.ID != nil {
		upsertInput.ID = message.Data.ID
	}

	// Execute the upsert operation
	ctxWithLogger := vlog.AttachLoggerToContext(ctx, logger)
	_, err := p.upsertUsecase.Execute(ctxWithLogger, upsertInput)
	if err != nil {
		logger.Error("failed to process appointment type upsert from message", vlog.F("error", err))
		return fmt.Errorf("failed to process appointment type upsert from message: %w", err)
	}

	logger.Info("successfully processed appointment type upsert", vlog.F("appointment_type", message.Data.AppointmentType))
	return nil
}
