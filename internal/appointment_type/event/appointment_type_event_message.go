package event

import (
	"time"
)

// AppointmentTypeEventData represents the data payload for appointment type events
type AppointmentTypeEventData struct {
	ID              *string `json:"id,omitempty"`
	AppointmentType string  `json:"appointment_type"`
	Enabled         bool    `json:"enabled"`
}

// AppointmentTypeEventMessage represents the complete event message structure
type AppointmentTypeEventMessage struct {
	Data      AppointmentTypeEventData `json:"data"`
	Timestamp time.Time                `json:"timestamp"`
	Source    string                   `json:"source"`
	EventType string                   `json:"event_type"`
}
