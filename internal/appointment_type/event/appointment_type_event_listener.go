package event

import (
	"context"
	"fmt"

	"gitlab.viswalslab.com/backend/price-list/infra/config"
)

// AppointmentTypeEventSubscriber defines the interface for appointment type event subscription
type AppointmentTypeEventSubscriber interface {
	// Subscribe sets the processor for handling appointment type events
	Subscribe(ctx context.Context, processor interface {
		ProcessMessage(ctx context.Context, message *AppointmentTypeEventMessage) error
	}) error

	// Start begins listening for appointment type events
	Start(ctx context.Context) error

	// Stop stops the event listener
	Stop() error

	// IsRunning returns whether the subscriber is currently running
	IsRunning() bool
}

// AppointmentTypeEventListener manages the appointment type event subscription service
type AppointmentTypeEventListener struct {
	subscriber AppointmentTypeEventSubscriber
	cfg        config.Configuration
}

// NewAppointmentTypeEventListener creates a new appointment type event listener instance
func NewAppointmentTypeEventListener(cfg config.Configuration, upsertUsecase UpsertAppointmentTypeUsecase) (*AppointmentTypeEventListener, error) {
	// Validate that appointment type queue URL is configured
	if cfg.AWS.SQSQueueURLAppointmentType == "" {
		return nil, fmt.Errorf("AWS_SQS_QUEUE_URL_APPOINTMENT_TYPE is required for appointment type event operations")
	}

	// Initialize SQS event subscriber with appointment type queue URL
	subscriber, err := NewSQSAppointmentTypeEventSubscriber(cfg, cfg.AWS.SQSQueueURLAppointmentType)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize SQS event subscriber: %w", err)
	}

	// Create event processor
	processor := NewAppointmentTypeUpsertEventProcessor(upsertUsecase)

	// Subscribe the processor to handle events
	if err := subscriber.Subscribe(context.Background(), processor); err != nil {
		return nil, fmt.Errorf("failed to subscribe processor: %w", err)
	}

	return &AppointmentTypeEventListener{
		subscriber: subscriber,
		cfg:        cfg,
	}, nil
}

// Start starts the appointment type event listener
func (l *AppointmentTypeEventListener) Start(ctx context.Context) error {
	return l.subscriber.Start(ctx)
}

// Stop stops the appointment type event listener
func (l *AppointmentTypeEventListener) Stop() error {
	return l.subscriber.Stop()
}

// IsRunning returns whether the appointment type event listener is currently running
func (l *AppointmentTypeEventListener) IsRunning() bool {
	return l.subscriber.IsRunning()
}
