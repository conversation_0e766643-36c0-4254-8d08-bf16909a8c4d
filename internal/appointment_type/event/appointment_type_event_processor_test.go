package event_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/event"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
)

// MockUpsertAppointmentTypeUsecase is a mock implementation of the UpsertAppointmentTypeUsecase interface
type MockUpsertAppointmentTypeUsecase struct {
	mock.Mock
}

func (m *MockUpsertAppointmentTypeUsecase) Execute(ctx context.Context, input *usecase.UpsertAppointmentTypeInput) (*usecase.UpsertAppointmentTypeOutput, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*usecase.UpsertAppointmentTypeOutput), args.Error(1)
}

func TestNewAppointmentTypeUpsertEventProcessor(t *testing.T) {
	mockUsecase := new(MockUpsertAppointmentTypeUsecase)
	processor := event.NewAppointmentTypeUpsertEventProcessor(mockUsecase)

	assert.NotNil(t, processor)
	assert.IsType(t, &event.AppointmentTypeUpsertEventProcessor{}, processor)
}

func TestAppointmentTypeUpsertEventProcessor_ProcessMessage(t *testing.T) {
	validID := core.NewID()

	tests := []struct {
		name           string
		message        *event.AppointmentTypeEventMessage
		setupMock      func(*MockUpsertAppointmentTypeUsecase)
		wantErr        bool
		wantErrMessage string
	}{
		{
			name: "Successful processing with ID",
			message: &event.AppointmentTypeEventMessage{
				Data: event.AppointmentTypeEventData{
					ID:              core.ToPtr(validID.Value()),
					AppointmentType: "Consultation",
					Enabled:         true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "appointment-type.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAppointmentTypeUsecase) {
				expectedInput := &usecase.UpsertAppointmentTypeInput{
					ID:              core.ToPtr(validID.Value()),
					AppointmentType: "Consultation",
					Enabled:         true,
				}
				expectedOutput := &usecase.UpsertAppointmentTypeOutput{
					ID:              validID.Value(),
					AppointmentType: "Consultation",
					Enabled:         true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name: "Successful processing without ID",
			message: &event.AppointmentTypeEventMessage{
				Data: event.AppointmentTypeEventData{
					AppointmentType: "Follow-up",
					Enabled:         true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "appointment-type.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAppointmentTypeUsecase) {
				expectedInput := &usecase.UpsertAppointmentTypeInput{
					ID:              nil,
					AppointmentType: "Follow-up",
					Enabled:         true,
				}
				expectedOutput := &usecase.UpsertAppointmentTypeOutput{
					ID:              "new-generated-id",
					AppointmentType: "Follow-up",
					Enabled:         true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
		{
			name:           "Nil message",
			message:        nil,
			setupMock:      func(mockUsecase *MockUpsertAppointmentTypeUsecase) {},
			wantErr:        true,
			wantErrMessage: "message is required",
		},
		{
			name: "Empty appointment type name",
			message: &event.AppointmentTypeEventMessage{
				Data: event.AppointmentTypeEventData{
					AppointmentType: "",
					Enabled:         true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "appointment-type.upsert",
			},
			setupMock:      func(mockUsecase *MockUpsertAppointmentTypeUsecase) {},
			wantErr:        true,
			wantErrMessage: "appointment type name is required in message data",
		},
		{
			name: "Invalid UUID format",
			message: &event.AppointmentTypeEventMessage{
				Data: event.AppointmentTypeEventData{
					ID:              core.ToPtr("invalid-uuid"),
					AppointmentType: "Consultation",
					Enabled:         true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "appointment-type.upsert",
			},
			setupMock:      func(mockUsecase *MockUpsertAppointmentTypeUsecase) {},
			wantErr:        true,
			wantErrMessage: "invalid UUID format in message data",
		},
		{
			name: "Usecase execution error",
			message: &event.AppointmentTypeEventMessage{
				Data: event.AppointmentTypeEventData{
					AppointmentType: "Emergency",
					Enabled:         true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "appointment-type.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAppointmentTypeUsecase) {
				expectedInput := &usecase.UpsertAppointmentTypeInput{
					ID:              nil,
					AppointmentType: "Emergency",
					Enabled:         true,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil, errors.New("database error"))
			},
			wantErr:        true,
			wantErrMessage: "failed to process appointment type upsert from message",
		},
		{
			name: "Business error from usecase",
			message: &event.AppointmentTypeEventMessage{
				Data: event.AppointmentTypeEventData{
					AppointmentType: "duplicate-name",
					Enabled:         true,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "appointment-type.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAppointmentTypeUsecase) {
				expectedInput := &usecase.UpsertAppointmentTypeInput{
					ID:              nil,
					AppointmentType: "duplicate-name",
					Enabled:         true,
				}
				businessErr := core.NewBusinessError("Appointment type name 'duplicate-name' already exists")
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(nil, businessErr)
			},
			wantErr:        true,
			wantErrMessage: "failed to process appointment type upsert from message",
		},
		{
			name: "Successful processing with disabled status",
			message: &event.AppointmentTypeEventMessage{
				Data: event.AppointmentTypeEventData{
					ID:              core.ToPtr(validID.Value()),
					AppointmentType: "Cancelled",
					Enabled:         false,
				},
				Timestamp: time.Now(),
				Source:    "test-source",
				EventType: "appointment-type.upsert",
			},
			setupMock: func(mockUsecase *MockUpsertAppointmentTypeUsecase) {
				expectedInput := &usecase.UpsertAppointmentTypeInput{
					ID:              core.ToPtr(validID.Value()),
					AppointmentType: "Cancelled",
					Enabled:         false,
				}
				expectedOutput := &usecase.UpsertAppointmentTypeOutput{
					ID:              validID.Value(),
					AppointmentType: "Cancelled",
					Enabled:         false,
				}
				mockUsecase.On("Execute", mock.Anything, expectedInput).Return(expectedOutput, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockUsecase := new(MockUpsertAppointmentTypeUsecase)
			tt.setupMock(mockUsecase)

			processor := event.NewAppointmentTypeUpsertEventProcessor(mockUsecase)
			err := processor.ProcessMessage(context.Background(), tt.message)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.wantErrMessage != "" {
					assert.Contains(t, err.Error(), tt.wantErrMessage)
				}
			} else {
				assert.NoError(t, err)
			}

			mockUsecase.AssertExpectations(t)
		})
	}
}
