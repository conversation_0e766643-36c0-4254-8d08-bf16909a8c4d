package transport

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type MockAppointmentTypeUsecase struct {
	mock.Mock
}

func (m *MockAppointmentTypeUsecase) GetAppointmentType(ctx context.Context, id string) (*AppointmentTypeResponse, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AppointmentTypeResponse), args.Error(1)
}

func (m *MockAppointmentTypeUsecase) ListAppointmentTypes(ctx context.Context, input ListAppointmentTypesRequest) ([]AppointmentTypeResponse, *ListAppointmentTypesOutputPagination, error) {
	args := m.Called(ctx, input)
	return args.Get(0).([]AppointmentTypeResponse), args.Get(1).(*ListAppointmentTypesOutputPagination), args.Error(2)
}

func TestAppointmentTypeHandler_GetAppointmentType(t *testing.T) {
	e := echo.New()

	t.Run("successful get appointment type", func(t *testing.T) {
		mockUsecase := new(MockAppointmentTypeUsecase)
		handler := &AppointmentTypeHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		atID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		expectedAT := &AppointmentTypeResponse{
			UUID: atID, AppointmentType: "Consultation", Enabled: true,
		}

		mockUsecase.On("GetAppointmentType", mock.Anything, atID).Return(expectedAT, nil)

		req := httptest.NewRequest(http.MethodGet, "/appointment-types/"+atID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("appointmentTypeId")
		c.SetParamValues(atID)

		err := handler.GetAppointmentType(c)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var response GetAppointmentTypeResponse
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		assert.True(t, response.Status)
		assert.Equal(t, expectedAT, response.Data)
		mockUsecase.AssertExpectations(t)
	})

	t.Run("missing appointmentTypeId param", func(t *testing.T) {
		mockUsecase := new(MockAppointmentTypeUsecase)
		handler := &AppointmentTypeHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}

		req := httptest.NewRequest(http.MethodGet, "/appointment-types/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.GetAppointmentType(c)
		require.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "appointmentTypeId is required")
	})

	t.Run("invalid UUID format", func(t *testing.T) {
		mockUsecase := new(MockAppointmentTypeUsecase)
		handler := &AppointmentTypeHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}
		invalidID := "invalid-uuid"
		req := httptest.NewRequest(http.MethodGet, "/appointment-types/"+invalidID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("appointmentTypeId")
		c.SetParamValues(invalidID)

		err := handler.GetAppointmentType(c)
		require.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusBadRequest, httpErr.Code)
		assert.Contains(t, httpErr.Message, "Invalid appointmentTypeId format")
	})

	t.Run("appointment type not found", func(t *testing.T) {
		mockUsecase := new(MockAppointmentTypeUsecase)
		handler := &AppointmentTypeHandler{Usecase: mockUsecase, Logger: vlog.NewWithLevel("error")}
		atID := "84ce4d70-a442-412b-bf27-06f4544a8661"
		mockUsecase.On("GetAppointmentType", mock.Anything, atID).Return(nil, assert.AnError)

		req := httptest.NewRequest(http.MethodGet, "/appointment-types/"+atID, nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("appointmentTypeId")
		c.SetParamValues(atID)

		err := handler.GetAppointmentType(c)
		require.Error(t, err)
		httpErr, ok := err.(*echo.HTTPError)
		require.True(t, ok)
		assert.Equal(t, http.StatusNotFound, httpErr.Code)
	})
}
