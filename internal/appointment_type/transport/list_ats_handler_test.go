package transport

import (
	"context"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

type mockAppointmentTypeUsecase struct{}

func (m *mockAppointmentTypeUsecase) ListAppointmentTypes(ctx context.Context, input ListAppointmentTypesRequest) ([]AppointmentTypeResponse, *ListAppointmentTypesOutputPagination, error) {
	return []AppointmentTypeResponse{}, &ListAppointmentTypesOutputPagination{CurrentPage: 0, Total: 0}, nil
}
func (m *mockAppointmentTypeUsecase) GetAppointmentType(ctx context.Context, id string) (*AppointmentTypeResponse, error) {
	return nil, nil
}

func TestAppointmentTypeHandler_ListAppointmentTypes_Split(t *testing.T) {
	e := echo.New()
	req := httptest.NewRequest("GET", "/appointment-types", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	h := &AppointmentTypeHandler{Usecase: &mockAppointmentTypeUsecase{}}
	err := h.ListAppointmentTypes(c)
	assert.NoError(t, err)
	assert.Equal(t, 200, rec.Code)
}
