package transport

import (
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

func (h *AppointmentTypeHandler) ListAppointmentTypes(c echo.Context) error {
	page, err := strconv.Atoi(c.Que<PERSON>("page"))
	if err != nil || page < 0 {
		page = 0
	}
	size, err := strconv.Atoi(c.Query<PERSON>aram("size"))
	if err != nil || size <= 0 {
		size = 10
	}
	if size > 100 {
		size = 100
	}

	enabledStr := c.QueryParam("enabled")
	var enabled *bool
	if enabledStr != "" {
		val, err := strconv.ParseBool(enabledStr)
		if err == nil {
			enabled = &val
		}
	}

	input := ListAppointmentTypesRequest{
		Enabled:   enabled,
	}

	appointmentTypes, pagination, err := h.Usecase.ListAppointmentTypes(c.Request().Context(), input)
	if err != nil {
		h.Logger.Error("ListAppointmentTypesHandler: failed to list appointment types", vlog.F("error", err))
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error())
	}
	return c.JSON(http.StatusOK, ListAppointmentTypesOutput{
		Status:     true,
		Message:    "",
		Data:       appointmentTypes,
		Pagination: pagination,
	})
}
