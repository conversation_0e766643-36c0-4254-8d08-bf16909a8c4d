package transport

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
)

func TestToAppointmentTypeResponse(t *testing.T) {
	id, _ := core.NewIDFromString("84ce4d70-a442-412b-bf27-06f4544a8661")
	apptType, err := entity.NewAppointmentType(&entity.NewAppointmentTypeInput{
		ID:              id.Value(),
		AppointmentType: "Consultation",
		Enabled:         true,
	})
	assert.NoError(t, err)

	resp := ToAppointmentTypeResponse(*apptType)
	assert.Equal(t, "84ce4d70-a442-412b-bf27-06f4544a8661", resp.UUID)
	assert.Equal(t, "Consultation", resp.AppointmentType)
	assert.True(t, resp.Enabled)
}
