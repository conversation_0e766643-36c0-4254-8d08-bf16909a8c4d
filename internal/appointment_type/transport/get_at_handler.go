package transport

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.viswalslab.com/backend/price-list/core"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

// GetAppointmentType handles GET /appointment-types/:appointmentTypeId
func (h *AppointmentTypeHandler) GetAppointmentType(c echo.Context) error {
	appointmentTypeId := c.Param("appointmentTypeId")
	if appointmentTypeId == "" {
		h.Logger.Error("GetAppointmentTypeHandler: appointmentTypeId is required", vlog.F("error", "missing appointmentTypeId"))
		return echo.NewHTTPError(http.StatusBadRequest, "appointmentTypeId is required")
	}

	if _, err := core.NewIDFromString(appointmentTypeId); err != nil {
		h.Logger.Error("GetAppointmentTypeHandler: invalid appointmentTypeId format", vlog.F("error", err), vlog.F("appointmentTypeId", appointmentTypeId))
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid appointmentTypeId format")
	}
	appointmentType, err := h.Usecase.GetAppointmentType(c.Request().Context(), appointmentTypeId)
	if err != nil {
		h.Logger.Error("GetAppointmentTypeHandler: appointmentType not found", vlog.F("error", err), vlog.F("appointmentTypeId", appointmentTypeId))
		return echo.NewHTTPError(http.StatusNotFound, err.Error())
	}
	return c.JSON(http.StatusOK, GetAppointmentTypeResponse{
		Status:  true,
		Message: "",
		Data:    appointmentType,
	})
}
