package transport

import (
	"context"

	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/entity"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type AppointmentTypeResponse struct {
	UUID            string `json:"uuid"`
	AppointmentType string `json:"appointment_type"`
	Enabled         bool   `json:"enabled"`
}

type ListAppointmentTypesOutput struct {
	Status     bool                                  `json:"status"`
	Message    string                                `json:"message"`
	Data       []AppointmentTypeResponse             `json:"data"`
	Pagination *ListAppointmentTypesOutputPagination `json:"pagination,omitempty"`
}

type GetAppointmentTypeResponse struct {
	Status  bool                     `json:"status"`
	Message string                   `json:"message"`
	Data    *AppointmentTypeResponse `json:"data"`
}

type ListAppointmentTypesOutputPagination struct {
	CurrentPage int `json:"current_page"`
	Total       int `json:"total"`
}

type ListAppointmentTypesRequest struct {
	Enabled         *bool
	AppointmentType *string
}

type AppointmentTypeUsecase interface {
	ListAppointmentTypes(ctx context.Context, input ListAppointmentTypesRequest) ([]AppointmentTypeResponse, *ListAppointmentTypesOutputPagination, error)
	GetAppointmentType(ctx context.Context, id string) (*AppointmentTypeResponse, error)
}

type AppointmentTypeHandler struct {
	Usecase AppointmentTypeUsecase
	Logger  vlog.Logger
}

func ToAppointmentTypeResponse(appointmentType entity.AppointmentType) AppointmentTypeResponse {
	return AppointmentTypeResponse{
		UUID:            appointmentType.ID(),
		AppointmentType: appointmentType.AppointmentType(),
		Enabled:         appointmentType.Enabled(),
	}
}
