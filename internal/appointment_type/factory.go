package appointment_type

import (
	"context"

	"github.com/jmoiron/sqlx"
	"gitlab.viswalslab.com/backend/price-list/infra/config"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/event"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/repository"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/service"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/transport"
	"gitlab.viswalslab.com/backend/price-list/internal/appointment_type/usecase"
	"gitlab.viswalslab.com/backend/standard/v2/vlog"
)

type Factory struct {
	AppointmentTypeEventListener *event.AppointmentTypeEventListener
}

// usecaseAdapter adapts the usecase to implement the event interface
type usecaseAdapter struct {
	upsertUsecase *usecase.UpsertAppointmentTypeUsecase
}

func (a *usecaseAdapter) Execute(ctx context.Context, input *usecase.UpsertAppointmentTypeInput) (*usecase.UpsertAppointmentTypeOutput, error) {
	return a.upsertUsecase.Execute(ctx, input)
}

func NewFactory(db *sqlx.DB, cfg config.Configuration) (*Factory, error) {
	// Initialize service layer
	validationService := service.NewAppointmentTypeValidationService()

	// Initialize repository for upsert operations
	appointmentTypeUpsertRepo := repository.NewAppointmentTypeUpsertRepository(db)

	// Initialize usecase
	upsertUsecase := usecase.NewUpsertAppointmentTypeUsecase(appointmentTypeUpsertRepo, validationService)

	// Create adapter for event interface
	adapter := &usecaseAdapter{upsertUsecase: upsertUsecase}

	// Initialize event listener
	eventListener, err := event.NewAppointmentTypeEventListener(cfg, adapter)
	if err != nil {
		return nil, err
	}

	return &Factory{
		AppointmentTypeEventListener: eventListener,
	}, nil
}

func NewAppointmentTypeHandlerFactory(db *sqlx.DB, logger vlog.Logger) *transport.AppointmentTypeHandler {
	repo := repository.NewAppointmentTypeRepository(db, logger)
	uc := usecase.NewAppointmentTypeUsecase(repo)
	return &transport.AppointmentTypeHandler{Usecase: uc}
}
